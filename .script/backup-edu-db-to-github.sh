#!/bin/bash
# 配置变量
DB_USER="root"
DB_PASS="Caho9:QoqwTafi"
DB_NAME="edu"
BACKUP_DIR="/opt/product/edu-db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/$DB_NAME-$DATE.sql"
ONE_GB=$((1024 * 1024 * 1024))  # 1GB的字节数
GITHUB_TOKEN="*********************************************************************************************"  # GitHub个人访问令牌
REPO_URL="https://${GITHUB_TOKEN}@github.com/RuneDance/edu-db.git"

# 进入备份目录
cd $BACKUP_DIR || { echo "无法进入备份目录"; exit 1; }

# 代理配置
export ALL_PROXY=socks5://127.0.0.1:10808
export HTTP_PROXY=socks5://127.0.0.1:10808
export HTTPS_PROXY=socks5://127.0.0.1:10808
export NO_PROXY=localhost,127.0.0.1

# 为Git配置代理（现在在正确的目录下）
git config http.proxy socks5://127.0.0.1:10808
git config https.proxy socks5://127.0.0.1:10808

# 配置Git pull策略
git config pull.rebase false

# 先从远程拉取最新更改并合并
echo "拉取远程仓库最新内容..."
git fetch $REPO_URL master
git reset --hard FETCH_HEAD

# 创建MySQL备份
echo "创建数据库备份..."
mysqldump -h 127.0.0.1 -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_FILE

# 检查备份是否成功
if [ $? -ne 0 ]; then
    echo "数据库备份失败"
    exit 1
fi

# 检查文件大小
FILE_SIZE=$(stat -c%s "$BACKUP_FILE")

# 如果备份文件大于1GB，才进行压缩
if [ "$FILE_SIZE" -gt "$ONE_GB" ]; then
    echo "文件大于1GB，进行压缩..."
    gzip "$BACKUP_FILE"
    BACKUP_FILE="$BACKUP_FILE.gz"
fi

# 添加到Git
git add "$BACKUP_FILE"

# 提交更改
git commit -m "Database backup - $DATE"

# 使用令牌推送到GitHub
echo "推送备份到远程仓库..."
git push $REPO_URL master

# 检查推送是否成功
if [ $? -eq 0 ]; then
    echo "备份已成功推送到GitHub"

    # 删除本地备份文件，但不提交此删除操作
    if [ -f "$BACKUP_FILE" ]; then
        rm "$BACKUP_FILE"
        echo "已删除本地备份文件: $BACKUP_FILE"
    fi

    # 如果是压缩文件，检查是否还有原始SQL文件并删除
    if [ -n "$(echo $BACKUP_FILE | grep '\.gz$')" ]; then
        ORIGINAL_SQL="${BACKUP_FILE%.gz}"
        if [ -f "$ORIGINAL_SQL" ]; then
            rm "$ORIGINAL_SQL"
            echo "已删除原始SQL文件: $ORIGINAL_SQL"
        fi
    fi

    # 不再提交删除操作，这样文件会保留在GitHub上
    # 移除以下行:
    # git add -A
    # git commit -m "Remove local backup after successful push - $DATE"
    # git push $REPO_URL master
else
    echo "推送到GitHub失败，保留本地备份文件"
fi