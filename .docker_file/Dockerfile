# 拉去JDK镜像
FROM eclipse-temurin:17.0.14_7-jre-alpine

# 安装等待工具
RUN apk add --no-cache bash

# 下载wait-for-it脚本
RUN wget https://raw.githubusercontent.com/vishnubob/wait-for-it/master/wait-for-it.sh -O /wait-for-it.sh \
    && chmod +x /wait-for-it.sh

# 创建非root用户
RUN addgroup -S spring && adduser -S spring -G spring

# 创建工作目录和日志目录并设置权限
WORKDIR /app
RUN mkdir -p /opt/product/edu/logs && \
    chown -R spring:spring /app && \
    chown -R spring:spring /opt/product/edu/logs && \
    chmod 777 /opt/product/edu/logs

# 复制jar文件
COPY --chown=spring:spring edu-1.0-SNAPSHOT.jar app.jar

USER spring

EXPOSE 8081

# 环境变量设置
ENV TZ=Asia/Shanghai \
    JAVA_OPTS="-Xms256m -Xmx256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"

# 使用wait-for-it等待依赖服务就绪
ENTRYPOINT ["/bin/sh", "-c", "/wait-for-it.sh mysql:3306 -t 60 -- /wait-for-it.sh redis:6379 -t 60 -- java ${JAVA_OPTS} -jar /app/app.jar"]
