services:
  web:
    build: .
    ports:
      - "8081:8081"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/opt/product/edu/logs
    user: "1000:1000"
    networks:
      - app-network
    environment:
      TZ: Asia/Shanghai
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: **************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: Caho9:QoqwTafi
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: Giwo3*LogxDafa

  mysql:
    image: mysql:8.0.33
    command: --default-authentication-plugin=mysql_native_password
    ports:
      # 添加这行，把容器的3306端口映射到宿主机的3306端口
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: Caho9:QoqwTafi
      MYSQL_DATABASE: edu
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pCaho9:QoqwTafi"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:6.2.12
    command: redis-server --requirepass Giwo3*LogxDafa
    ports:
      # 添加这行，映射Redis端口到宿主机
      - "6379:6379"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "Giwo3*LogxDafa", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  mysql_data:

networks:
  app-network:
    name: app-network
    driver: bridge