#!/bin/bash

# 定义变量
PROJECT_PATH="/Users/<USER>/Developer/IdeaProject/edu"
JAR_PATH="$PROJECT_PATH/target/edu-1.0-SNAPSHOT.jar"
SERVER_USER="yyt"
# SERVER_IP="*************"
SERVER_IP="*************"
SERVER_DEST_PATH="/home/<USER>/下载"
SERVER_PASSWORD="yuyongtao"

# 第一步：进入项目目录并执行Maven命令
echo "步骤1: 进入项目目录 $PROJECT_PATH"
cd "$PROJECT_PATH" || {
    echo "错误: 无法进入项目目录 $PROJECT_PATH"
    exit 1
}

echo "步骤1(续): 执行 mvn clean 命令..."
mvn clean
if [ $? -ne 0 ]; then
    echo "错误: mvn clean 命令执行失败，脚本终止"
    exit 1
fi

echo "步骤1(续): 执行 mvn clean package -P prod -DskipTests 命令..."
mvn clean package -P prod -DskipTests
if [ $? -ne 0 ]; then
    echo "错误: mvn clean package 命令执行失败，脚本终止"
    exit 1
fi

# 第二步：检查JAR文件是否生成并上传到服务器
echo "步骤2: 检查JAR文件是否生成 $JAR_PATH"
if [ -f "$JAR_PATH" ]; then
    echo "JAR文件已成功生成，开始上传到服务器..."

    # 使用sshpass自动输入密码（需要先安装：brew install sshpass）
    # 如果没有sshpass，可以使用传统的scp方式（需要手动输入密码）
    if command -v sshpass &> /dev/null; then
        echo "使用sshpass上传文件..."
        sshpass -p "$SERVER_PASSWORD" scp "$JAR_PATH" "$SERVER_USER@$SERVER_IP:$SERVER_DEST_PATH"
    else
        echo "未检测到sshpass，将使用传统scp方式，需要手动输入密码..."
        echo "正在上传JAR文件到服务器，密码是: $SERVER_PASSWORD"
        scp "$JAR_PATH" "$SERVER_USER@$SERVER_IP:$SERVER_DEST_PATH"
    fi

    # 检查上传结果
    if [ $? -eq 0 ]; then
        echo "步骤3: JAR文件上传成功，部署完成！"
    else
        echo "步骤3: JAR文件上传失败！"
        exit 1
    fi
else
    echo "错误: JAR文件未生成，构建可能失败"
    exit 1
fi

echo "所有步骤已完成"