#!/bin/bash

# 定义变量
DOWNLOAD_JAR_PATH="/home/<USER>/下载/edu-1.0-SNAPSHOT.jar"
TARGET_PATH="/opt/product/edu"
TARGET_JAR_PATH="$TARGET_PATH/edu-1.0-SNAPSHOT.jar"

# 检查下载目录中的JAR文件是否存在
echo "检查 $DOWNLOAD_JAR_PATH 是否存在..."
if [ ! -f "$DOWNLOAD_JAR_PATH" ]; then
    echo "错误: $DOWNLOAD_JAR_PATH 文件不存在，程序结束。"
    exit 1
fi

echo "$DOWNLOAD_JAR_PATH 文件存在，继续执行部署..."

# 进入目标目录
echo "进入目录 $TARGET_PATH..."
cd "$TARGET_PATH" || {
    echo "错误: 无法进入目录 $TARGET_PATH，程序结束。"
    exit 1
}

# 停止当前运行的容器
echo "执行 docker-compose down..."
docker-compose down
if [ $? -ne 0 ]; then
    echo "警告: docker-compose down 命令执行出错，但将继续执行脚本..."
fi

# 如果目标JAR文件存在，将其备份
if [ -f "$TARGET_JAR_PATH" ]; then
    # 生成当前日期时间字符串，格式：YYYYMMDD_HHMMSS
    CURRENT_DATETIME=$(date +"%Y%m%d_%H%M%S")
    BACKUP_JAR_NAME="edu-1.0-SNAPSHOT_${CURRENT_DATETIME}.jar"

    echo "备份当前JAR文件为 $BACKUP_JAR_NAME..."
    mv "$TARGET_JAR_PATH" "$TARGET_PATH/$BACKUP_JAR_NAME"

    if [ $? -ne 0 ]; then
        echo "错误: 无法重命名JAR文件，程序结束。"
        exit 1
    fi
fi

# 移动新的JAR文件到目标位置
echo "移动 $DOWNLOAD_JAR_PATH 到 $TARGET_PATH..."
mv "$DOWNLOAD_JAR_PATH" "$TARGET_PATH/"
if [ $? -ne 0 ]; then
    echo "错误: 无法移动JAR文件，程序结束。"
    exit 1
fi

# 重新启动容器
echo "执行 docker-compose up -d --build..."
docker-compose up -d --build
if [ $? -ne 0 ]; then
    echo "错误: docker-compose up -d --build 命令执行失败，程序结束。"
    exit 1
fi

echo "部署完成! 新的JAR文件已成功移动并且容器已重启。"