CREATE TABLE t_edu_student
(
    ID                 VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    NAME_ZH            VARCHAR(255) COMMENT '中文名',
    NAME_EN            VARCHAR(255) COMMENT '英文名',
    STUDENT_CODE       VARCHAR(255) COMMENT '学生编号',
    GENDER             TINYINT COMMENT '性别(0：女、1：男)',
    BIRTH_DATE         DATE COMMENT '出生日期(真实)',
    DEPARTMENT_NAME    VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE    VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    CLASS_ID           VARCHAR(255) COMMENT '班级ID',
    ADMISSION_DATE     DATE COMMENT '入校时间',
    CARD_TYPE          VARCHAR(155) COMMENT '证件类型',
    CARD_NUM           VARCHAR(255) COMMENT '证件号码',
    CARD_NAME_ZH       VARCHAR(255) COMMENT '证件中文名',
    CARD_NAME_EN       VARCHAR(255) COMMENT '证件英文名',
    PHONE_NUM          VARCHAR(255) COMMENT '手机号',

    IS_PAID            VARCHAR(20) COMMENT '是否缴费(0：否、1：是)',
    STATUS             VARCHAR(20) COMMENT '是否缴费(0:退学、1:旁听、2:在读)',
    IS_EXAM            VARCHAR(20) COMMENT '是否参加考试(0：否、1：是)',
    IS_TRANSFERRED     VARCHAR(20) COMMENT '是否为转入生(0：否、1：是)',
    TRANSFER_IN_DATE   TIMESTAMP COMMENT '转入时间',

    MIDDLE_SCHOOL_NAME VARCHAR(255) COMMENT '初中就读学校名称',
    IS_BOARDING        VARCHAR(20) COMMENT '是否缴费(0：否、1：是)',
    EMAIL              VARCHAR(255) COMMENT '邮箱',
    PARENT_EMAIL       VARCHAR(255) COMMENT '家长邮箱',
    CONTACT_INFO       TEXT COMMENT '联系人信息',
    DESCRIPTION        VARCHAR(255) COMMENT '描述',

    ETHNICITY          VARCHAR(60) COMMENT '民族',
    HEIGHT             DECIMAL(5, 1) COMMENT '身高',
    WEIGHT             DECIMAL(5, 1) COMMENT '体重',
    BLOOD_TYPE         VARCHAR(10) COMMENT '血型',
    BIRTH_PLACE        VARCHAR(255) COMMENT '籍贯',
    CLOTHING_SIZE      VARCHAR(60) COMMENT '衣服尺码',
    SIZE_SITUATION     VARCHAR(255) COMMENT '尺码情况',
    BIRTH_ADDRESS      VARCHAR(255) COMMENT '出生地址',
    HOUSEHOLD_ADDRESS  VARCHAR(255) COMMENT '户籍地址',
    HOME_ADDRESS       VARCHAR(255) COMMENT '家庭住址',
    IS_ONLY_CHILD      VARCHAR(20) COMMENT '是否是独生子女(0：否、1：是)',
    SITUATION          VARCHAR(255) COMMENT '情况',

    CREATED_AT         TIMESTAMP COMMENT '创建时间',
    CREATED_BY         VARCHAR(255) COMMENT '创建人',
    UPDATED_AT         TIMESTAMP COMMENT '更新时间',
    UPDATED_BY         VARCHAR(255) COMMENT '更新人'
);