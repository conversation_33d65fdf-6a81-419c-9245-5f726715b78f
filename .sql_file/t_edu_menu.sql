CREATE TABLE t_edu_menu
-- 菜单表
(
    ID             VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    PARENT_ID      VARCHAR(255) COMMENT '父级菜单ID',

    NAME           VARCHAR(60) COMMENT '菜单名称',     -- 显示在导航栏上的名称
    NAME_EN        VARCHAR(60) COMMENT '菜单英文名称', -- 国际化
    PATH           VARCHAR(255) COMMENT '路由路径',    -- 前端路由地址
    COMPONENT      VARCHAR(255) COMMENT '组件路径',    -- 前端组件路径
    META           TEXT COMMENT '元数据',              -- 菜单项相关的其他元数据
    ICON           VARCHAR(60) COMMENT '图标',         -- 菜单图标
    DIRECTORY_SORT INT COMMENT '目录菜单序号',         -- 用于控制菜单的显示顺序
    PAGE_SORT      INT COMMENT '页面菜单序号',         -- 用于控制菜单的显示顺序
    TYPE           VARCHAR(20) COMMENT '菜单类型(0: 目录菜单, 1: 页面菜单)',
    IS_HIDDEN      VARCHAR(20) COMMENT '是否隐藏(0: 隐藏, 1: 显示)',
    STATUS         VARCHAR(20) COMMENT '状态(0：禁用、1：启用)',

    CREATED_AT     TIMESTAMP COMMENT '创建时间',
    CREATED_BY     VARCHAR(255) COMMENT '创建人',
    UPDATED_AT     TIMESTAMP COMMENT '更新时间',
    UPDATED_BY     VARCHAR(255) COMMENT '更新人'
);