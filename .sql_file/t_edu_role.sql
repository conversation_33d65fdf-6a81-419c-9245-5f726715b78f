CREATE TABLE t_edu_role
-- 角色表
(
    ID          VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    NAME        VARCHAR(50) COMMENT '角色名称',
    CODE        VARCHAR(50) COMMENT '角色编码',
    STATUS      VARCHAR(20) COMMENT '状态(0：禁用、1：启用)',
    DESCRIPTION VARCHAR(255) COMMENT '角色描述',

    CREATED_AT  TIMESTAMP COMMENT '创建时间',
    CREATED_BY  VARCHAR(255) COMMENT '创建人',
    UPDATED_AT  TIMESTAMP COMMENT '更新时间',
    UPDATED_BY  VARCHAR(255) COMMENT '更新人'
);