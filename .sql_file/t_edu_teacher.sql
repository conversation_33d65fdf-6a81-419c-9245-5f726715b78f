CREATE TABLE t_edu_teacher
(
    ID                      VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    NAME_ZH                 VARCHAR(255) COMMENT '中文名',
    NAME_EN                 VARCHAR(255) COMMENT '英文名',
    GENDER                  VARCHAR(20) COMMENT '性别(0：女、1：男)',
    DEPARTMENT              VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE         VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    EMP_NO                  VARCHAR(255) COMMENT '工号',
    STATUS                  VARCHAR(20) COMMENT '状态(0：离职、1：在职、2：出差、3：请假、4：休假)',
    TYPE                    VARCHAR(20) COMMENT '类型(0：外教、1：中教)',

    IS_SUBJECT_GROUP_LEADER VARCHAR(20) COMMENT '是否为学科组组长(0：否、1：是)',
    SUBJECT_GROUP_CODE      VARCHAR(255) COMMENT '学科组编号',
    OFFICE_CODE             VARCHAR(255) COMMENT '办公室编号',

    DESCRIPTION             VARCHAR(255) COMMENT '描述',

    CREATED_AT              TIMESTAMP COMMENT '创建时间',
    CREATED_BY              VARCHAR(255) COMMENT '创建人',
    UPDATED_AT              TIMESTAMP COMMENT '更新时间',
    UPDATED_BY              VARCHAR(255) COMMENT '更新人'
);