CREATE TABLE t_edu_purchase_order_detail
-- 采购明细表(采购管理子表)表
(
    ID                VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    PURCHASE_ID       VARCHAR(255) COMMENT '采购ID',
    BOOK_NAME         VARCHAR(255) COMMENT '书名',
    ISBN              VARCHAR(255) COMMENT 'ISBN编号',
    AUTHOR            VARCHAR(255) COMMENT '作者',
    PUBLISHER         VARCHAR(255) COMMENT '出版社',
    PUBLICATION_DATE  DATE COMMENT '出版日期',
    UNIT_PRICE        DECIMAL(10, 2) COMMENT '单价',
    QUANTITY          INT DEFAULT 0 COMMENT '数量',
    TOTAL_PRICE       DECIMAL(10, 2) COMMENT '小计',
    BOOK_TYPE         VARCHAR(20) COMMENT '书籍类型(1:中文教材、2:外文教材)',
    SUBJECT_CODE      VARCHAR(255) COMMENT '学科编号',
    G<PERSON><PERSON>_LEVEL       VARCHAR(90) COMMENT '年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)',
    URGENT_LEVEL      VARCHAR(20) COMMENT '部门(1：普通、2：紧急、3：特急)',
    RECEIVED_QUANTITY INT DEFAULT 0 COMMENT '已收数量',
    QUALITY_STATUS    VARCHAR(20) COMMENT '部门(0：不合格、1：待检查、2：合格)',

    DESCRIPTION       VARCHAR(255) COMMENT '备注描述',
    CREATED_AT        TIMESTAMP COMMENT '创建时间',
    CREATED_BY        VARCHAR(255) COMMENT '创建人',
    UPDATED_AT        TIMESTAMP COMMENT '更新时间',
    UPDATED_BY        VARCHAR(255) COMMENT '更新人'
);