CREATE TABLE t_edu_duty
-- 值日表
(
    ID              VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    DEPARTMENT      VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    YEAR            VARCHAR(255) COMMENT '年度',
    SEMESTER        VARCHAR(20) COMMENT '学期(S1：第一学期、S2：第二学期)',
    SEQ_WEEK        VARCHAR(20) COMMENT '周数',
    WEEKDAY         VARCHAR(20) COMMENT '星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)',
    DUTY_DATE       DATE COMMENT '值日日期',
    DUTY_TYPE       VARCHAR(35) COMMENT '值日类型(0：值日、1：Year10A+B、2：Year11A+B、3：Year12+春季)',

    TEACHER_NAME    VARCHAR(255) COMMENT '值日教师姓名',
    TEACHER_ID      VARCHAR(255) COMMENT '值日教师ID',

    COLOR           VARCHAR(255) COMMENT '置换颜色',
    IS_BASE_GROUP   VARCHAR(20) COMMENT '是否是基组(0：否、1：是，默认否)' DEFAULT '0',
    BASE_GROUP_CODE VARCHAR(20) COMMENT '基组编码(A：A组、B：B组)',
    DESCRIPTION     VARCHAR(255) COMMENT '描述',

    CREATED_AT      TIMESTAMP COMMENT '创建时间',
    CREATED_BY      VARCHAR(255) COMMENT '创建人',
    UPDATED_AT      TIMESTAMP COMMENT '更新时间',
    UPDATED_BY      VARCHAR(255) COMMENT '更新人'
);