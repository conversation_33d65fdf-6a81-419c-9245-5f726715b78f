CREATE TABLE t_edu_inbound_record
-- 入库记录表
(
    ID                   VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    PURCHASE_ID          VARCHAR(255) COMMENT '采购ID',
    BOOK_ID              VARCHAR(255) COMMENT '书籍ID',
    INBOUND_QUANTITY     INT DEFAULT 0 COMMENT '入库数量',
    INBOUND_DATE         DATE COMMENT '入库日期',
    QUALITY_CHECK_RESULT VARCHAR(20) COMMENT '质量检查结果(0：不合格、1：合格、2：待检查)',
    QUALITY_CHECK_NAME   VARCHAR(255) COMMENT '质量检查人姓名',
    QUALITY_CHECK_DATE   DATE COMMENT '质量检查日期',
    STORAGE_LOCATION     VARCHAR(255) COMMENT '存放位置',
    INBOUND_NAME         VARCHAR(255) COMMENT '入库人姓名',
    BATCH_NO             VARCHAR(255) COMMENT '批次号',
    INBOUND_TYPE         VARCHAR(20) COMMENT '入库类型(1：采购入库、2：调拨入库、3：退回入库)',

    DESCRIPTION          VARCHAR(255) COMMENT '备注描述',
    CREATED_AT           TIMESTAMP COMMENT '创建时间',
    CREATED_BY           VARCHAR(255) COMMENT '创建人',
    UPDATED_AT           TIMESTAMP COMMENT '更新时间',
    UPDATED_BY           VARCHAR(255) COMMENT '更新人'
);