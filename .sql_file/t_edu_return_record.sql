CREATE TABLE t_edu_return_record
-- 归还记录表
(
    ID                 VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    BORROW_ID          VARCHAR(255) COMMENT '领用ID',
    RETURN_QUANTITY    INT DEFAULT 0 COMMENT '归还数量',
    RETURN_DATE        DATE COMMENT '归还日期',
    RETURN_CONDITION   VARCHAR(20) COMMENT '归还状况(1：完好、2：轻微损坏、3:严重损坏)',
    QUALITY_CHECK_NAME VARCHAR(255) COMMENT '质检人姓名',
    DAMAGE_DESCRIPTION VARCHAR(255) COMMENT '损坏描述',
    PENALTY_AMOUNT     DECIMAL(10, 2) COMMENT '罚款金额',
    RETURN_NAME        VARCHAR(255) COMMENT '归还人姓名',
    RECEIVED_NAME      VARCHAR(255) COMMENT '接收人姓名',

    DESCRIPTION        VARCHAR(255) COMMENT '备注描述',
    CREATED_AT         TIMESTAMP COMMENT '创建时间',
    CREATED_BY         VARCHAR(255) COMMENT '创建人',
    UPDATED_AT         TIMESTAMP COMMENT '更新时间',
    UPDATED_BY         VARCHAR(255) COMMENT '更新人'
);