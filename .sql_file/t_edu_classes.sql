CREATE TABLE t_edu_classes
(
    ID                   VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    DEPARTMENT           VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE      VARCHAR(20) COMMENT '部门编号(SY：双语部、IC：融合部、DP：高中部)',

    GRADE_NAME           VARCHAR(255) COMMENT '年级名称',
    GRADE_CODE           VARCHAR(20) COMMENT '年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)',

    NAME                 VARCHAR(255) COMMENT '班级名称',
    CODE                 VARCHAR(20) COMMENT '班级编号(A：A班、B：B班)',
    HEAD_TEACHER_ID      VARCHAR(255) COMMENT '正班主任ID',
    ASST_HEAD_TEACHER_ID VARCHAR(255) COMMENT '副班主任ID',

    CLASSROOM_CODE       VARCHAR(25) COMMENT '教室编号',

    DESCRIPTION          VARCHAR(255) COMMENT '描述',

    CREATED_AT           TIMESTAMP COMMENT '创建时间',
    CREATED_BY           VARCHAR(255) COMMENT '创建人',
    UPDATED_AT           TIMESTAMP COMMENT '更新时间',
    UPDATED_BY           VARCHAR(255) COMMENT '更新人'
);