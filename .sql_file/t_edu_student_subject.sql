CREATE TABLE t_edu_student_subject
(
    ID            VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    STUDENT_ID    VARCHAR(255) COMMENT '学生ID',

    SUBJECT       VARCHAR(255) COMMENT '所选学科',
    SUBJECT_CODE  VARCHAR(255) COMMENT '所选学科编号',
    SUBJECT_TYPE  VARCHAR(30) COMMENT '学科分类(AA：A&A、AI：A&I、LL：L&L、LP：L&P)',
    SUBJECT_LEVEL VARCHAR(255) COMMENT '学科层级(SL：SL、HL：HL)',


    DESCRIPTION   VARCHAR(255) COMMENT '描述',
    CREATED_AT    TIMESTAMP COMMENT '创建时间',
    CREATED_BY    VARCHAR(255) COMMENT '创建人',
    UPDATED_AT    TIMESTAMP COMMENT '更新时间',
    UPDATED_BY    VARCHAR(255) COMMENT '更新人'
);