CREATE TABLE t_edu_purchase_contract
-- 采购合同表
(
    ID                 VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    CONTRACT_NO        VARCHAR(255) COMMENT '合同编号',
    PURCHASE_ID        VARCHAR(255) COMMENT '采购ID',
    SUPPLIER_ID        VARCHAR(255) COMMENT '供应商ID',
    CONTRACT_TITLE     VARCHAR(255) COMMENT '合同标题',
    CONTRACT_AMOUNT    DECIMAL(10, 2) COMMENT '合同金额',
    PREPAYMENT_AMOUNT  DECIMAL(10, 2) COMMENT '预付款金额',
    PREPAYMENT_RATIO   DECIMAL(10, 2) COMMENT '预付款比例',
    CONTRACT_STATUS    VARCHAR(20) COMMENT '采购状态(0：已终止、1：草稿、2：待审批、3：审批中、4：已审批、5：已签订、6：已执行、7：已完成)',
    OA_PROCESS_ID      VARCHAR(255) COMMENT 'OA流程ID',
    CONTRACT_FILE_PATH VARCHAR(255) COMMENT '合同文件路径',
    SIGN_DATE          DATE COMMENT '签订日期',
    EFFECTIVE_DATE     DATE COMMENT '生效日期',
    EXPIRY_DATE        DATE COMMENT '到期日期',
    PAYMENT_TERMS      VARCHAR(255) COMMENT '付款条件',
    DELIVERY_TERMS     VARCHAR(255) COMMENT '交货条件',
    QUALITY_STANDARDS  VARCHAR(255) COMMENT '质量标准',

    DESCRIPTION        VARCHAR(255) COMMENT '备注描述',
    CREATED_AT         TIMESTAMP COMMENT '创建时间',
    CREATED_BY         VARCHAR(255) COMMENT '创建人',
    UPDATED_AT         TIMESTAMP COMMENT '更新时间',
    UPDATED_BY         VARCHAR(255) COMMENT '更新人'
);