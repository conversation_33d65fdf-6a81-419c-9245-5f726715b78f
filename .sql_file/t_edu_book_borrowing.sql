CREATE TABLE t_edu_book_borrowing
-- 书籍领用表
(
    ID                   VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    DEPARTMENT           VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE      VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    YEAR                 VARCHAR(255) COMMENT '年度',
    SEMESTER             VARCHAR(20) COMMENT '学期(S1：第一学期、S2：第二学期)',
    borrow_no            VARCHAR(255) COMMENT '领用单号',
    BOOK_ID              VARCHAR(255) COMMENT '书籍ID',
    BORROWER_NAME        VARCHAR(255) COMMENT '领用人姓名',
    BORROWER_TYPE        VARCHAR(255) COMMENT '领用人类型',
    BORROWER_ID          VARCHAR(255) COMMENT '领用人(教师/学生)ID',
    CONTACT_PHONE        VARCHAR(255) COMMENT '联系电话',
    BORROW_QUANTITY      INT DEFAULT 0 COMMENT '领用数量',
    BORROW_DATE          DATE COMMENT '领用日期',
    IS_NEED_RETURN       VARCHAR(20) COMMENT '是否需要归还(0：否、1：是)',
    EXPECTED_RETURN_DATE DATE COMMENT '预计归还日期',
    ACTUAL_RETURN_DATE   DATE COMMENT '实际归还日期',
    BORROW_PURPOSE       VARCHAR(20) COMMENT '领用用途(1：教学用、2：学习用、3：研究用、4：其他)',
    BORROW_REASON        VARCHAR(255) COMMENT '领用原因说明',
    BORROW_STATUS        VARCHAR(20) COMMENT '领用状态(0：遗失、1：已借出、2：已归还、3：逾期未还)',
    RETURN_CONDITION     VARCHAR(20) COMMENT '归还状况(0：遗失、1：完好、2：轻微损坏、3：严重损坏)',
    APPROVED_NAME        DATE COMMENT '审批人姓名',
    APPROVAL_DATE        DATE COMMENT '审批日期',

    DESCRIPTION          VARCHAR(255) COMMENT '备注描述',
    CREATED_AT           TIMESTAMP COMMENT '创建时间',
    CREATED_BY           VARCHAR(255) COMMENT '创建人',
    UPDATED_AT           TIMESTAMP COMMENT '更新时间',
    UPDATED_BY           VARCHAR(255) COMMENT '更新人'
);