CREATE TABLE t_edu_exam
-- 考试表
(
    ID               VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    YEAR             VARCHAR(255) COMMENT '年度',
    SEMESTER         VARCHAR(20) COMMENT '学期(S1：第一学期、S2：第二学期)',
    DEPARTMENT_CODE  VARCHAR(20) COMMENT '部门编号(SY：双语部、IC：融合部、DP：高中部)',
    TYPE             VARCHAR(20) COMMENT '考试类型(MONTHLY: 月考、MID_TERM: 期中、MOCK: 模考、IB: IB大考、FINAL_TERM: 期末)',
    TITLE            VARCHAR(255) COMMENT '考试标题',
    GRADE_CODE       VARCHAR(20) COMMENT '年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)',
    CLASS_CODE       VARCHAR(20) COMMENT '班级编号(A：A班、B：B班)',
    CLASSROOM_CODE   VARCHAR(255) COMMENT '教室编号',

    COMPOSITION_INFO TEXT COMMENT '组成信息',
    SEQ_DAY          INT COMMENT '天数',
    EXAM_DATE        DATE COMMENT '考试日期',
    WEEKDAY          VARCHAR(20) COMMENT '星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)',
    TOTAL_DURATION   INT COMMENT '总时长',

    INVIGILATOR      VARCHAR(255) COMMENT '监考人',
    INSPECTOR        TEXT COMMENT '巡考人',
    EXAM_CANDIDATE   INT COMMENT '考生人数',

    STATUS           VARCHAR(20) COMMENT '状态(0：禁用、1：启用)',

    DESCRIPTION      VARCHAR(255) COMMENT '备注',

    CREATED_AT       TIMESTAMP COMMENT '创建时间',
    CREATED_BY       VARCHAR(255) COMMENT '创建人',
    UPDATED_AT       TIMESTAMP COMMENT '更新时间',
    UPDATED_BY       VARCHAR(255) COMMENT '更新人'
);