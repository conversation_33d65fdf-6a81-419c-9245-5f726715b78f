CREATE TABLE t_edu_admission
(
    ID                   VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    NAME                 VARCHAR(255) COMMENT '姓名',
    PHONE_NUM            VARCHAR(255) COMMENT '电话',

    ADMISSION_GRADE_CODE VARCHAR(255) COMMENT '年级编号(1：1年级、2：2年级、3：3年级、4：4年级、5：5年级、6：6年级、7：7年级、8：8年级、9：9年级、10：10年级、11：11年级)',

    YEAR_SYSTEM_CODE     VARCHAR(255) COMMENT '申请学制(3：3年、3：3.5年)',
    IS_ENROLLED          VARCHAR(20) COMMENT '是否入读(0：否、1：是)',

    INVITE_DESC          VARCHAR(1024) COMMENT '开放日邀约描述',
    EXAM_INFO            VARCHAR(1024) COMMENT '考试情况描述',

    DESCRIPTION          VARCHAR(255) COMMENT '备注',

    CREATED_AT           TIMESTAMP COMMENT '创建时间',
    CREATED_BY           VARCHAR(255) COMMENT '创建人',
    UPDATED_AT           TIMESTAMP COMMENT '更新时间',
    UPDATED_BY           VARCHAR(255) COMMENT '更新人'
);