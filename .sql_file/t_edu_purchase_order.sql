CREATE TABLE t_edu_purchase_order
-- 书籍采购表
(
    ID                     VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    DEPARTMENT             VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE        VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    YEAR                   VARCHAR(255) COMMENT '年度',
    SEMESTER               VARCHAR(20) COMMENT '学期(S1：第一学期、S2：第二学期)',
    PURCHASE_NO            VARCHAR(255) COMMENT '采购单号',
    PURCHASE_DATE          DATE COMMENT '采购日期',
    REQUESTER_NAME         VARCHAR(255) COMMENT '申请人姓名',
    BOOK_CATEGORY          VARCHAR(20) COMMENT '书籍分类(1:中文教材、2:外文教材)',
    SUPPLIER_ID            VARCHAR(255) COMMENT '供应商ID',
    TOTAL_AMOUNT           DECIMAL(10, 2) COMMENT '总金额',
    CURRENCY               VARCHAR(20) COMMENT '货币类型(CNY：人民币、JPY：日元、USD：美元、EUR：欧元)',
    PAYMENT_METHOD         VARCHAR(20) COMMENT '付款方式(1:先付款后发货、2:先发货后付款)',
    PURCHASE_STATUS        VARCHAR(20) COMMENT '采购状态(0：已取消、1：待报价、2：已报价、3：待审批、4：已审批、5：已下单、6：已发货、7：已收货、8：已完成)',
    IS_NEED_CONTRACT       VARCHAR(20) COMMENT '是否需要合同(0：否、1：是)',
    CONTRACT_ID            VARCHAR(255) COMMENT '合同ID',
    EXPECTED_DELIVERY_DATE DATE COMMENT '预计到货日期',
    ACTUAL_DELIVERY_DATE   DATE COMMENT '实际到货日期',

    DESCRIPTION            VARCHAR(255) COMMENT '备注描述',
    CREATED_AT             TIMESTAMP COMMENT '创建时间',
    CREATED_BY             VARCHAR(255) COMMENT '创建人',
    UPDATED_AT             TIMESTAMP COMMENT '更新时间',
    UPDATED_BY             VARCHAR(255) COMMENT '更新人'
);