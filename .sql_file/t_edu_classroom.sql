CREATE TABLE t_edu_classroom
(
    ID              VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    DEPARTMENT      VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE VARCHAR(20) COMMENT '部门编号(SY：双语部、IC：融合部、DP：高中部)',
    BUILDING_NAME   VARCHAR(255) COMMENT '楼名',
    BUILDING_CODE   VARCHAR(255) COMMENT '楼号',
    FLOOR           VARCHAR(20) COMMENT '楼层(1：1楼、2：2楼、3：3楼、4：4楼、5：5楼、6：6楼、7：7楼、8：8楼、9：9楼、10：10楼)',

    NAME            VARCHAR(255) COMMENT '教室名称',
    CODE            VARCHAR(255) COMMENT '教室编号',
    IS_CONTROLLED   VARCHAR(20) COMMENT '是否有门禁(0：否、1：是)',
    TAG             VARCHAR(255) COMMENT '标签',
    DESCRIPTION     VARCHAR(255) COMMENT '备注',

    CREATED_AT      TIMESTAMP COMMENT '创建时间',
    CREATED_BY      VARCHAR(255) COMMENT '创建人',
    UPDATED_AT      TIMESTAMP COMMENT '更新时间',
    UPDATED_BY      VARCHAR(255) COMMENT '更新人'
);