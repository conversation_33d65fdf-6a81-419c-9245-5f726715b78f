CREATE TABLE t_edu_payment_record
-- 支付记录表
(
    ID              VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    CONTRACT_ID     VARCHAR(255) COMMENT '合同ID',
    PAYMENT_TYPE    VARCHAR(20) COMMENT '支付类型(1：预付款、2：尾款、3:其他)',
    PAYMENT_AMOUNT  DECIMAL(10, 2) COMMENT '支付金额',
    PAYMENT_DATE    DATE COMMENT '支付日期',
    PAYMENT_METHOD  VARCHAR(20) COMMENT '支付方式(1：银行转账、2：支票、3:现金)',
    OA_PROCESS_ID   VARCHAR(255) COMMENT 'OA流程ID',
    PAYMENT_STATUS  VARCHAR(20) COMMENT '支付方式(0：支付失败、1：待审批、2：已审批、3:已支付)',
    PAYMENT_VOUCHER VARCHAR(255) COMMENT '支付凭证',

    DESCRIPTION     VARCHAR(255) COMMENT '备注描述',
    CREATED_AT      TIMESTAMP COMMENT '创建时间',
    CREATED_BY      VARCHAR(255) COMMENT '创建人',
    UPDATED_AT      TIMESTAMP COMMENT '更新时间',
    UPDATED_BY      VARCHAR(255) COMMENT '更新人'
);