CREATE TABLE t_edu_invite_code
(
    ID          VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    CODE        VARCHAR(60) COMMENT '邀请码',
    USER_ID     VARCHAR(255) COMMENT '用户ID',
    USED_TIME   TIMESTAMP COMMENT '使用时间',
    IS_USED     VARCHAR(20) COMMENT '是否使用(0：否、1：是)',
    EXPIRY_TIME TIMESTAMP COMMENT '过期时间',
    DESCRIPTION VARCHAR(255) COMMENT '描述',
    CREATED_AT  TIMESTAMP COMMENT '创建时间',
    CREATED_BY  VARCHAR(255) COMMENT '创建人',
    UPDATED_AT  TIMESTAMP COMMENT '更新时间',
    UPDATED_BY  VARCHAR(255) COMMENT '更新人'
);