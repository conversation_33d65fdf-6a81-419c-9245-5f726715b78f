CREATE TABLE t_edu_schedule
(
    ID              VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    YEAR            VARCHAR(255) COMMENT '年度',
    SEMESTER        VARCHAR(20) COMMENT '学期(S1：第一学期、S2：第二学期)',
    DEPARTMENT      VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    SUBJECT_NAME    VARCHAR(255) COMMENT '学科名称·即子学科',
    GRADE_CODE      VARCHAR(20) COMMENT '年级名称(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)',
    CLASS_CODE      VARCHAR(20) COMMENT '班级编号(A：A班、B：B班)',

    SUBJECT_LEVEL   VARCHAR(20) COMMENT '学科层级(SL：SL、HL：HL)',
    TEACHER_ID VARCHAR(255) COMMENT '教师ID',
    CLASS_ROOM_CODE VARCHAR(255) COMMENT '教室编号',

    WEEKDAY         VARCHAR(20) COMMENT '星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)',
    DAY_PART        VARCHAR(20) COMMENT '时段(0：上午、1：中午、2：下午)',
    PERIOD VARCHAR (20) COMMENT '课段(1：第一节课、2：第二节课、3：第三节课、4：第四节课、5：第五节课、6：第六节课、7：第七节课、8：第八节课、9：第九节课、10：第十节课)',
    START_TIME      TIME COMMENT '开始时间',
    END_TIME        TIME COMMENT '结束时间',


    DESCRIPTION     VARCHAR(255) COMMENT '描述',

    CREATED_AT      TIMESTAMP COMMENT '创建时间',
    CREATED_BY      VARCHAR(255) COMMENT '创建人',
    UPDATED_AT      TIMESTAMP COMMENT '更新时间',
    UPDATED_BY      VARCHAR(255) COMMENT '更新人'
);