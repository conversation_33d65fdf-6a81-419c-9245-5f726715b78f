CREATE TABLE t_edu_tmp_schedule
(
    ID              VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    YEAR            VARCHAR(255) COMMENT '年度',
    SEMESTER        VARCHAR(20) COMMENT '学期(S1：第一学期、S2：第二学期)',
    DEPARTMENT      VARCHAR(255) COMMENT '部门名称',

    DEPARTMENT_CODE VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    GRADE_CODE      VARCHAR(20) COMMENT '年级名称(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)',
    CLASS_CODE      VARCHAR(20) COMMENT '班级编号(A：A班、B：B班)',

    POSITION        VARCHAR(255) COMMENT '位置',
    CONTENT         TEXT COMMENT '内容',
    START_DATE      DATE COMMENT '开始日期',
    END_DATE        DATE COMMENT '结束日期',

    COLOR           VARCHAR(255) COMMENT '置换颜色',

    DESCRIPTION     VARCHAR(255) COMMENT '描述',

    CREATED_AT      TIMESTAMP COMMENT '创建时间',
    CREATED_BY      VARCHAR(255) COMMENT '创建人',
    UPDATED_AT      TIMESTAMP COMMENT '更新时间',
    UPDATED_BY      VARCHAR(255) COMMENT '更新人'
);