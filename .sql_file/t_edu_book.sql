CREATE TABLE t_edu_book
-- 书籍表
(
    ID                    VARCHAR(255) PRIMARY KEY COMMENT '主键ID',

    BOOK_NAME             VARCHAR(255) COMMENT '书名',
    AUTHOR                VARCHAR(255) COMMENT '作者',
    ISBN                  VARCHAR(255) COMMENT 'ISBN编号',
    PUBLISHER             VARCHAR(255) COMMENT '出版社',
    PUBLICATION_DATE      DATE COMMENT '出版日期',
    BOOK_CATEGORY         VARCHAR(20) COMMENT '书籍分类(1:中文教材、2:外文教材)',
    SUBJECT_CODE          VARCHAR(255) COMMENT '学科编号',
    GRADE_LEVEL           VARCHAR(90) COMMENT '年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)',
    PRICE                 DECIMAL(10, 2) COMMENT '单价',
    PURCHASE_PRICE        DECIMAL(10, 2) COMMENT '采购价',
    SUPPLIER_ID           VARCHAR(255) COMMENT '供应商ID',
    CURRENT_STOCK         INT DEFAULT 0 COMMENT '当前库存',
    TOTAL_IN_STOCK        INT DEFAULT 0 COMMENT '总入库数',
    TOTAL_OUT_STOCK       INT DEFAULT 0 COMMENT '总出库数',
    MIN_STOCK_ALERT       INT DEFAULT 0 COMMENT '最低库存预警',
    MAX_STOCK_LIMIT       INT COMMENT '最高库存限制',
    storage_location      VARCHAR(255) COMMENT '存放位置',
    BOOK_CONDITION        VARCHAR(20) COMMENT '书籍状况(1：全新、2：良好、3：一般、4：损坏)',
    FRONT_COVER_IMAGE     VARCHAR(255) COMMENT '封面图片',
    FRONT_COVER_FILE_NAME VARCHAR(255) COMMENT '封面图片原始文件名',
    BACK_COVER_IMAGE      VARCHAR(255) COMMENT '封底图片',
    BACK_COVER_FILE_NAME  VARCHAR(255) COMMENT '封底图片原始文件名',
    SCAN_DATE             DATE COMMENT '扫描入库日期',
    SCANNED_BY            VARCHAR(255) COMMENT '扫描人',
    BOOK_STATUS           VARCHAR(20) COMMENT '书籍状态(0：下架、1：正常、2：损坏、3：遗失)',
    DESCRIPTION           VARCHAR(255) COMMENT '备注描述',

    CREATED_AT            TIMESTAMP COMMENT '创建时间',
    CREATED_BY            VARCHAR(255) COMMENT '创建人',
    UPDATED_AT            TIMESTAMP COMMENT '更新时间',
    UPDATED_BY            VARCHAR(255) COMMENT '更新人'
);