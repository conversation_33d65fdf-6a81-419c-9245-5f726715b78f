CREATE TABLE t_edu_permission
-- 权限表
(
    ID             VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    MENU_ID        VARCHAR(255) COMMENT '菜单ID',
    MENU_PARENT_ID VARCHAR(255) COMMENT '菜单父级ID',

    NAME           VARCHAR(60) COMMENT '权限名称',
    CODE           VARCHAR(100) COMMENT '权限编码',
    TYPE           VARCHAR(20) COMMENT '权限类型(1: 菜单权限, 2: 按钮权限)',

    URL            VARCHAR(255) COMMENT '权限URL', -- 当 type 为 2 (按钮权限) 时，可以记录按钮触发的 API 接口地址，用于后端权限校验。
    METHOD         VARCHAR(20) COMMENT '请求方法(GET、POST、PUT、DELETE)',
    STATUS         VARCHAR(20) COMMENT '状态(0：禁用、1：启用)',
    DESCRIPTION    VARCHAR(255) COMMENT '权限描述',

    CREATED_AT     TIMESTAMP COMMENT '创建时间',
    CREATED_BY     VARCHAR(255) COMMENT '创建人',
    UPDATED_AT     TIMESTAMP COMMENT '更新时间',
    UPDATED_BY     VARCHAR(255) COMMENT '更新人'
);