CREATE TABLE t_edu_file
-- 文件表
(
    ID          VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    FILE_ID     VARCHAR(255) COMMENT '文件唯一标识符',
    FILE_NAME   VARCHAR(255) COMMENT '原始文件名',
    FILE_TYPE   VARCHAR(20) COMMENT '文件类型',
    FILE_SIZE   BIGINT(20) COMMENT '文件大小（字节）',
    FULL_PATH   VARCHAR(500) COMMENT '完整文件路径',
    STORAGE_DIR VARCHAR(255) COMMENT '存储目录',
    PREVIEW_URL VARCHAR(500) COMMENT '预览URL',
    IS_PREVIEW  VARCHAR(20) COMMENT '是否可预览(0: 否, 1: 是)',
    EXTENSION   VARCHAR(20) COMMENT '文件扩展名',

    STATUS      VARCHAR(20) COMMENT '状态(0：禁用、1：启用)',
    DESCRIPTION VARCHAR(255) COMMENT '备注',

    CREATED_AT  TIMESTAMP COMMENT '创建时间',
    CREATED_BY  VARCHAR(255) COMMENT '创建人',
    UPDATED_AT  TIMESTAMP COMMENT '更新时间',
    UPDATED_BY  VARCHAR(255) COMMENT '更新人'
);