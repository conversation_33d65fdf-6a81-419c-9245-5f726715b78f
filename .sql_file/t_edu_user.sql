CREATE TABLE t_edu_user
(
    ID              VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    LAST_NAME       VARCHAR(255) COMMENT '姓',
    FIRST_NAME      VARCHAR(255) COMMENT '名',
    NAME_ZH         VARCHAR(255) COMMENT '中文名',
    NAME_EN         VARCHAR(255) COMMENT '英文名',
    GENDER          TINYINT COMMENT '性别(0：女、1：男)',
    BIRTH_DATE      DATE COMMENT '出生日期(真实)',
    BIRTHDAY_TYPE   VARCHAR(255) COMMENT '生日类型(0：农历、1：公立)',
    BIRTHDAY        DATE COMMENT '生日',
    DEPARTMENT_NAME VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    EMP_NO          VARCHAR(255) COMMENT '工号',
    EMAIL           VARCHAR(255) COMMENT '邮箱',
    PASSWORD        VARCHAR(255) COMMENT '用户密码',
    LANGUAGE        VARCHAR(50) COMMENT '语言',
    CREATED_AT      TIMESTAMP COMMENT '创建时间',
    CREATED_BY      VARCHAR(255) COMMENT '创建人',
    UPDATED_AT      TIMESTAMP COMMENT '更新时间',
    UPDATED_BY      VARCHAR(255) COMMENT '更新人'
);