CREATE TABLE t_edu_supplier
-- 供应商表
(
    ID               VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    SUPPLIER_NAME    VARCHAR(255) COMMENT '供应商名称',
    SUPPLIER_CODE    VARCHAR(255) COMMENT '供应商编码',
    SUPPLIER_TYPE    VARCHAR(20) COMMENT '供应商类型(1：中文图书、2：外文图书、3：综合)',
    CONTACT_NAME     VARCHAR(255) COMMENT '联系人姓名',
    CONTACT_PHONE    VARCHAR(255) COMMENT '联系人电话',
    CONTACT_EMAIL    VARCHAR(255) COMMENT '联系人邮箱',
    CONTACT_ADDRESS  VARCHAR(255) COMMENT '联系人地址',

    BUSINESS_LICENSE VARCHAR(255) COMMENT '营业执照号',
    BANK_NAME        VARCHAR(255) COMMENT '开户银行',
    TAX_NUMBER       VARCHAR(255) COMMENT '税号',
    CREDIT_RATING    VARCHAR(20) COMMENT '信用等级(A：极好、B：好、C:一般、D：差、E：极差)',
    STATUS           VARCHAR(20) COMMENT '合作状态(0:终止、1:正常、2:暂停)',
    DESCRIPTION      VARCHAR(255) COMMENT '备注描述',

    CREATED_AT       TIMESTAMP COMMENT '创建时间',
    CREATED_BY       VARCHAR(255) COMMENT '创建人',
    UPDATED_AT       TIMESTAMP COMMENT '更新时间',
    UPDATED_BY       VARCHAR(255) COMMENT '更新人'
);