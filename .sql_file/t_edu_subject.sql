CREATE TABLE t_edu_subject
(
    ID                 VARCHAR(255) PRIMARY KEY COMMENT '主键ID',
    DEPARTMENT         VARCHAR(255) COMMENT '部门名称',
    DEPARTMENT_CODE    VARCHAR(20) COMMENT '部门(SY：双语部、IC：融合部、DP：高中部)',
    GROUP_NAME         VARCHAR(255) COMMENT '学科组名称',
    GROUP_CODE         VARCHAR(255) COMMENT '学科组编号',
    NAME               VARCHAR(255) COMMENT '学科名称',
    CODE               VARCHAR(255) COMMENT '学科编号',
    LEVEL              VARCHAR(255) COMMENT '学科层级',
    SUB_SUBJECT_NAME   VARCHAR(255) COMMENT '子学科名称',
    SUBJECT_TEACHER_ID VARCHAR(255) COMMENT '学科教师ID',
    TEACHING_CLASS_ID  VARCHAR(400) COMMENT '教授班级ID',
    DESCRIPTION        VARCHAR(255) COMMENT '描述',
    CREATED_AT         TIMESTAMP COMMENT '创建时间',
    CREATED_BY         VARCHAR(255) COMMENT '创建人',
    UPDATED_AT         TIMESTAMP COMMENT '更新时间',
    UPDATED_BY         VARCHAR(255) COMMENT '更新人'
);