package com.edu.www.po;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * ManageBac学生API响应PO
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class MBStudentResponsePO {
    
    /**
     * 学生列表
     */
    private List<MBStudentPO> students;
    
    /**
     * 分页元数据
     */
    private MetaPO meta;
    
    /**
     * 分页元数据
     */
    public static class MetaPO {
        /**
         * 当前页
         */
        @JsonProperty("current_page")
        private Integer currentPage;
        
        /**
         * 总页数
         */
        @JsonProperty("total_pages")
        private Integer totalPages;
        
        /**
         * 总记录数
         */
        @JsonProperty("total_count")
        private Integer totalCount;
        
        /**
         * 每页记录数
         */
        @JsonProperty("per_page")
        private Integer perPage;

        public Integer getCurrentPage() {
            return currentPage;
        }

        public void setCurrentPage(Integer currentPage) {
            this.currentPage = currentPage;
        }

        public Integer getTotalPages() {
            return totalPages;
        }

        public void setTotalPages(Integer totalPages) {
            this.totalPages = totalPages;
        }

        public Integer getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Integer totalCount) {
            this.totalCount = totalCount;
        }

        public Integer getPerPage() {
            return perPage;
        }

        public void setPerPage(Integer perPage) {
            this.perPage = perPage;
        }
    }

    public List<MBStudentPO> getStudents() {
        return students;
    }

    public void setStudents(List<MBStudentPO> students) {
        this.students = students;
    }

    public MetaPO getMeta() {
        return meta;
    }

    public void setMeta(MetaPO meta) {
        this.meta = meta;
    }
}
