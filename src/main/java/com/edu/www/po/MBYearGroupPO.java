package com.edu.www.po;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * ManageBac年级组信息PO
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class MBYearGroupPO {
    
    /**
     * 年级组ID
     */
    private Long id;
    
    /**
     * 年级组名称
     */
    private String name;
    
    /**
     * 年级组简称
     */
    @JsonProperty("short_name")
    private String shortName;
    
    /**
     * 项目名称
     */
    private String program;
    
    /**
     * 年级
     */
    private String grade;
    
    /**
     * 学生ID列表
     */
    @JsonProperty("student_ids")
    private List<Long> studentIds;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getProgram() {
        return program;
    }

    public void setProgram(String program) {
        this.program = program;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public List<Long> getStudentIds() {
        return studentIds;
    }

    public void setStudentIds(List<Long> studentIds) {
        this.studentIds = studentIds;
    }

    @Override
    public String toString() {
        return "MBYearGroupPO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", shortName='" + shortName + '\'' +
                ", program='" + program + '\'' +
                ", grade='" + grade + '\'' +
                ", studentIds=" + studentIds +
                '}';
    }
}
