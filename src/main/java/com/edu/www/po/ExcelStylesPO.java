package com.edu.www.po;

import org.apache.poi.ss.usermodel.CellStyle;

/**
 * Excel样式封装类
 * 用于统一管理Excel导出时的各种单元格样式
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public record ExcelStylesPO(
        /**
         * 表头样式
         */
        CellStyle headerStyle,
        
        /**
         * 周标题样式
         */
        CellStyle weekTitleStyle,
        
        /**
         * 数据单元格样式
         */
        CellStyle dataStyle,
        
        /**
         * 值日类型样式
         */
        CellStyle dutyTypeStyle
) {
    
    /**
     * 验证所有样式都不为null
     */
    public ExcelStylesPO {
        if (headerStyle == null) {
            throw new IllegalArgumentException("headerStyle cannot be null");
        }
        if (weekTitleStyle == null) {
            throw new IllegalArgumentException("weekTitleStyle cannot be null");
        }
        if (dataStyle == null) {
            throw new IllegalArgumentException("dataStyle cannot be null");
        }
        if (dutyTypeStyle == null) {
            throw new IllegalArgumentException("dutyTypeStyle cannot be null");
        }
    }
    
    /**
     * 创建ExcelStyles的构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * ExcelStyles构建器
     */
    public static class Builder {
        private CellStyle headerStyle;
        private CellStyle weekTitleStyle;
        private CellStyle dataStyle;
        private CellStyle dutyTypeStyle;
        
        public Builder headerStyle(CellStyle headerStyle) {
            this.headerStyle = headerStyle;
            return this;
        }
        
        public Builder weekTitleStyle(CellStyle weekTitleStyle) {
            this.weekTitleStyle = weekTitleStyle;
            return this;
        }
        
        public Builder dataStyle(CellStyle dataStyle) {
            this.dataStyle = dataStyle;
            return this;
        }
        
        public Builder dutyTypeStyle(CellStyle dutyTypeStyle) {
            this.dutyTypeStyle = dutyTypeStyle;
            return this;
        }
        
        public ExcelStylesPO build() {
            return new ExcelStylesPO(headerStyle, weekTitleStyle, dataStyle, dutyTypeStyle);
        }
    }
}
