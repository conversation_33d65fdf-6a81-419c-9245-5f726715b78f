package com.edu.www.po;

/**
 * 考试组成信息PO
 */
public class CompositionInfoPO {
    /**
     * 学科编码·即子学科编码
     */
    private String subjectCode;

    /**
     * 学科分类(AA：A&A、AI：A&I、LL：L&L、LP：L&P)
     */
    private String subjectType;

    /**
     * 试卷类型(参见EduPaperTypeEnum枚举)
     */
    private String subjectPaper;

    /**
     * 学科层级(SL：SL、HL：HL)
     */
    private String subjectLevel;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 间隔时长
     */
    private String intervalDuration;

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    public String getSubjectPaper() {
        return subjectPaper;
    }

    public void setSubjectPaper(String subjectPaper) {
        this.subjectPaper = subjectPaper;
    }

    public String getSubjectLevel() {
        return subjectLevel;
    }

    public void setSubjectLevel(String subjectLevel) {
        this.subjectLevel = subjectLevel;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getIntervalDuration() {
        return intervalDuration;
    }

    public void setIntervalDuration(String intervalDuration) {
        this.intervalDuration = intervalDuration;
    }
}
