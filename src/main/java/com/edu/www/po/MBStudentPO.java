package com.edu.www.po;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.List;

/**
 * ManageBac学生信息PO
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class MBStudentPO {
    
    /**
     * 学生ID
     */
    private Long id;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 名字
     */
    @JsonProperty("first_name")
    private String firstName;
    
    /**
     * 姓氏
     */
    @JsonProperty("last_name")
    private String lastName;
    
    /**
     * 是否归档
     */
    private Boolean archived;
    
    /**
     * UI语言
     */
    @JsonProperty("ui_language")
    private String uiLanguage;
    
    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private Date createdAt;
    
    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    private Date updatedAt;
    
    /**
     * 学生编号
     */
    @JsonProperty("student_id")
    private String studentId;
    
    /**
     * 标识符
     */
    private String identifier;
    
    /**
     * 最后访问时间
     */
    @JsonProperty("last_accessed_at")
    private Date lastAccessedAt;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 班主任ID
     */
    @JsonProperty("homeroom_advisor_id")
    private Long homeroomAdvisorId;
    
    /**
     * 班级年级
     */
    @JsonProperty("class_grade")
    private String classGrade;
    
    /**
     * 项目
     */
    private String program;
    
    /**
     * 年级组ID
     */
    @JsonProperty("year_group_id")
    private Long yearGroupId;
    
    /**
     * 家长ID列表
     */
    @JsonProperty("parent_ids")
    private List<Long> parentIds;
    
    /**
     * 街道地址
     */
    @JsonProperty("street_address")
    private String streetAddress;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 州/省
     */
    private String state;
    
    /**
     * 邮编
     */
    private String zipcode;
    
    /**
     * 国家
     */
    private String country;
    
    /**
     * 国籍列表
     */
    private List<String> nationalities;
    
    /**
     * 语言列表
     */
    private List<String> languages;
    
    /**
     * 毕业年份
     */
    @JsonProperty("graduating_year")
    private Integer graduatingYear;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    public String getUiLanguage() {
        return uiLanguage;
    }

    public void setUiLanguage(String uiLanguage) {
        this.uiLanguage = uiLanguage;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public Date getLastAccessedAt() {
        return lastAccessedAt;
    }

    public void setLastAccessedAt(Date lastAccessedAt) {
        this.lastAccessedAt = lastAccessedAt;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Long getHomeroomAdvisorId() {
        return homeroomAdvisorId;
    }

    public void setHomeroomAdvisorId(Long homeroomAdvisorId) {
        this.homeroomAdvisorId = homeroomAdvisorId;
    }

    public String getClassGrade() {
        return classGrade;
    }

    public void setClassGrade(String classGrade) {
        this.classGrade = classGrade;
    }

    public String getProgram() {
        return program;
    }

    public void setProgram(String program) {
        this.program = program;
    }

    public Long getYearGroupId() {
        return yearGroupId;
    }

    public void setYearGroupId(Long yearGroupId) {
        this.yearGroupId = yearGroupId;
    }

    public List<Long> getParentIds() {
        return parentIds;
    }

    public void setParentIds(List<Long> parentIds) {
        this.parentIds = parentIds;
    }

    public String getStreetAddress() {
        return streetAddress;
    }

    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public List<String> getNationalities() {
        return nationalities;
    }

    public void setNationalities(List<String> nationalities) {
        this.nationalities = nationalities;
    }

    public List<String> getLanguages() {
        return languages;
    }

    public void setLanguages(List<String> languages) {
        this.languages = languages;
    }

    public Integer getGraduatingYear() {
        return graduatingYear;
    }

    public void setGraduatingYear(Integer graduatingYear) {
        this.graduatingYear = graduatingYear;
    }
}
