package com.edu.www.po;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;

/**
 * 值日统计Excel导出PO
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@HeadRowHeight(25)
@ContentRowHeight(20)
// 表头样式
@HeadStyle(
    fillForegroundColor = 22, // 浅灰色
    horizontalAlignment = HorizontalAlignmentEnum.CENTER,
    verticalAlignment = VerticalAlignmentEnum.CENTER,
    borderTop = BorderStyleEnum.THIN,
    borderBottom = BorderStyleEnum.THIN,
    borderLeft = BorderStyleEnum.THIN,
    borderRight = BorderStyleEnum.THIN,
    topBorderColor = 8,  // 黑色
    bottomBorderColor = 8,
    leftBorderColor = 8,
    rightBorderColor = 8
)
// 表头字体
@HeadFontStyle(
    fontName = "宋体",
    fontHeightInPoints = 11,
    bold = BooleanEnum.TRUE
)
// 内容样式
@ContentStyle(
    horizontalAlignment = HorizontalAlignmentEnum.CENTER,
    verticalAlignment = VerticalAlignmentEnum.CENTER,
    borderTop = BorderStyleEnum.THIN,
    borderBottom = BorderStyleEnum.THIN,
    borderLeft = BorderStyleEnum.THIN,
    borderRight = BorderStyleEnum.THIN,
    topBorderColor = 8,  // 黑色
    bottomBorderColor = 8,
    leftBorderColor = 8,
    rightBorderColor = 8,
    wrapped = BooleanEnum.TRUE
)
// 内容字体
@ContentFontStyle(
    fontName = "宋体",
    fontHeightInPoints = 11
)
public class DutyStatisticExcelPO {

    /**
     * 序号
     */
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(8)
    private Integer seq;

    /**
     * 值日教师姓名
     */
    @ExcelProperty(value = "教师姓名", index = 1)
    @ColumnWidth(15)
    private String teacherName;

    /**
     * 周一至周四值日次数(普通值日次数)
     */
    @ExcelProperty(value = "值日次数", index = 2)
    @ColumnWidth(20)
    private Integer regularDutyCount;

    /**
     * 周五值日次数
     */
    @ExcelProperty(value = "周五值日", index = 3)
    @ColumnWidth(15)
    private Integer fridayDutyCount;

    /**
     * 值晚自修天数
     */
    @ExcelProperty(value = "18:30到21:10晚 天数", index = 4)
    @ColumnWidth(15)
    private Integer lateDutyDay;

    /**
     * 周日值日次数
     */
    @ExcelProperty(value = "周日晚自习", index = 5)
    @ColumnWidth(15)
    private Integer sundayDutyCount;

    /**
     * 周一至周四值日日期(普通值日日期)
     */
    @ExcelProperty(value = "周一至周四值日日期", index = 6)
    @ColumnWidth(30)
    private String regularDutyDateStr;

    /**
     * 周五值日日期
     */
    @ExcelProperty(value = "周五值日日期", index = 7)
    @ColumnWidth(25)
    private String fridayDutyDateStr;

    /**
     * 值晚自修日期
     */
    @ExcelProperty(value = "值晚自修日期", index = 8)
    @ColumnWidth(25)
    private String lateDutyDateStr;

    /**
     * 周日值日日期
     */
    @ExcelProperty(value = "周日值日日期", index = 9)
    @ColumnWidth(25)
    private String sundayDutyDateStr;

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public Integer getRegularDutyCount() {
        return regularDutyCount;
    }

    public void setRegularDutyCount(Integer regularDutyCount) {
        this.regularDutyCount = regularDutyCount;
    }

    public Integer getFridayDutyCount() {
        return fridayDutyCount;
    }

    public void setFridayDutyCount(Integer fridayDutyCount) {
        this.fridayDutyCount = fridayDutyCount;
    }

    public Integer getLateDutyDay() {
        return lateDutyDay;
    }

    public void setLateDutyDay(Integer lateDutyDay) {
        this.lateDutyDay = lateDutyDay;
    }

    public Integer getSundayDutyCount() {
        return sundayDutyCount;
    }

    public void setSundayDutyCount(Integer sundayDutyCount) {
        this.sundayDutyCount = sundayDutyCount;
    }

    public String getRegularDutyDateStr() {
        return regularDutyDateStr;
    }

    public void setRegularDutyDateStr(String regularDutyDateStr) {
        this.regularDutyDateStr = regularDutyDateStr;
    }

    public String getFridayDutyDateStr() {
        return fridayDutyDateStr;
    }

    public void setFridayDutyDateStr(String fridayDutyDateStr) {
        this.fridayDutyDateStr = fridayDutyDateStr;
    }

    public String getLateDutyDateStr() {
        return lateDutyDateStr;
    }

    public void setLateDutyDateStr(String lateDutyDateStr) {
        this.lateDutyDateStr = lateDutyDateStr;
    }

    public String getSundayDutyDateStr() {
        return sundayDutyDateStr;
    }

    public void setSundayDutyDateStr(String sundayDutyDateStr) {
        this.sundayDutyDateStr = sundayDutyDateStr;
    }
}
