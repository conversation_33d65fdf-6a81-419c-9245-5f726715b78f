package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "随机分组接口")
@RequestMapping("/random/group")
public class RandomGroupController {
    @Autowired
    private StudentService studentService;


    @ApiOperation("随机分组(开始)")
    @GetMapping("/start")
    public ResponseEntity<Object> start(Integer groupNum) {
        return studentService.randomGroup(groupNum);
    }
}
