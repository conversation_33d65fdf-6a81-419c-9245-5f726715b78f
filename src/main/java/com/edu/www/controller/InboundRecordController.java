package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.InboundRecordService;
import com.edu.www.vo.InboundRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 入库记录管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "入库记录管理接口")
@RequestMapping("/inbound/record")
public class InboundRecordController {

    @Autowired
    private InboundRecordService inboundRecordService;

    /**
     * 根据ID查询入库记录信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询入库记录信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return inboundRecordService.get(request, id);
    }

    /**
     * 分页查询入库记录信息
     *
     * @param inboundRecordVO
     * @return
     */
    @ApiOperation("分页查询入库记录信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody InboundRecordVO inboundRecordVO) {
        return inboundRecordService.queryPage(request, inboundRecordVO);
    }

    /**
     * 新增入库记录信息
     *
     * @param inboundRecordVO
     * @return
     */
    @ApiOperation("新增入库记录信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody InboundRecordVO inboundRecordVO) {
        return inboundRecordService.insert(request, inboundRecordVO);
    }

    /**
     * 修改入库记录信息
     *
     * @param inboundRecordVO
     * @return
     */
    @ApiOperation("修改入库记录信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody InboundRecordVO inboundRecordVO) {
        return inboundRecordService.update(request, inboundRecordVO);
    }

    /**
     * 删除入库记录信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除入库记录信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return inboundRecordService.delete(request, id);
    }
}
