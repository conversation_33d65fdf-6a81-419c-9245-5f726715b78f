package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.PurchaseContractService;
import com.edu.www.vo.PurchaseContractVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购合同管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "采购合同管理接口")
@RequestMapping("/purchaseContract")
public class PurchaseContractController {

    @Autowired
    private PurchaseContractService purchaseContractService;

    /**
     * 根据ID查询采购合同信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询采购合同信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return purchaseContractService.get(request, id);
    }

    /**
     * 分页查询采购合同信息
     *
     * @param purchaseContractVO
     * @return
     */
    @ApiOperation("分页查询采购合同信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody PurchaseContractVO purchaseContractVO) {
        return purchaseContractService.queryPage(request, purchaseContractVO);
    }

    /**
     * 新增采购合同信息
     *
     * @param purchaseContractVO
     * @return
     */
    @ApiOperation("新增采购合同信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody PurchaseContractVO purchaseContractVO) {
        return purchaseContractService.insert(request, purchaseContractVO);
    }

    /**
     * 修改采购合同信息
     *
     * @param purchaseContractVO
     * @return
     */
    @ApiOperation("修改采购合同信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody PurchaseContractVO purchaseContractVO) {
        return purchaseContractService.update(request, purchaseContractVO);
    }

    /**
     * 删除采购合同信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除采购合同信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return purchaseContractService.delete(request, id);
    }
}
