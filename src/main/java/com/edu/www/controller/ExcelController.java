package com.edu.www.controller;

import com.edu.www.service.ExcelService;
import com.edu.www.vo.SplitExpenseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Excel信息 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "Excel接口")
@RequestMapping("/excel")
public class ExcelController {

    @Autowired
    private ExcelService excelService;

    @ApiOperation("导入Excel")
    @GetMapping("/import")
    public ResponseEntity<Boolean> importExcel() {
        return null;
    }


    @ApiOperation("导出Excel")
    @GetMapping("/export")
    public ResponseEntity<Boolean> exportExcel() {
        return null;
    }

    /**
     * 生成分摊表
     *
     * @param request
     * @param splitExpenseVO
     * @return
     */
    public ResponseEntity<Resource> generateSplitExpenseExcel(HttpServletRequest request, @RequestBody SplitExpenseVO splitExpenseVO) {

        byte[] excelBytes = excelService.generateSplitExpenseExcel(splitExpenseVO);

        // Set headers for download
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=users.xlsx");
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        ByteArrayResource resource = new ByteArrayResource(excelBytes);

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(excelBytes.length)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .body(resource);

    }
}
