package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.enums.*;
import com.edu.www.po.MBYearGroupPO;
import com.edu.www.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.List;

/**
 * 公共信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "公共信息接口")
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private CommonService commonService;

    /**
     * 获取部门编号枚举列表
     *
     * @return
     */
    @ApiOperation("获取部门编号枚举列表")
    @GetMapping("/getDeptCodeList")
    public ResponseEntity<Object> getDeptCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> deptCodeMap = EduDeptCodeEnum.getAll();
        responseEntity.setData(deptCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取部门编号枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取年级编号枚举列表
     *
     * @param deptCode 部门编码(IC：融合部、DP：高中部)
     * @return
     */
    @ApiOperation("获取年级编号枚举列表")
    @GetMapping("/getGradeCodeList")
    public ResponseEntity<Object> getGradeCodeList(@RequestParam(required = false) String deptCode) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> gradeCodeMap = EduGradeCodeEnum.getByDeptCode(deptCode);
        responseEntity.setData(gradeCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取年级编号枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取班级编号枚举列表
     *
     * @return
     */
    @ApiOperation("获取班级编号枚举列表")
    @GetMapping("/getClassCodeList")
    public ResponseEntity<Object> getClassCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> classCodeMap = EduClassCodeEnum.getAll();
        responseEntity.setData(classCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取班级编号枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取基组编码枚举列表
     *
     * @return
     */
    @ApiOperation("获取基组编码枚举列表")
    @GetMapping("/getBaseGroupCodeList")
    public ResponseEntity<Object> getBaseGroupCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> baseGroupCodeMap = EduBaseGroupEnum.getAll();
        responseEntity.setData(baseGroupCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取基组编码枚举列表成功");
        return responseEntity;
    }

    /**
     * 根据部门获取班级列表
     *
     * @param deptCode 部门编码
     * @return
     */
    @ApiOperation("根据部门获取班级列表")
    @GetMapping("/getClazzByDept")
    public ResponseEntity<Object> getClazzByDept(@RequestParam String deptCode) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> clazzCodeMap = EduClazzCodeEnum.getByDeptCode(deptCode);
        responseEntity.setData(clazzCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取班级列表成功");
        return responseEntity;
    }

    /**
     * 获取学科层级枚举列表
     *
     * @return
     */
    @ApiOperation("获取学科层级枚举列表")
    @GetMapping("/getSubjectLevelList")
    public ResponseEntity<Object> getSubjectLevelList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> subjectLevelMap = EduSubjectLevelEnum.getAll();
        responseEntity.setData(subjectLevelMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学科层级枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取星期枚举列表
     *
     * @return
     */
    @ApiOperation("获取星期枚举列表")
    @GetMapping("/getWeekdayList")
    public ResponseEntity<Object> getWeekdayList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> weekdayMap = EduWeekdayEnum.getAllDesc();
        responseEntity.setData(weekdayMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取星期枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取时段枚举列表
     *
     * @return
     */
    @ApiOperation("获取时段枚举列表")
    @GetMapping("/getDayPartList")
    public ResponseEntity<Object> getDayPartList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> dayPartMap = EduDayPartEnum.getAll();
        responseEntity.setData(dayPartMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取时段枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取是否枚举列表
     *
     * @return
     */
    @ApiOperation("获取是否枚举列表")
    @GetMapping("/getYesOrNoList")
    public ResponseEntity<Object> getYesOrNoList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> yesOrNoMap = EduYesOrNoEnum.getAll();
        responseEntity.setData(yesOrNoMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取是否枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取课段枚举列表
     *
     * @return
     */
    @ApiOperation("获取课段枚举列表")
    @GetMapping("/getPeriodList")
    public ResponseEntity<Object> getPeriodList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> periodMap = EduPeriodEnum.getAll();
        responseEntity.setData(periodMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取课段枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取性别枚举列表
     *
     * @return
     */
    @ApiOperation("获取性别枚举列表")
    @GetMapping("/getGenderList")
    public ResponseEntity<Object> getGenderList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> genderMap = EduGenderEnum.getAll();
        responseEntity.setData(genderMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取性别枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取工作状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取工作状态枚举列表")
    @GetMapping("/getWorkStatusList")
    public ResponseEntity<Object> getWorkStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> workStatusMap = EduWorkStatusEnum.getAll();
        responseEntity.setData(workStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取工作状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取学期枚举列表
     *
     * @return
     */
    @ApiOperation("获取学期枚举列表")
    @GetMapping("/getSemesterList")
    public ResponseEntity<Object> getSemesterList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> semesterMap = EduSemesterEnum.getAll();
        responseEntity.setData(semesterMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学期枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取证件类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取证件类型枚举列表")
    @GetMapping("/getCardTypeList")
    public ResponseEntity<Object> getCardTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> semesterMap = EduCardTypeEnum.getAll();
        responseEntity.setData(semesterMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取证件类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取学生状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取学生状态枚举列表")
    @GetMapping("/getStudentStatusList")
    public ResponseEntity<Object> getStudentStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> semesterMap = EduStudentStatusEnum.getAll();
        responseEntity.setData(semesterMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学生状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取入学年级编号枚举列表
     *
     * @return
     */
    @ApiOperation("获取入学年级编号枚举列表")
    @GetMapping("/getAdmissionGradeList")
    public ResponseEntity<Object> getAdmissionGradeCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> admissionGradeCodeMap = EduAdmissionGradeEnum.getAll();
        responseEntity.setData(admissionGradeCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取入学年级编号枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取申请学制枚举列表
     *
     * @return
     */
    @ApiOperation("获取申请学制枚举列表")
    @GetMapping("/getYearSystemList")
    public ResponseEntity<Object> getYearSystemCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> yearSystemCodeMap = EduYearSystemEnum.getAll();
        responseEntity.setData(yearSystemCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取申请学制枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取学科组编码枚举列表
     *
     * @return
     */
    @ApiOperation("获取学科组编码枚举列表")
    @GetMapping("/getGroupCodeList")
    public ResponseEntity<Object> getGroupCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> groupCodeMap = EduGroupCodeEnum.getAll();
        responseEntity.setData(groupCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学科组编码枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取所有学科编码枚举列表
     *
     * @return
     */
    @ApiOperation("获取所有学科编码枚举列表")
    @GetMapping("/getSubjectCodeList")
    public ResponseEntity<Object> getSubjectCodeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> subjectCodeMap = EduSubjectCodeEnum.getAll();
        responseEntity.setData(subjectCodeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学科编码枚举列表成功");
        return responseEntity;
    }

    /**
     * 根据学科组编码获取学科列表
     *
     * @param groupCode 学科组编码
     * @return
     */
    @ApiOperation("根据学科组编码获取学科列表")
    @GetMapping("/getSubjectByGroup")
    public ResponseEntity<Object> getSubjectByGroup(@RequestParam(required = false) String groupCode) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> subjectMap = EduSubjectCodeEnum.getByGroupCode(groupCode);
        responseEntity.setData(subjectMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学科列表成功");
        return responseEntity;
    }

    /**
     * 获取学科组及其包含的学科信息
     *
     * @return
     */
    @ApiOperation("获取学科组及其包含的学科信息")
    @GetMapping("/getGroupsWithSubjects")
    public ResponseEntity<Object> getGroupsWithSubjects() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        responseEntity.setData(EduSubjectCodeEnum.getGroupsWithSubjects());
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学科组及学科信息成功");
        return responseEntity;
    }

    /**
     * 获取子学科枚举列表
     *
     * @return
     */
    @ApiOperation("获取子学科枚举列表")
    @GetMapping("/getSubSubjectList")
    public ResponseEntity<Object> getSubSubjectList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> subSubjectMap = EduSubSubjectEnum.getAll();
        responseEntity.setData(subSubjectMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取子学科枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取未来6年的学年时间段列表
     * 格式：2024-2025, 2025-2026, ...
     *
     * @return 6个学年时间段的列表
     */
    @ApiOperation("获取未来6年的学年时间段列表")
    @GetMapping("/getAcademicYearList")
    public ResponseEntity<Object> getAcademicYearList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        List<String> academicYearList = commonService.getAcademicYearList();
        responseEntity.setData(academicYearList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学年时间段列表成功");
        return responseEntity;
    }

    /**
     * 获取学科分类枚举列表
     *
     * @return
     */
    @ApiOperation("获取学科分类枚举列表")
    @GetMapping("/getSubjectTypeList")
    public ResponseEntity<Object> getSubjectTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> subjectTypeMap = EduSubjectTypeEnum.getAll();
        responseEntity.setData(subjectTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取学科分类枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取教学楼枚举列表
     *
     * @return
     */
    @ApiOperation("获取教学楼枚举列表")
    @GetMapping("/getBuildingList")
    public ResponseEntity<Object> getBuildingList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> buildingMap = EduBuildingEnum.getAll();
        responseEntity.setData(buildingMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取教学楼枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取家庭关系枚举列表
     *
     * @return
     */
    @ApiOperation("获取家庭关系枚举列表")
    @GetMapping("/getRelationshipList")
    public ResponseEntity<Object> getRelationshipList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> relationshipMap = EduRelationshipEnum.getAll();
        responseEntity.setData(relationshipMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取家庭关系枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取民族枚举列表
     *
     * @return
     */
    @ApiOperation("获取民族枚举列表")
    @GetMapping("/getEthnicityList")
    public ResponseEntity<Object> getEthnicityList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> ethnicityMap = EduEthnicityEnum.getAll();
        responseEntity.setData(ethnicityMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取民族枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取血型枚举列表
     *
     * @return
     */
    @ApiOperation("获取血型枚举列表")
    @GetMapping("/getBloodTypeList")
    public ResponseEntity<Object> getBloodTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> bloodTypeMap = EduBloodTypeEnum.getAll();
        responseEntity.setData(bloodTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取血型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取衣服尺码枚举列表
     *
     * @return
     */
    @ApiOperation("获取衣服尺码枚举列表")
    @GetMapping("/getClothingSizeList")
    public ResponseEntity<Object> getClothingSizeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> clothingSizeMap = EduClothingSizeEnum.getAll();
        responseEntity.setData(clothingSizeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取衣服尺码枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取比较运算符枚举列表
     *
     * @return
     */
    @ApiOperation("获取比较运算符枚举列表")
    @GetMapping("/getComparisonOperator")
    public ResponseEntity<Object> getComparisonOperatorList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> cmpSymMap = EduCmpSymEnum.getAll();
        responseEntity.setData(cmpSymMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取比较运算符枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取周序枚举列表
     *
     * @return
     */
    @ApiOperation("获取周序枚举列表")
    @GetMapping("/getWeekSeqList")
    public ResponseEntity<Object> getWeekSeqList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> weekSeqMap = EduWeekSeqEnum.getAll();
        responseEntity.setData(weekSeqMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取周序枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取权限类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取权限类型枚举列表")
    @GetMapping("/getPermissionTypeList")
    public ResponseEntity<Object> getPermissionTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> permissionTypeMap = EduPermissionTypeEnum.getAll();
        responseEntity.setData(permissionTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取权限类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取权限类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取菜单类型枚举列表")
    @GetMapping("/getMenuTypeList")
    public ResponseEntity<Object> getMenuTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> menuTypeMap = EduMenuTypeEnum.getAll();
        responseEntity.setData(menuTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取菜单类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取请求方法枚举列表
     *
     * @return
     */
    @ApiOperation("获取请求方法枚举列表")
    @GetMapping("/getMethodList")
    public ResponseEntity<Object> getMethodList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> methodMap = EduMethodEnum.getAll();
        responseEntity.setData(methodMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取请求方法枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取语言枚举列表
     *
     * @return
     */
    @ApiOperation("获取语言枚举列表")
    @GetMapping("/getLanguageList")
    public ResponseEntity<Object> getLanguageList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> languageMap = EduLanguageEnum.getAll();
        responseEntity.setData(languageMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取语言枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取试卷类型枚举列表
     * @return
     */
    @ApiOperation("获取试卷类型枚举列表")
    @GetMapping("/getPaperTypeList")
    public ResponseEntity<Object> getPaperTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        responseEntity.setData(EduPaperTypeEnum.getAll());
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取试卷类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取考试类型枚举列表
     * @return
     */
    @ApiOperation("获取考试类型枚举列表")
    @GetMapping("/getExamTypeList")
    public ResponseEntity<Object> getExamTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        responseEntity.setData(EduExamTypeEnum.getAll());
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取考试类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取年级组信息
     * @return
     */
    @ApiOperation("获取年级组信息")
    @GetMapping("/getYearGroups")
    public ResponseEntity<Object> getYearGroups() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        try {
            List<MBYearGroupPO> yearGroups = commonService.getYearGroups();
            responseEntity.setData(yearGroups);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("获取年级组信息成功，共" + yearGroups.size() + "个年级组");
        } catch (Exception e) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("获取年级组信息失败: " + e.getMessage());
        }
        return responseEntity;
    }

    /**
     * 获取DP部门值日类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取DP部门值日类型枚举列表")
    @GetMapping("/getDPDutyTypeList")
    public ResponseEntity<Object> getDPDutyTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> dpDutyTypeMap = EduDPDutyTypeEnum.getAllDesc();
        responseEntity.setData(dpDutyTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取DP部门值日类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取DP部门值日类型枚举详细信息
     *
     * @return
     */
    @ApiOperation("获取DP部门值日类型枚举详细信息")
    @GetMapping("/getDPDutyTypeDetail")
    public ResponseEntity<Object> getDPDutyTypeDetails() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, Map<String, String>> allMap = EduDPDutyTypeEnum.getAll();
        responseEntity.setData(allMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取DP部门值日类型枚举详细信息成功");
        return responseEntity;
    }

    /**
     * 获取楼层枚举列表
     *
     * @return
     */
    @ApiOperation("获取楼层枚举列表")
    @GetMapping("/getFloorList")
    public ResponseEntity<Object> getFloorList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> floorMap = EduFloorEnum.getAll();
        responseEntity.setData(floorMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取楼层枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取供应商类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取供应商类型枚举列表")
    @GetMapping("/getSupplierTypeList")
    public ResponseEntity<Object> getSupplierTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> supplierTypeMap = EduSupplierTypeEnum.getAll();
        responseEntity.setData(supplierTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取供应商类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取信用等级枚举列表
     *
     * @return
     */
    @ApiOperation("获取信用等级枚举列表")
    @GetMapping("/getCreditRatingList")
    public ResponseEntity<Object> getCreditRatingList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> creditRatingMap = EduCreditRatingEnum.getAll();
        responseEntity.setData(creditRatingMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取信用等级枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取供应商合作状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取供应商合作状态枚举列表")
    @GetMapping("/getSupplierStatusList")
    public ResponseEntity<Object> getSupplierStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> supplierStatusMap = EduSupplierStatusEnum.getAll();
        responseEntity.setData(supplierStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取供应商合作状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取书籍分类枚举列表
     *
     * @return
     */
    @ApiOperation("获取书籍分类枚举列表")
    @GetMapping("/getBookCategoryList")
    public ResponseEntity<Object> getBookCategoryList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> bookCategoryMap = EduBookCategoryEnum.getAll();
        responseEntity.setData(bookCategoryMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取书籍分类枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取书籍年级编号枚举列表
     *
     * @return
     */
    @ApiOperation("获取书籍年级编号枚举列表")
    @GetMapping("/getBookGradeLevelList")
    public ResponseEntity<Object> getBookGradeLevelList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> bookGradeLevelMap = EduBookGradeLevelEnum.getAll();
        responseEntity.setData(bookGradeLevelMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取书籍年级编号枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取书籍状况枚举列表
     *
     * @return
     */
    @ApiOperation("获取书籍状况枚举列表")
    @GetMapping("/getBookConditionList")
    public ResponseEntity<Object> getBookConditionList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> bookConditionMap = EduBookConditionEnum.getAll();
        responseEntity.setData(bookConditionMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取书籍状况枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取书籍状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取书籍状态枚举列表")
    @GetMapping("/getBookStatusList")
    public ResponseEntity<Object> getBookStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> bookStatusMap = EduBookStatusEnum.getAll();
        responseEntity.setData(bookStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取书籍状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取领用用途枚举列表
     *
     * @return
     */
    @ApiOperation("获取领用用途枚举列表")
    @GetMapping("/getBorrowPurposeList")
    public ResponseEntity<Object> getBorrowPurposeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> borrowPurposeMap = EduBorrowPurposeEnum.getAll();
        responseEntity.setData(borrowPurposeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取领用用途枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取领用状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取领用状态枚举列表")
    @GetMapping("/getBorrowStatusList")
    public ResponseEntity<Object> getBorrowStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> borrowStatusMap = EduBorrowStatusEnum.getAll();
        responseEntity.setData(borrowStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取领用状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取归还状况枚举列表
     *
     * @return
     */
    @ApiOperation("获取归还状况枚举列表")
    @GetMapping("/getReturnConditionList")
    public ResponseEntity<Object> getReturnConditionList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> returnConditionMap = EduReturnConditionEnum.getAll();
        responseEntity.setData(returnConditionMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取归还状况枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取质量检查结果枚举列表
     *
     * @return
     */
    @ApiOperation("获取质量检查结果枚举列表")
    @GetMapping("/getQualityCheckResultList")
    public ResponseEntity<Object> getQualityCheckResultList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> qualityCheckResultMap = EduQualityCheckResultEnum.getAll();
        responseEntity.setData(qualityCheckResultMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取质量检查结果枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取入库类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取入库类型枚举列表")
    @GetMapping("/getInboundTypeList")
    public ResponseEntity<Object> getInboundTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> inboundTypeMap = EduInboundTypeEnum.getAll();
        responseEntity.setData(inboundTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取入库类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取支付类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取支付类型枚举列表")
    @GetMapping("/getPaymentTypeList")
    public ResponseEntity<Object> getPaymentTypeList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> paymentTypeMap = EduPaymentTypeEnum.getAll();
        responseEntity.setData(paymentTypeMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取支付类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取支付方式枚举列表
     *
     * @return
     */
    @ApiOperation("获取支付方式枚举列表")
    @GetMapping("/getPaymentMethodList")
    public ResponseEntity<Object> getPaymentMethodList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> paymentMethodMap = EduPaymentMethodEnum.getAll();
        responseEntity.setData(paymentMethodMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取支付方式枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取支付状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取支付状态枚举列表")
    @GetMapping("/getPaymentStatusList")
    public ResponseEntity<Object> getPaymentStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> paymentStatusMap = EduPaymentStatusEnum.getAll();
        responseEntity.setData(paymentStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取支付状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取合同状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取合同状态枚举列表")
    @GetMapping("/getContractStatusList")
    public ResponseEntity<Object> getContractStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> contractStatusMap = EduContractStatusEnum.getAll();
        responseEntity.setData(contractStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取合同状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取货币类型枚举列表
     *
     * @return
     */
    @ApiOperation("获取货币类型枚举列表")
    @GetMapping("/getCurrencyList")
    public ResponseEntity<Object> getCurrencyList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> currencyMap = EduCurrencyEnum.getAll();
        responseEntity.setData(currencyMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取货币类型枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取采购付款方式枚举列表
     *
     * @return
     */
    @ApiOperation("获取采购付款方式枚举列表")
    @GetMapping("/getPurchasePaymentMethodList")
    public ResponseEntity<Object> getPurchasePaymentMethodList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> purchasePaymentMethodMap = EduPurchasePaymentMethodEnum.getAll();
        responseEntity.setData(purchasePaymentMethodMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取采购付款方式枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取采购状态枚举列表
     *
     * @return
     */
    @ApiOperation("获取采购状态枚举列表")
    @GetMapping("/getPurchaseStatusList")
    public ResponseEntity<Object> getPurchaseStatusList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> purchaseStatusMap = EduPurchaseStatusEnum.getAll();
        responseEntity.setData(purchaseStatusMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取采购状态枚举列表成功");
        return responseEntity;
    }

    /**
     * 获取紧急程度枚举列表
     *
     * @return
     */
    @ApiOperation("获取紧急程度枚举列表")
    @GetMapping("/getUrgentLevelList")
    public ResponseEntity<Object> getUrgentLevelList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        Map<String, String> urgentLevelMap = EduUrgentLevelEnum.getAll();
        responseEntity.setData(urgentLevelMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取紧急程度枚举列表成功");
        return responseEntity;
    }
}