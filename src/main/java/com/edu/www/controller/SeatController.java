package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.ExamService;
import com.edu.www.service.SeatService;
import com.edu.www.vo.ExamVO;
import com.edu.www.vo.SeatVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 考试信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "座位表接口")
@RequestMapping("/seat")
public class SeatController {

    @Autowired
    private SeatService seatService;

    /**
     * 根据选择的科目信息查询学生信息
     *
     * @param request
     * @param seatVO
     * @return
     */
    @ApiOperation("根据选择的科目信息查询学生信息")
    @PostMapping("/query")
    public ResponseEntity<Object> query(HttpServletRequest request, @RequestBody SeatVO seatVO) {
        return seatService.query(request, seatVO);
    }
}
