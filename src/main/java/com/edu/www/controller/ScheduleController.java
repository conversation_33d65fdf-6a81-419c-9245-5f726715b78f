package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduGradeCodeEnum;
import com.edu.www.enums.EduSemesterEnum;
import com.edu.www.enums.EduSubSubjectEnum;
import com.edu.www.service.CommonService;
import com.edu.www.service.ScheduleService;
import com.edu.www.vo.ScheduleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 课程表信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "课程表信息接口")
@RequestMapping("/schedule")
public class ScheduleController {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ScheduleService scheduleService;

    /**
     * 根据ID查询课程表信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询课程表信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return scheduleService.get(request, id);
    }

    /**
     * 分页查询课程表信息
     *
     * @param scheduleVO
     * @return
     */
    @ApiOperation("分页查询课程表信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody ScheduleVO scheduleVO) {
        return scheduleService.queryPage(request, scheduleVO);
    }

    /**
     * 个人课表
     *
     * @return
     */
    @ApiOperation("个人课表")
    @GetMapping("/self")
    public ResponseEntity<Object> self() {
        ScheduleVO scheduleVO = new ScheduleVO();
        scheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleVO.setSemester(EduSemesterEnum.SECOND.getKey());
        scheduleVO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        return scheduleService.self(scheduleVO);
    }

    /**
     * 自习课表信息
     *
     * @return
     */
    @ApiOperation("自习课表信息")
    @GetMapping("/selfStudy")
    public ResponseEntity<Object> selfStudy() {
        ScheduleVO scheduleVO = new ScheduleVO();
        scheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleVO.setSemester(EduSemesterEnum.SECOND.getKey());
        scheduleVO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        scheduleVO.setSubjectName(EduSubSubjectEnum.SELF_STUDY.getKey());
        return scheduleService.selfStudy(scheduleVO);
    }

    /**
     * 春季班课表信息
     *
     * @return
     */
    @ApiOperation("春季班课表信息")
    @GetMapping("/spring")
    public ResponseEntity<Object> spring() {
        ScheduleVO scheduleVO = new ScheduleVO();
        scheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleVO.setSemester(EduSemesterEnum.SECOND.getKey());
        scheduleVO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        scheduleVO.setGradeCode(EduGradeCodeEnum.SPRING.getKey());
        return scheduleService.spring(scheduleVO);
    }

    /**
     * 10年级课表信息
     *
     * @return
     */
    @ApiOperation("10年级课表信息")
    @GetMapping("/yearTen")
    public ResponseEntity<Object> yearTen() {
        ScheduleVO scheduleVO = new ScheduleVO();
        scheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleVO.setSemester(EduSemesterEnum.SECOND.getKey());
        scheduleVO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        scheduleVO.setGradeCode(EduGradeCodeEnum.GRADE_10.getKey());
        return scheduleService.yearTen(scheduleVO);
    }

    /**
     * 11年级课表信息
     *
     * @return
     */
    @ApiOperation("11年级课表信息")
    @GetMapping("/yearEleven")
    public ResponseEntity<Object> yearEleven() {
        ScheduleVO scheduleVO = new ScheduleVO();
        scheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleVO.setSemester(EduSemesterEnum.SECOND.getKey());
        scheduleVO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        scheduleVO.setGradeCode(EduGradeCodeEnum.GRADE_11.getKey());
        return scheduleService.yearEleven(scheduleVO);
    }

    /**
     * 12年级课表信息
     *
     * @return
     */
    @ApiOperation("12年级课表信息")
    @GetMapping("/yearTwelve")
    public ResponseEntity<Object> yearTwelve() {
        ScheduleVO scheduleVO = new ScheduleVO();
        scheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleVO.setSemester(EduSemesterEnum.SECOND.getKey());
        scheduleVO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        scheduleVO.setGradeCode(EduGradeCodeEnum.GRADE_12.getKey());
        return scheduleService.yearTwelve(scheduleVO);
    }

    /**
     * 新增课程表信息
     *
     * @param scheduleVO
     * @return
     */
    @ApiOperation("新增课程表信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody ScheduleVO scheduleVO) {
        return scheduleService.insert(request, scheduleVO);
    }

    /**
     * 修改课程表信息
     *
     * @param scheduleVO
     * @return
     */
    @ApiOperation("修改课程表信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody ScheduleVO scheduleVO) {
        return scheduleService.update(request, scheduleVO);
    }

    /**
     * 删除课程表信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除课程表信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return scheduleService.delete(request, id);
    }
} 