package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@RestController
@Api(tags = "文件上传管理接口")
@RequestMapping("/file")
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    /**
     * 获取文件访问URL
     *
     * @param fileId 文件ID
     * @return 文件访问URL
     */
    @ApiOperation("获取文件访问URL")
    @GetMapping("/getUrl")
    public ResponseEntity<Object> getUrl(@RequestParam("fileId") String fileId) {
        return fileUploadService.getFileUrl(fileId);
    }

    /**
     * 下载文件
     *
     * @param fileId   文件ID
     * @param response HTTP响应
     */
    @ApiOperation("下载文件")
    @GetMapping("/download")
    public void download(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        fileUploadService.download(fileId, response);
    }

    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return 上传结果
     */
    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public ResponseEntity<Object> upload(@RequestParam("file") MultipartFile file) {
        return fileUploadService.uploadFile(file);
    }

    /**
     * 批量上传文件
     *
     * @param files 上传的文件数组
     * @return 上传结果
     */
    @ApiOperation("批量上传文件")
    @PostMapping("/batchUpload")
    public ResponseEntity<Object> batchUpload(@RequestParam("files") MultipartFile[] files) {
        return fileUploadService.batchUploadFile(files);
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 删除结果
     */
    @ApiOperation("删除文件")
    @DeleteMapping("/delete")
    public ResponseEntity<Object> delete(@RequestParam("fileId") String fileId) {
        return fileUploadService.deleteFile(fileId);
    }

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID数组，逗号分隔
     * @return 删除结果
     */
    @ApiOperation("批量删除文件")
    @PostMapping("/batchDelete")
    public ResponseEntity<Object> batchDelete(@RequestParam("fileIds") List<String> fileIds) {
        return fileUploadService.batchDeleteFiles(fileIds);
    }

    /**
     * 预览文件
     * 优先从本地存储查找图片，如果找不到则从GitHub获取
     *
     * @param fileId   文件ID
     * @param response HTTP响应
     */
    @ApiOperation("预览文件")
    @GetMapping("/preview")
    public void preview(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        fileUploadService.preview(fileId, response);
    }
}
