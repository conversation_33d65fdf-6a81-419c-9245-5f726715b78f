package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.PurchaseOrderService;
import com.edu.www.vo.PurchaseOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购订单管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "采购订单管理接口")
@RequestMapping("/purchase/order")
public class PurchaseOrderController {

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    /**
     * 根据ID查询采购订单信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询采购订单信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return purchaseOrderService.get(request, id);
    }

    /**
     * 分页查询采购订单信息
     *
     * @param purchaseOrderVO
     * @return
     */
    @ApiOperation("分页查询采购订单信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody PurchaseOrderVO purchaseOrderVO) {
        return purchaseOrderService.queryPage(request, purchaseOrderVO);
    }

    /**
     * 新增采购订单信息
     *
     * @param purchaseOrderVO
     * @return
     */
    @ApiOperation("新增采购订单信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody PurchaseOrderVO purchaseOrderVO) {
        return purchaseOrderService.insert(request, purchaseOrderVO);
    }

    /**
     * 修改采购订单信息
     *
     * @param purchaseOrderVO
     * @return
     */
    @ApiOperation("修改采购订单信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody PurchaseOrderVO purchaseOrderVO) {
        return purchaseOrderService.update(request, purchaseOrderVO);
    }

    /**
     * 删除采购订单信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除采购订单信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return purchaseOrderService.delete(request, id);
    }
}
