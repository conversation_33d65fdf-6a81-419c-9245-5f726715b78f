package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.MenuService;
import com.edu.www.vo.MenuVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 菜单信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@RestController
@Api(tags = "菜单管理接口")
@RequestMapping("/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;

    /**
     * 根据ID查询菜单信息
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    @ApiOperation("根据ID查询菜单信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return menuService.get(request, id);
    }

    /**
     * 分页查询菜单信息
     *
     * @param menuVO 查询条件
     * @return 菜单列表
     */
    @ApiOperation("分页查询菜单信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody MenuVO menuVO) {
        return menuService.queryPage(request, menuVO);
    }

    /**
     * 根据用户ID查询菜单树结构
     *
     * @param userId 用户ID
     * @return 菜单树
     */
    @ApiOperation("根据用户ID查询菜单树结构")
    @GetMapping("/queryMenuTreeByUserId")
    public ResponseEntity<Object> queryMenuTreeByUserId(@RequestParam("userId") String userId) {
        return menuService.queryMenuTreeByUserId(userId);
    }

    /**
     * 新增菜单
     *
     * @param menuVO 菜单信息
     * @return 结果
     */
    @ApiOperation("新增菜单")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody MenuVO menuVO) {
        return menuService.insert(request, menuVO);
    }

    /**
     * 更新菜单
     *
     * @param menuVO 菜单信息
     * @return 结果
     */
    @ApiOperation("更新菜单")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody MenuVO menuVO) {
        return menuService.update(request, menuVO);
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 结果
     */
    @ApiOperation("删除菜单")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return menuService.delete(request, id);
    }
} 