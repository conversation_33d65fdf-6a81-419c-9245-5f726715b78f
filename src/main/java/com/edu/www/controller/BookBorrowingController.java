package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.BookBorrowingService;
import com.edu.www.vo.BookBorrowingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 书籍领用管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "书籍领用管理接口")
@RequestMapping("/bookBorrowing")
public class BookBorrowingController {

    @Autowired
    private BookBorrowingService bookBorrowingService;

    /**
     * 根据ID查询书籍领用信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询书籍领用信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return bookBorrowingService.get(request, id);
    }

    /**
     * 分页查询书籍领用信息
     *
     * @param bookBorrowingVO
     * @return
     */
    @ApiOperation("分页查询书籍领用信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody BookBorrowingVO bookBorrowingVO) {
        return bookBorrowingService.queryPage(request, bookBorrowingVO);
    }

    /**
     * 新增书籍领用信息
     *
     * @param bookBorrowingVO
     * @return
     */
    @ApiOperation("新增书籍领用信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody BookBorrowingVO bookBorrowingVO) {
        return bookBorrowingService.insert(request, bookBorrowingVO);
    }

    /**
     * 修改书籍领用信息
     *
     * @param bookBorrowingVO
     * @return
     */
    @ApiOperation("修改书籍领用信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody BookBorrowingVO bookBorrowingVO) {
        return bookBorrowingService.update(request, bookBorrowingVO);
    }

    /**
     * 删除书籍领用信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除书籍领用信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return bookBorrowingService.delete(request, id);
    }
}
