package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.DutyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 值日信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "值日接口")
@RequestMapping("/duty")
public class DutyController {

    private static final Logger logger = LoggerFactory.getLogger(DutyController.class);

    @Autowired
    private DutyService dutyService;

    /**
     * 查询三个月的值日数据(上月、本月、下月)
     *
     * @param request
     * @return
     */
    @ApiOperation("查询三个月的值日数据")
    @GetMapping("/query")
    public ResponseEntity<Object> query(HttpServletRequest request) {
        return dutyService.query(request);
    }

    /**
     * 查询三个月的值日数据详情(带ID格式)
     *
     * @param request
     * @return
     */
    @ApiOperation("查询三个月的值日数据详情")
    @GetMapping("/detail")
    public ResponseEntity<Object> detail(HttpServletRequest request) {
        return dutyService.detail(request);
    }

    /**
     * 预览自动填充结果
     *
     * @param request
     * @return
     */
    @ApiOperation("预览自动填充结果")
    @GetMapping("/previewAutoFill")
    public ResponseEntity<Object> previewAutoFill(HttpServletRequest request) {
        return dutyService.previewAutoFill(request);
    }

    /**
     * 自动填充下个月值日数据
     *
     * @param request
     * @return
     */
    @ApiOperation("自动填充下个月值日数据")
    @PostMapping("/autoFillNextMonth")
    public ResponseEntity<Object> autoFillNextMonth(HttpServletRequest request) {
        return dutyService.autoFillNextMonth(request);
    }

    /**
     * 导出值日信息到Excel
     *
     * @param request
     * @param response
     * @return
     */
    @ApiOperation("导出值日信息到Excel")
    @GetMapping("/export")
    public ResponseEntity<Object> export(HttpServletRequest request, HttpServletResponse response) {
        return dutyService.exportDutyInfoToExcel(request, response);
    }

    /**
     * 置换表格内容信息
     *
     * @param masterId
     * @param slaveId
     * @return
     */
    @ApiOperation(value = "置换表格内容信息", notes = "置换表格内容信息")
    @GetMapping("/swap")
    public ResponseEntity<Object> swap(HttpServletRequest request,
                                       @RequestParam("masterId") String masterId,
                                       @RequestParam("slaveId") String slaveId) {
        return dutyService.swap(request, masterId, slaveId);
    }

    /**
     * 统计值日信息
     * @param request
     * @param startDate
     * @param endDate
     * @return
     */
    @ApiOperation(value = "统计值日信息", notes = "统计值日信息")
    @GetMapping("/statistic")
    public ResponseEntity<Object> statistic(HttpServletRequest request,
                                       @RequestParam("startDate") String startDate,
                                       @RequestParam("endDate") String endDate) {
        return dutyService.statistic(request, startDate, endDate);
    }

    /**
     * 导出值日统计信息到Excel
     * @param request
     * @param response
     * @param startDate
     * @param endDate
     * @return
     */
    @ApiOperation(value = "导出值日统计信息到Excel", notes = "导出值日统计信息到Excel")
    @GetMapping("/exportStatistic")
    public ResponseEntity<Object> exportStatistic(HttpServletRequest request,
                                                 HttpServletResponse response,
                                                 @RequestParam("startDate") String startDate,
                                                 @RequestParam("endDate") String endDate) {
        return dutyService.exportStatisticToExcel(request, response, startDate, endDate);
    }
}
