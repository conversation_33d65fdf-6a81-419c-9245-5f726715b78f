package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.PurchaseOrderDetailService;
import com.edu.www.vo.PurchaseOrderDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购明细管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "采购明细管理接口")
@RequestMapping("/purchase/order/detail")
public class PurchaseOrderDetailController {

    @Autowired
    private PurchaseOrderDetailService purchaseOrderDetailService;

    /**
     * 根据ID查询采购明细信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询采购明细信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return purchaseOrderDetailService.get(request, id);
    }

    /**
     * 根据采购单号查询采购详细信息
     *
     * @param request
     * @param purchaseNo
     * @return
     */
    @ApiOperation("根据采购单号查询采购详细信息")
    @GetMapping("/getDetail")
    public ResponseEntity<Object> getDetailByPurchaseNo(HttpServletRequest request, @RequestParam("purchaseNo") String purchaseNo) {
        return purchaseOrderDetailService.getDetailByPurchaseNo(request, purchaseNo);
    }

    /**
     * 分页查询采购明细信息
     *
     * @param purchaseOrderDetailVO
     * @return
     */
    @ApiOperation("分页查询采购明细信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody PurchaseOrderDetailVO purchaseOrderDetailVO) {
        return purchaseOrderDetailService.queryPage(request, purchaseOrderDetailVO);
    }

    /**
     * 新增采购明细信息
     *
     * @param purchaseOrderDetailVO
     * @return
     */
    @ApiOperation("新增采购明细信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody PurchaseOrderDetailVO purchaseOrderDetailVO) {
        return purchaseOrderDetailService.insert(request, purchaseOrderDetailVO);
    }

    /**
     * 修改采购明细信息
     *
     * @param purchaseOrderDetailVO
     * @return
     */
    @ApiOperation("修改采购明细信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody PurchaseOrderDetailVO purchaseOrderDetailVO) {
        return purchaseOrderDetailService.update(request, purchaseOrderDetailVO);
    }

    /**
     * 删除采购明细信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除采购明细信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return purchaseOrderDetailService.delete(request, id);
    }
}
