package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.Constant;
import com.edu.www.service.BookService;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.BookVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 书籍信息管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "书籍信息接口")
@RequestMapping("/book")
public class BookController {

    @Autowired
    private BookService bookService;

    /**
     * 根据ID查询书籍信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询书籍信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return bookService.get(request, id);
    }

    /**
     * 分页查询书籍信息
     *
     * @param bookVO
     * @return
     */
    @ApiOperation("分页查询书籍信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody BookVO bookVO) {
        return bookService.queryPage(request, bookVO);
    }

    /**
     * 新增书籍信息
     *
     * @param request             HTTP请求
     * @param bookName            书名
     * @param author              作者
     * @param isbn                ISBN编号
     * @param publisher           出版社
     * @param publicationDate     出版日期
     * @param bookCategory        书籍分类
     * @param subjectCode         学科编号
     * @param gradeLevel          年级编号
     * @param gradeLevels         年级列表（逗号分隔）
     * @param price               单价
     * @param purchasePrice       采购价
     * @param supplierId          供应商ID
     * @param currentStock        当前库存
     * @param description         描述
     * @param frontCoverImageFile 封面图片文件
     * @param backCoverImageFile  封底图片文件
     * @return
     */
    @ApiOperation("新增书籍信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(
            HttpServletRequest request,
            @ApiParam(value = "书名", required = true) @RequestParam("bookName") String bookName,
            @ApiParam(value = "作者") @RequestParam(value = "author", required = false) String author,
            @ApiParam(value = "ISBN编号", required = true) @RequestParam("isbn") String isbn,
            @ApiParam(value = "出版社") @RequestParam(value = "publisher", required = false) String publisher,
            @ApiParam(value = "出版日期") @RequestParam(value = "publicationDate", required = false) String publicationDate,
            @ApiParam(value = "书籍分类", required = true) @RequestParam("bookCategory") String bookCategory,
            @ApiParam(value = "学科编号") @RequestParam(value = "subjectCode", required = false) String subjectCode,
            @ApiParam(value = "年级编号") @RequestParam(value = "gradeLevel", required = false) String gradeLevel,
            @ApiParam(value = "年级列表") @RequestParam(value = "gradeLevels", required = false) String gradeLevels,
            @ApiParam(value = "单价") @RequestParam(value = "price", required = false) String price,
            @ApiParam(value = "采购价") @RequestParam(value = "purchasePrice", required = false) String purchasePrice,
            @ApiParam(value = "供应商ID") @RequestParam(value = "supplierId", required = false) String supplierId,
            @ApiParam(value = "当前库存") @RequestParam(value = "currentStock", required = false) String currentStock,
            @ApiParam(value = "描述") @RequestParam(value = "description", required = false) String description,
            @ApiParam(value = "封面图片文件") @RequestParam(value = "frontCoverImageFile", required = false) MultipartFile frontCoverImageFile,
            @ApiParam(value = "封底图片文件") @RequestParam(value = "backCoverImageFile", required = false) MultipartFile backCoverImageFile) {

        try {
            // 构建BookVO对象
            BookVO bookVO = new BookVO();
            bookVO.setBookName(bookName);
            bookVO.setAuthor(author);
            bookVO.setIsbn(isbn);
            bookVO.setPublisher(publisher);
            bookVO.setBookCategory(bookCategory);
            bookVO.setSubjectCode(subjectCode);
            bookVO.setGradeLevel(gradeLevel);
            bookVO.setDescription(description);
            bookVO.setSupplierId(supplierId);

            // 处理年级列表
            if (gradeLevels != null && !gradeLevels.trim().isEmpty()) {
                bookVO.setGradeLevels(Arrays.asList(gradeLevels.split(Constant.DELIMITER_COMMA)));
            }

            // 设置文件
            bookVO.setFrontCoverImageFile(frontCoverImageFile);
            bookVO.setBackCoverImageFile(backCoverImageFile);

            // 处理数值类型字段
            if (price != null && !price.trim().isEmpty()) {
                bookVO.setPrice(new BigDecimal(price));
            }

            if (purchasePrice != null && !purchasePrice.trim().isEmpty()) {
                bookVO.setPurchasePrice(new BigDecimal(purchasePrice));
            }

            if (currentStock != null && !currentStock.trim().isEmpty()) {
                bookVO.setCurrentStock(Integer.parseInt(currentStock));
            }

            if (publicationDate != null && !publicationDate.trim().isEmpty()) {
                bookVO.setPublicationDate(DateUtil.stringToDate(publicationDate, DateUtil.FORMAT_DATE_SHORT));
            }

            return bookService.insert(request, bookVO);

        } catch (NumberFormatException e) {
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("数值格式不正确：" + e.getMessage());
            return errorResponse;
        } catch (Exception e) {
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("参数处理失败：" + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 修改书籍信息
     *
     * @param request             HTTP请求
     * @param id                  书籍ID
     * @param bookName            书名
     * @param author              作者
     * @param isbn                ISBN编号
     * @param publisher           出版社
     * @param publicationDate     出版日期
     * @param bookCategory        书籍分类
     * @param subjectCode         学科编号
     * @param gradeLevel          年级编号
     * @param gradeLevels         年级列表（逗号分隔）
     * @param price               单价
     * @param purchasePrice       采购价
     * @param supplierId          供应商ID
     * @param currentStock        当前库存
     * @param description         描述
     * @param frontCoverImageFile 封面图片文件
     * @param backCoverImageFile  封底图片文件
     * @return
     */
    @ApiOperation("修改书籍信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(
            HttpServletRequest request,
            @ApiParam(value = "书籍ID", required = true) @RequestParam("id") String id,
            @ApiParam(value = "书名", required = true) @RequestParam("bookName") String bookName,
            @ApiParam(value = "作者") @RequestParam(value = "author", required = false) String author,
            @ApiParam(value = "ISBN编号", required = true) @RequestParam("isbn") String isbn,
            @ApiParam(value = "出版社") @RequestParam(value = "publisher", required = false) String publisher,
            @ApiParam(value = "出版日期") @RequestParam(value = "publicationDate", required = false) String publicationDate,
            @ApiParam(value = "书籍分类", required = true) @RequestParam("bookCategory") String bookCategory,
            @ApiParam(value = "学科编号") @RequestParam(value = "subjectCode", required = false) String subjectCode,
            @ApiParam(value = "年级编号") @RequestParam(value = "gradeLevel", required = false) String gradeLevel,
            @ApiParam(value = "年级列表") @RequestParam(value = "gradeLevels", required = false) String gradeLevels,
            @ApiParam(value = "单价") @RequestParam(value = "price", required = false) String price,
            @ApiParam(value = "采购价") @RequestParam(value = "purchasePrice", required = false) String purchasePrice,
            @ApiParam(value = "供应商ID") @RequestParam(value = "supplierId", required = false) String supplierId,
            @ApiParam(value = "当前库存") @RequestParam(value = "currentStock", required = false) String currentStock,
            @ApiParam(value = "描述") @RequestParam(value = "description", required = false) String description,
            @ApiParam(value = "封面图片文件") @RequestParam(value = "frontCoverImageFile", required = false) MultipartFile frontCoverImageFile,
            @ApiParam(value = "封底图片文件") @RequestParam(value = "backCoverImageFile", required = false) MultipartFile backCoverImageFile) {

        try {
            // 构建BookVO对象
            BookVO bookVO = new BookVO();
            bookVO.setId(id);
            bookVO.setBookName(bookName);
            bookVO.setAuthor(author);
            bookVO.setIsbn(isbn);
            bookVO.setPublisher(publisher);
            bookVO.setBookCategory(bookCategory);
            bookVO.setSubjectCode(subjectCode);
            bookVO.setGradeLevel(gradeLevel);
            bookVO.setDescription(description);
            bookVO.setSupplierId(supplierId);

            // 处理年级列表
            if (gradeLevels != null && !gradeLevels.trim().isEmpty()) {
                bookVO.setGradeLevels(Arrays.asList(gradeLevels.split(Constant.DELIMITER_COMMA)));
            }

            // 设置文件
            bookVO.setFrontCoverImageFile(frontCoverImageFile);
            bookVO.setBackCoverImageFile(backCoverImageFile);

            // 处理数值类型字段
            if (price != null && !price.trim().isEmpty()) {
                bookVO.setPrice(new BigDecimal(price));
            }

            if (purchasePrice != null && !purchasePrice.trim().isEmpty()) {
                bookVO.setPurchasePrice(new BigDecimal(purchasePrice));
            }

            if (currentStock != null && !currentStock.trim().isEmpty()) {
                bookVO.setCurrentStock(Integer.parseInt(currentStock));
            }

            if (publicationDate != null && !publicationDate.trim().isEmpty()) {
                bookVO.setPublicationDate(DateUtil.stringToDate(publicationDate, DateUtil.FORMAT_DATE_SHORT));
            }

            return bookService.update(request, bookVO);

        } catch (NumberFormatException e) {
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("数值格式不正确：" + e.getMessage());
            return errorResponse;
        } catch (Exception e) {
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("参数处理失败：" + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 删除书籍信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除书籍信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return bookService.delete(request, id);
    }
}
