package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.convert.BookConverter;
import com.edu.www.service.BookService;
import com.edu.www.vo.BookVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 书籍信息管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "书籍信息接口")
@RequestMapping("/book")
public class BookController {

    @Autowired
    private BookService bookService;

    /**
     * 根据ID查询书籍信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询书籍信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return bookService.get(request, id);
    }

    @ApiOperation("查询书籍信息")
    @GetMapping("/getBook")
    public ResponseEntity<Object> getBook() {
        return bookService.getBook();
    }

    /**
     * 分页查询书籍信息
     *
     * @param bookVO
     * @return
     */
    @ApiOperation("分页查询书籍信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody BookVO bookVO) {
        return bookService.queryPage(request, bookVO);
    }

    /**
     * 新增书籍信息
     * 支持BookVO的所有字段，通过BookConverter进行参数转换
     *
     * @param request             HTTP请求
     * @param frontCoverImageFile 封面图片文件
     * @param backCoverImageFile  封底图片文件
     * @return
     */
    @ApiOperation("新增书籍信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(
            HttpServletRequest request,
            @ApiParam(value = "封面图片文件") @RequestParam(value = "frontCoverImageFile", required = false) MultipartFile frontCoverImageFile,
            @ApiParam(value = "封底图片文件") @RequestParam(value = "backCoverImageFile", required = false) MultipartFile backCoverImageFile) {

        try {
            // 使用BookConverter直接从request转换，支持BookVO的所有字段
            BookVO bookVO = BookConverter.convertFromRequest(request, frontCoverImageFile, backCoverImageFile);

            return bookService.insert(request, bookVO);

        } catch (Exception e) {
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("参数处理失败：" + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 修改书籍信息
     * 支持BookVO的所有字段，通过BookConverter进行参数转换
     *
     * @param request             HTTP请求
     * @param frontCoverImageFile 封面图片文件
     * @param backCoverImageFile  封底图片文件
     * @return
     */
    @ApiOperation("修改书籍信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(
            HttpServletRequest request,
            @ApiParam(value = "封面图片文件") @RequestParam(value = "frontCoverImageFile", required = false) MultipartFile frontCoverImageFile,
            @ApiParam(value = "封底图片文件") @RequestParam(value = "backCoverImageFile", required = false) MultipartFile backCoverImageFile) {

        try {
            // 使用BookConverter直接从request转换，支持BookVO的所有字段
            BookVO bookVO = BookConverter.convertFromRequest(request, frontCoverImageFile, backCoverImageFile);

            return bookService.update(request, bookVO);

        } catch (Exception e) {
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("参数处理失败：" + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 删除书籍信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除书籍信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return bookService.delete(request, id);
    }
}
