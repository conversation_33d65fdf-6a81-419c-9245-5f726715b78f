package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.UserService;
import com.edu.www.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 用户信息 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "用户接口")
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 根据ID查询用户信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询用户信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return userService.get(request, id);
    }

    /**
     * 分页查询用户信息
     *
     * @param userVO
     * @return
     */
    @ApiOperation("分页查询用户信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody UserVO userVO) {
        return userService.queryPage(request, userVO);
    }

    /**
     * 用户注册
     *
     * @param userVO
     * @return
     */
    @ApiOperation("用户注册")
    @PostMapping("/register")
    public ResponseEntity<Object> register(@RequestBody UserVO userVO) {
        return userService.register(userVO);
    }

    /**
     * 新增用户
     *
     * @param userVO
     * @return
     */
    @ApiOperation("新增用户")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody UserVO userVO) {
        return userService.insert(request, userVO);
    }

    /**
     * 用户修改信息
     *
     * @param userVO
     * @return
     */
    @ApiOperation("用户修改信息")
    @PostMapping("/modify")
    public ResponseEntity<Object> modify(@RequestBody UserVO userVO) {
        return userService.modify(userVO);
    }

    /**
     * 修改用户信息
     *
     * @param request
     * @param userVO
     * @return
     */
    @ApiOperation("修改用户信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody UserVO userVO) {
        return userService.update(request, userVO);
    }

    /**
     * 修改密码
     *
     * @param id
     * @param oldPwd
     * @param newPwd
     * @return
     */
    @ApiOperation("修改密码")
    @GetMapping("/modifyPwd")
    public ResponseEntity<Object> modifyPwd(@RequestParam("id") String id,
                                            @RequestParam("oldPwd") String oldPwd,
                                            @RequestParam("newPwd") String newPwd) {
        return userService.modifyPwd(id, oldPwd, newPwd);
    }

    /**
     * 修改密码
     *
     * @param request
     * @param id
     * @param oldPwd
     * @param newPwd
     * @return
     */
    @ApiOperation("修改密码")
    @GetMapping("/updatePwd")
    public ResponseEntity<Object> updatePwd(HttpServletRequest request,
                                            @RequestParam("id") String id,
                                            @RequestParam("oldPwd") String oldPwd,
                                            @RequestParam("newPwd") String newPwd) {
        return userService.updatePwd(request, id, oldPwd, newPwd);
    }


    /**
     * 用户登录
     *
     * @param userVO
     * @return
     */
    @ApiOperation("用户登录")
    @PostMapping(value = "/login")
    public ResponseEntity<Object> login(@RequestBody UserVO userVO) {
        return userService.login(userVO);
    }


    @ApiOperation("用户登出")
    @GetMapping(value = "/logout")
    public ResponseEntity<Object> login(String username) {
        return userService.logout(username);
    }

    /**
     * 用户注销
     *
     * @param id
     * @return
     */
    @ApiOperation("用户注销")
    @GetMapping("/logoff")
    public ResponseEntity<Object> logoff(@RequestParam("id") String id) {
        return userService.logoff(id);
    }

    /**
     * 删除用户
     *
     * @param request
     * @param id
     * @return
     */
    @ApiOperation("删除用户")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return userService.delete(request, id);
    }

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     * @return 结果
     */
    @ApiOperation("为用户分配角色")
    @PostMapping("/assignRole")
    public ResponseEntity<Object> assignRole(@RequestParam("userId") String userId, @RequestBody List<String> roleIds) {
        return userService.assignRole(userId, roleIds);
    }

    /**
     * 查询用户角色列表
     *
     * @param userId
     * @return
     */
    @ApiOperation("查询用户角色列表")
    @GetMapping("/queryUserRole")
    public ResponseEntity<Object> queryUserRole(@RequestParam("userId") String userId) {
        return userService.queryUserRole(userId);
    }
}
