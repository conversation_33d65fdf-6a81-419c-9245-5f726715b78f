package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.PermissionService;
import com.edu.www.vo.PermissionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 权限信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@RestController
@Api(tags = "权限管理接口")
@RequestMapping("/permission")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 根据ID查询权限信息
     *
     * @param id 权限ID
     * @return 权限信息
     */
    @ApiOperation("根据ID查询权限信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return permissionService.get(request, id);
    }

    /**
     * 分页查询权限信息
     *
     * @param permissionVO 查询条件
     * @return 权限列表
     */
    @ApiOperation("分页查询权限信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody PermissionVO permissionVO) {
        return permissionService.queryPage(request, permissionVO);
    }


    /**
     * 分页查询权限信息
     *
     * @param request
     * @param permissionVO
     * @return
     */
    @ApiOperation("分页查询权限信息")
    @PostMapping("/queryByPage")
    public ResponseEntity<Object> queryByPage(HttpServletRequest request, @RequestBody PermissionVO permissionVO) {
        return permissionService.queryByPage(request, permissionVO);
    }

    /**
     * 新增权限
     *
     * @param permissionVO 权限信息
     * @return 结果
     */
    @ApiOperation("新增权限")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody PermissionVO permissionVO) {
        return permissionService.insert(request, permissionVO);
    }

    /**
     * 更新权限
     *
     * @param permissionVO 权限信息
     * @return 结果
     */
    @ApiOperation("更新权限")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody PermissionVO permissionVO) {
        return permissionService.update(request, permissionVO);
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 结果
     */
    @ApiOperation("删除权限")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return permissionService.delete(request, id);
    }
} 