package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.RoleService;
import com.edu.www.vo.RoleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * 角色信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@RestController
@Api(tags = "角色管理接口")
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 根据ID查询角色信息
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @ApiOperation("根据ID查询角色信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return roleService.get(request, id);
    }

    /**
     * 获取所有角色信息
     *
     * @return
     */
    @ApiOperation("获取所有角色信息")
    @GetMapping("/getRoleList")
    public ResponseEntity<Object> getRoleList() {
        return roleService.getRoleList();
    }

    /**
     * 分页查询角色信息
     *
     * @param roleVO 查询条件
     * @return 角色列表
     */
    @ApiOperation("分页查询角色信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody RoleVO roleVO) {
        return roleService.queryPage(request, roleVO);
    }

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @ApiOperation("根据用户ID查询角色列表")
    @GetMapping("/queryByUserId")
    public ResponseEntity<Object> queryByUserId(HttpServletRequest request, @RequestParam("userId") String userId) {
        return roleService.queryByUserId(request, userId);
    }

    /**
     * 新增角色
     *
     * @param roleVO 角色信息
     * @return 结果
     */
    @ApiOperation("新增角色")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody RoleVO roleVO) {
        return roleService.insert(request, roleVO);
    }

    /**
     * 更新角色
     *
     * @param roleVO 角色信息
     * @return 结果
     */
    @ApiOperation("更新角色")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody RoleVO roleVO) {
        return roleService.update(request, roleVO);
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 结果
     */
    @ApiOperation("删除角色")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return roleService.delete(request, id);
    }

    /**
     * 为角色分配权限
     *
     * @param roleId        角色ID
     * @param permissionIds 权限ID列表
     * @return 结果
     */
    @ApiOperation("为角色分配权限")
    @PostMapping("/assignPermissions")
    public ResponseEntity<Object> assignPermissions(HttpServletRequest request, @RequestParam("roleId") String roleId, @RequestBody Set<String> permissionIds) {
        return roleService.assignPermissions(request, roleId, permissionIds);
    }

    /**
     * 查询角色的权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @ApiOperation("查询角色的权限列表")
    @GetMapping("/queryRolePermissions")
    public ResponseEntity<Object> queryRolePermissions(HttpServletRequest request, @RequestParam("roleId") String roleId) {
        return roleService.queryRolePermissions(request, roleId);
    }
} 