package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.ClassesService;
import com.edu.www.vo.ClassesVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 班级信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "班级信息接口")
@RequestMapping("/classes")
public class ClassesController {

    @Autowired
    private ClassesService classesService;

    /**
     * 根据ID查询班级信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询班级信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return classesService.get(request, id);
    }

    /**
     * 根据部门编号查询所有班级信息
     *
     * @param deptCode
     * @return
     */
    @ApiOperation("根据部门编号查询所有班级信息")
    @GetMapping("/getClassByDeptCode")
    public ResponseEntity<Object> getAllClasses(String deptCode) {
        return classesService.getAllClasses(deptCode);
    }

    /**
     * 分页查询班级信息
     *
     * @param classesVO
     * @return
     */
    @ApiOperation("分页查询班级信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody ClassesVO classesVO) {
        return classesService.queryPage(request, classesVO);
    }

    /**
     * 新增班级信息
     *
     * @param classesVO
     * @return
     */
    @ApiOperation("新增班级信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody ClassesVO classesVO) {
        return classesService.insert(request, classesVO);
    }

    /**
     * 修改班级信息
     *
     * @param classesVO
     * @return
     */
    @ApiOperation("修改班级信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody ClassesVO classesVO) {
        return classesService.update(request, classesVO);
    }

    /**
     * 删除班级信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除班级信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return classesService.delete(request, id);
    }
} 