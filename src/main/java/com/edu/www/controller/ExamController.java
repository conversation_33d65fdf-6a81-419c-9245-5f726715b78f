package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.ExamService;
import com.edu.www.vo.ExamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 考试信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "考试信息接口")
@RequestMapping("/exam")
public class ExamController {

    @Autowired
    private ExamService examService;

    /**
     * 根据ID查询考试信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询考试信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return examService.get(request, id);
    }

    /**
     * 分页查询考试信息
     *
     * @param examVO
     * @return
     */
    @ApiOperation("分页查询考试信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody ExamVO examVO) {
        return examService.queryPage(request, examVO);
    }

    /**
     * 新增考试信息
     *
     * @param examVO
     * @return
     */
    @ApiOperation("新增考试信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody ExamVO examVO) {
        return examService.insert(request, examVO);
    }

    /**
     * 修改考试信息
     *
     * @param examVO
     * @return
     */
    @ApiOperation("修改考试信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody ExamVO examVO) {
        return examService.update(request, examVO);
    }

    /**
     * 删除考试信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除考试信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return examService.delete(request, id);
    }

    /**
     * 生成打印标签贴
     *
     * @param request
     * @param ids
     * @return
     */
    @ApiOperation("生成打印标签贴")
    @PostMapping("/tag")
    public ResponseEntity<Object> examTag(HttpServletRequest request, @RequestBody List<String> ids) {
        return examService.examTag(request, ids);
    }

    /**
     * 监考时长汇总
     *
     * @param request
     * @param examVO
     * @return
     */
    @ApiOperation("监考时长汇总")
    @PostMapping("/duration")
    public ResponseEntity<Object> duration(HttpServletRequest request, @RequestBody ExamVO examVO) {
        return examService.duration(request, examVO);
    }

    /**
     * 监考时长详情
     *
     * @param request
     * @param invigilatorId
     * @return
     */
    @ApiOperation("监考时长汇总")
    @PostMapping("/duration/detail")
    public ResponseEntity<Object> durationDetail(HttpServletRequest request, @RequestParam("invigilatorId") String invigilatorId) {
        return examService.durationDetail(request, invigilatorId);
    }
}
