package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.TmpScheduleService;
import com.edu.www.vo.TmpScheduleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 临时课程表信息管理Controller
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@RestController
@RequestMapping("/tmp/schedule")
@Api(tags = "临时课程表信息管理")
public class TmpScheduleController {

    @Autowired
    private TmpScheduleService tmpScheduleService;

    /**
     * 根据ID查询临时课程表信息
     *
     * @param id 临时课程表ID
     * @return 临时课程表信息
     */
    @ApiOperation(value = "根据ID查询临时课程表信息", notes = "根据ID查询临时课程表信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(@RequestParam("id") String id) {
        return tmpScheduleService.get(id);
    }

    /**
     * 查询临时课程表列表信息
     *
     * @param tmpScheduleVO
     * @return
     */
    @ApiOperation(value = "查询临时课程表列表信息", notes = "查询临时课程表列表信息")
    @PostMapping("/query")
    public ResponseEntity<Object> query(@RequestBody TmpScheduleVO tmpScheduleVO) {
        return tmpScheduleService.query(tmpScheduleVO);
    }

    /**
     * 新增临时课程表信息
     *
     * @param tmpScheduleVO 临时课程表信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增临时课程表信息", notes = "新增临时课程表信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(@RequestBody TmpScheduleVO tmpScheduleVO) {
        return tmpScheduleService.insert(tmpScheduleVO);
    }

    /**
     * 修改临时课程表信息
     *
     * @param tmpScheduleVO 临时课程表信息
     * @return 操作结果
     */
    @ApiOperation(value = "修改临时课程表信息", notes = "修改临时课程表信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(@RequestBody TmpScheduleVO tmpScheduleVO) {
        return tmpScheduleService.update(tmpScheduleVO);
    }

    /**
     * 批量修改临时课程表信息
     *
     * @param tmpScheduleVOList 临时课程表信息列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量修改临时课程表信息", notes = "批量修改临时课程表信息")
    @PostMapping("/batchUpdate")
    public ResponseEntity<Object> batchUpdate(@RequestBody List<TmpScheduleVO> tmpScheduleVOList) {
        return tmpScheduleService.batchUpdate(tmpScheduleVOList);
    }

    /**
     * 置换临时课程表表格内容信息
     *
     * @param masterId
     * @param slaveId
     * @return
     */
    @ApiOperation(value = "置换临时课程表表格内容信息", notes = "置换临时课程表表格内容信息")
    @GetMapping("/swap")
    public ResponseEntity<Object> swap(@RequestParam("masterId") String masterId, @RequestParam("slaveId") String slaveId) {
        return tmpScheduleService.swap(masterId, slaveId);
    }

    /**
     * 根据ID删除临时课程表信息
     *
     * @param id 临时课程表ID
     * @return 操作结果
     */
    @ApiOperation(value = "根据ID删除临时课程表信息", notes = "根据ID删除临时课程表信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(@RequestParam("id") String id) {
        return tmpScheduleService.delete(id);
    }
}