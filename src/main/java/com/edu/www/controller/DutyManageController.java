package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.DutyManageService;
import com.edu.www.vo.DutyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 值日信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "值日信息接口")
@RequestMapping("/duty/manage")
public class DutyManageController {

    @Autowired
    private DutyManageService dutyManageService;

    /**
     * 根据ID查询值日信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询值日信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return dutyManageService.get(request, id);
    }

    /**
     * 查询基组信息
     *
     * @param request
     * @return
     */
    @ApiOperation("查询基组信息")
    @GetMapping("/getBaseGroup")
    public ResponseEntity<Object> getBaseGroup(HttpServletRequest request) {
        return dutyManageService.getBaseGroup(request);
    }

    /**
     * 分页查询值日信息
     *
     * @param dutyVO
     * @return
     */
    @ApiOperation("分页查询值日信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody DutyVO dutyVO) {
        return dutyManageService.queryPage(request, dutyVO);
    }

    /**
     * 新增值日信息
     *
     * @param dutyVO
     * @return
     */
    @ApiOperation("新增值日信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody DutyVO dutyVO) {
        return dutyManageService.insert(request, dutyVO);
    }

    /**
     * 修改值日信息
     *
     * @param dutyVO
     * @return
     */
    @ApiOperation("修改值日信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody DutyVO dutyVO) {
        return dutyManageService.update(request, dutyVO);
    }

    /**
     * 删除值日信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除值日信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return dutyManageService.delete(request, id);
    }

    /**
     * 批量删除值日信息
     *
     * @param request
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除值日信息")
    @PostMapping("/batchDelete")
    public ResponseEntity<Object> batchDelete(HttpServletRequest request, @RequestBody List<String> ids) {
        return dutyManageService.batchDelete(request, ids);
    }
}
