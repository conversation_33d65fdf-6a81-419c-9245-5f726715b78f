package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.AdmissionService;
import com.edu.www.vo.AdmissionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 招生信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "招生信息接口")
@RequestMapping("/admission")
public class AdmissionController {

    @Autowired
    private AdmissionService admissionService;

    /**
     * 根据ID查询招生信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询招生信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return admissionService.get(request, id);
    }

    /**
     * 分页查询招生信息
     *
     * @param admissionVO
     * @return
     */
    @ApiOperation("分页查询招生信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody AdmissionVO admissionVO) {
        return admissionService.queryPage(request, admissionVO);
    }

    /**
     * 新增招生信息
     *
     * @param admissionVO
     * @return
     */
    @ApiOperation("新增招生信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody AdmissionVO admissionVO) {
        return admissionService.insert(request, admissionVO);
    }

    /**
     * 修改招生信息
     *
     * @param admissionVO
     * @return
     */
    @ApiOperation("修改招生信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody AdmissionVO admissionVO) {
        return admissionService.update(request, admissionVO);
    }

    /**
     * 删除招生信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除招生信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id")  String id) {
        return admissionService.delete(request, id);
    }
} 