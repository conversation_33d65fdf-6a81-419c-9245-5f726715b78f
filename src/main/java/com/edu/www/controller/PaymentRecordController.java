package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.PaymentRecordService;
import com.edu.www.vo.PaymentRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付记录管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "支付记录管理接口")
@RequestMapping("/payment/record")
public class PaymentRecordController {

    @Autowired
    private PaymentRecordService paymentRecordService;

    /**
     * 根据ID查询支付记录信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询支付记录信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return paymentRecordService.get(request, id);
    }

    /**
     * 分页查询支付记录信息
     *
     * @param paymentRecordVO
     * @return
     */
    @ApiOperation("分页查询支付记录信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody PaymentRecordVO paymentRecordVO) {
        return paymentRecordService.queryPage(request, paymentRecordVO);
    }

    /**
     * 新增支付记录信息
     *
     * @param paymentRecordVO
     * @return
     */
    @ApiOperation("新增支付记录信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody PaymentRecordVO paymentRecordVO) {
        return paymentRecordService.insert(request, paymentRecordVO);
    }

    /**
     * 修改支付记录信息
     *
     * @param paymentRecordVO
     * @return
     */
    @ApiOperation("修改支付记录信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody PaymentRecordVO paymentRecordVO) {
        return paymentRecordService.update(request, paymentRecordVO);
    }

    /**
     * 删除支付记录信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除支付记录信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return paymentRecordService.delete(request, id);
    }
}
