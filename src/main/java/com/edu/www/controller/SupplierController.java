package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.SupplierService;
import com.edu.www.vo.SupplierVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 供应商信息管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "供应商信息接口")
@RequestMapping("/supplier")
public class SupplierController {

    @Autowired
    private SupplierService supplierService;

    /**
     * 根据ID查询供应商信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询供应商信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return supplierService.get(request, id);
    }

    /**
     * 分页查询供应商信息
     *
     * @param supplierVO
     * @return
     */
    @ApiOperation("分页查询供应商信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody SupplierVO supplierVO) {
        return supplierService.queryPage(request, supplierVO);
    }

    /**
     * 新增供应商信息
     *
     * @param supplierVO
     * @return
     */
    @ApiOperation("新增供应商信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody SupplierVO supplierVO) {
        return supplierService.insert(request, supplierVO);
    }

    /**
     * 修改供应商信息
     *
     * @param supplierVO
     * @return
     */
    @ApiOperation("修改供应商信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody SupplierVO supplierVO) {
        return supplierService.update(request, supplierVO);
    }

    /**
     * 删除供应商信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除供应商信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return supplierService.delete(request, id);
    }

    /**
     * 生成供应商编码
     *
     * @return
     */
    @ApiOperation("生成供应商编码")
    @GetMapping("/generateCode")
    public ResponseEntity<Object> generateCode(HttpServletRequest request) {
        return supplierService.generateCode(request);
    }

    /**
     * 查询所有供应商列表
     *
     * @return
     */
    @ApiOperation("查询所有供应商列表")
    @GetMapping("/getAll")
    public ResponseEntity<Object> getAll() {
        return supplierService.getAll();
    }
}
