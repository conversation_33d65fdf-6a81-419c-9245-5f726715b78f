package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.ReturnRecordService;
import com.edu.www.vo.ReturnRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 归还记录管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@RestController
@Api(tags = "归还记录管理接口")
@RequestMapping("/return/record")
public class ReturnRecordController {

    @Autowired
    private ReturnRecordService returnRecordService;

    /**
     * 根据ID查询归还记录信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询归还记录信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return returnRecordService.get(request, id);
    }

    /**
     * 分页查询归还记录信息
     *
     * @param returnRecordVO
     * @return
     */
    @ApiOperation("分页查询归还记录信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody ReturnRecordVO returnRecordVO) {
        return returnRecordService.queryPage(request, returnRecordVO);
    }

    /**
     * 新增归还记录信息
     *
     * @param returnRecordVO
     * @return
     */
    @ApiOperation("新增归还记录信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody ReturnRecordVO returnRecordVO) {
        return returnRecordService.insert(request, returnRecordVO);
    }

    /**
     * 修改归还记录信息
     *
     * @param returnRecordVO
     * @return
     */
    @ApiOperation("修改归还记录信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody ReturnRecordVO returnRecordVO) {
        return returnRecordService.update(request, returnRecordVO);
    }

    /**
     * 删除归还记录信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除归还记录信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return returnRecordService.delete(request, id);
    }
}
