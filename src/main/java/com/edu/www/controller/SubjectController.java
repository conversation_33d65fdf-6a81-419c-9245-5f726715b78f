package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.SubjectService;
import com.edu.www.vo.SubjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 学科信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "学科信息接口")
@RequestMapping("/subject")
public class SubjectController {

    @Autowired
    private SubjectService subjectService;

    /**
     * 根据ID查询学科信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询学科信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return subjectService.get(request, id);
    }

    /**
     * 分页查询学科信息
     *
     * @param subjectVO
     * @return
     */
    @ApiOperation("分页查询学科信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody SubjectVO subjectVO) {
        return subjectService.queryPage(request, subjectVO);
    }

    /**
     * 新增学科信息
     *
     * @param subjectVO
     * @return
     */
    @ApiOperation("新增学科信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody SubjectVO subjectVO) {
        return subjectService.insert(request, subjectVO);
    }

    /**
     * 修改学科信息
     *
     * @param subjectVO
     * @return
     */
    @ApiOperation("修改学科信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody SubjectVO subjectVO) {
        return subjectService.update(request, subjectVO);
    }

    /**
     * 删除学科信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除学科信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return subjectService.delete(request, id);
    }
} 