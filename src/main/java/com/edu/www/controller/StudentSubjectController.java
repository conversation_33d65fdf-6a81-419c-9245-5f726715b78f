package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.StudentSubjectService;
import com.edu.www.vo.StudentSubjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 学生学科管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "学生学科接口")
@RequestMapping("/student/subject")
public class StudentSubjectController {

    @Autowired
    private StudentSubjectService studentSubjectService;

    /**
     * 根据ID查询学生学科信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询学生学科信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return studentSubjectService.get(request, id);
    }


    /**
     * 分页查询学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    @ApiOperation("分页查询学生学科信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody StudentSubjectVO studentSubjectVO) {
        return studentSubjectService.queryPage(request, studentSubjectVO);
    }

    /**
     * 新增学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    @ApiOperation("新增学生学科信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody StudentSubjectVO studentSubjectVO) {
        return studentSubjectService.insert(request, studentSubjectVO);
    }

    /**
     * 修改学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    @ApiOperation("修改学生学科信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody StudentSubjectVO studentSubjectVO) {
        return studentSubjectService.update(request, studentSubjectVO);
    }

    /**
     * 删除学生学科信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除学生学科信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return studentSubjectService.delete(request, id);
    }
} 