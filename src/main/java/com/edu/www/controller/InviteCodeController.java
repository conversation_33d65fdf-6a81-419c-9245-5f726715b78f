package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;

import com.edu.www.service.InviteCodeService;
import com.edu.www.task.InviteCodeTask;
import com.edu.www.vo.InviteCodeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 邀请码管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "邀请码接口")
@RequestMapping("/invite/code")
public class InviteCodeController {

    @Autowired
    private InviteCodeTask inviteCodeTask;

    @Autowired
    private InviteCodeService inviteCodeService;

    /**
     * 手动触发删除过期邀请码状态
     *
     * @return
     */
    @ApiOperation("手动触发删除过期邀请码状态")
    @GetMapping("/handle")
    public ResponseEntity<Object> handleDeleteExpired() {
        inviteCodeTask.deleteExpiredInviteCode();
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("已触发过期邀请码状态删除任务");
        return responseEntity;
    }

    /**
     * 根据ID查询邀请码
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询邀请码")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return inviteCodeService.get(request, id);
    }

    /**
     * 分页查询邀请码
     *
     * @param inviteCodeVO
     * @return
     */
    @ApiOperation("分页查询邀请码")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody InviteCodeVO inviteCodeVO) {
        return inviteCodeService.queryPage(request, inviteCodeVO);
    }

    /**
     * 生成邀请码
     *
     * @return
     */
    @ApiOperation("生成邀请码")
    @GetMapping("/generate")
    public ResponseEntity<Object> generate(HttpServletRequest request) {
        return inviteCodeService.generate(request);
    }

    /**
     * 修改邀请码
     *
     * @param inviteCodeVO
     * @return
     */
    @ApiOperation("修改邀请码")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody InviteCodeVO inviteCodeVO) {
        return inviteCodeService.update(request, inviteCodeVO);
    }

    /**
     * 删除邀请码
     *
     * @param id
     * @return
     */
    @ApiOperation("删除邀请码")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return inviteCodeService.delete(request, id);
    }
} 