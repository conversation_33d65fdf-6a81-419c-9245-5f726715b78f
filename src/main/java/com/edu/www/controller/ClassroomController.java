package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.ClassroomService;
import com.edu.www.vo.ClassroomVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 教室信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "教室信息接口")
@RequestMapping("/classroom")
public class ClassroomController {

    @Autowired
    private ClassroomService classroomService;

    /**
     * 根据ID查询教室信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询教室信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return classroomService.get(request, id);
    }

    /**
     * 根据部门编号查询教室信息
     *
     * @param deptCode 部门编号(SY：双语部、IC：融合部、DP：高中部)
     * @return
     */
    @ApiOperation("根据部门编号查询教室信息")
    @GetMapping("/getClassroomByDeptCode")
    public ResponseEntity<Object> getClassroomByDeptCode(String deptCode) {
        return classroomService.getClassroomByDeptCode(deptCode);
    }

    /**
     * 分页查询教室信息
     *
     * @param classroomVO
     * @return
     */
    @ApiOperation("分页查询教室信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody ClassroomVO classroomVO) {
        return classroomService.queryPage(request, classroomVO);
    }

    /**
     * 新增教室信息
     *
     * @param classroomVO
     * @return
     */
    @ApiOperation("新增教室信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody ClassroomVO classroomVO) {
        return classroomService.insert(request, classroomVO);
    }

    /**
     * 修改教室信息
     *
     * @param classroomVO
     * @return
     */
    @ApiOperation("修改教室信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody ClassroomVO classroomVO) {
        return classroomService.update(request, classroomVO);
    }

    /**
     * 删除教室信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除教室信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return classroomService.delete(request, id);
    }
} 