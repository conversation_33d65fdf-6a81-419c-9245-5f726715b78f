package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.FileService;
import com.edu.www.vo.FileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 文件信息管理 Controller
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@RestController
@Api(tags = "文件信息接口")
@RequestMapping("/file")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 根据ID查询文件信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询文件信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return fileService.get(request, id);
    }

    /**
     * 根据文件ID查询文件信息
     *
     * @param fileId
     * @return
     */
    @ApiOperation("根据文件ID查询文件信息")
    @GetMapping("/getByFileId")
    public ResponseEntity<Object> getByFileId(HttpServletRequest request, @RequestParam("fileId") String fileId) {
        return fileService.getByFileId(request, fileId);
    }

    /**
     * 分页查询文件信息
     *
     * @param fileVO
     * @return
     */
    @ApiOperation("分页查询文件信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody FileVO fileVO) {
        return fileService.queryPage(request, fileVO);
    }
}
