package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.StudentService;
import com.edu.www.vo.StudentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 学生信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "学生信息接口")
@RequestMapping("/student")
public class StudentController {

    @Autowired
    private StudentService studentService;

    /**
     * 根据ID查询用户信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询用户信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return studentService.get(request, id);
    }

    /**
     * 分页查询学生信息
     *
     * @param studentVO
     * @return
     */
    @ApiOperation("分页查询学生信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody StudentVO studentVO) {
        return studentService.queryPage(request, studentVO);
    }

    /**
     * 查询所有学生信息
     *
     * @return 所有学生信息列表
     */
    @ApiOperation("查询所有学生信息")
    @GetMapping("/queryAll")
    public ResponseEntity<Object> queryAll() {
        return studentService.queryAll();
    }

    /**
     * 新增学生信息
     *
     * @param studentVO
     * @return
     */
    @ApiOperation("新增学生信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody StudentVO studentVO) {
        return studentService.insert(request, studentVO);
    }

    /**
     * 修改学生信息
     *
     * @param studentVO
     * @return
     */
    @ApiOperation("修改学生信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody StudentVO studentVO) {
        return studentService.update(request, studentVO);
    }

    /**
     * 删除学生信息
     *
     * @param id
     * @return
     */
    @ApiOperation("删除学生信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return studentService.delete(request, id);
    }

    /**
     * 生成学生在读证明
     *
     * @param id
     * @return
     */
    @ApiOperation("生成学生在读证明")
    @GetMapping("/generateReadingCertificate")
    public ResponseEntity<Object> generateReadingCertificate(HttpServletRequest request, @RequestParam("id") String id) {
        return studentService.generateReadingCertificate(request, id);
    }

    /**
     * 生成多学生在读证明
     *
     * @param ids
     * @return
     */
    @ApiOperation("生成多学生在读证明")
    @PostMapping("/generateGroupReadingCertificate")
    public ResponseEntity<Object> generateGroupReadingCertificate(HttpServletRequest request, @RequestBody List<String> ids) {
        return studentService.generateGroupReadingCertificate(request, ids);
    }

    /**
     * 同步学生信息（支持ManageBac API参数）
     *
     * @param request          HTTP请求
     * @param studentIds       学生ID列表（多个，如：["AA1001", "BB2002"]）
     * @param yearGroupId      年级组ID（单个）
     * @return 同步结果
     */
    @ApiOperation("同步学生信息（支持ManageBac API参数）")
    @PostMapping("/sync/api")
    public ResponseEntity<Object> sync(HttpServletRequest request,
                                                    @RequestParam(value = "studentIds", required = false) List<String> studentIds,
                                                    @RequestParam(value = "yearGroupId", required = false) Long yearGroupId) {
        return studentService.sync(request, studentIds, yearGroupId);
    }
}
