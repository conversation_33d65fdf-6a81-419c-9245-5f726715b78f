package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.ToolService;
import com.edu.www.vo.RescheduleVO;
import com.edu.www.vo.ScheduleToolVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * 调课 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "调课接口")
@RequestMapping("/tool")
public class ToolController {

    @Autowired
    private ToolService toolService;

    /**
     * 换课
     *
     * @return
     */
    @ApiOperation("换课")
    @GetMapping("/reschedule")
    public ResponseEntity<Object> reschedule() {
        RescheduleVO rescheduleVO = new RescheduleVO();
        rescheduleVO.setDepartmentCode("DP");
        rescheduleVO.setYear("2024-2025");
        rescheduleVO.setSemester("S2");
        rescheduleVO.setGradeCode("10");
        // rescheduleVO.setClassCode("");
        rescheduleVO.setWeekday("3");
        rescheduleVO.setPeriod("3");
        return toolService.reschedule(rescheduleVO);
    }

    /**
     * 课表查询
     *
     * @param scheduleToolVO
     * @return
     */
    @ApiOperation(value = "课表查询")
    @PostMapping("/query")
    public ResponseEntity<Object> query(HttpServletRequest request, @RequestBody ScheduleToolVO scheduleToolVO) {
        return toolService.query(request, scheduleToolVO);
    }



}
