package com.edu.www.controller;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.CommonService;
import com.edu.www.service.TeacherService;
import com.edu.www.vo.TeacherVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 教师信息管理 Controller
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@RestController
@Api(tags = "教师信息接口")
@RequestMapping("/teacher")
public class TeacherController {

    @Autowired
    private TeacherService teacherService;

    /**
     * 根据ID查询教师信息
     *
     * @param id
     * @return
     */
    @ApiOperation("根据ID查询教师信息")
    @GetMapping("/get")
    public ResponseEntity<Object> get(HttpServletRequest request, @RequestParam("id") String id) {
        return teacherService.get(request, id);
    }

    /**
     * 查询所有教师信息
     *
     * @return
     */
    @ApiOperation("根据部门编码查询教师信息")
    @GetMapping("/getAllTeacher")
    public ResponseEntity<Object> getAllTeacher() {
        return teacherService.getAllTeacher();
    }

    /**
     * 根据部门编码查询教师信息
     *
     * @param departmentCode
     * @return
     */
    @ApiOperation("根据部门编码查询教师信息")
    @GetMapping("/getByDeptCode")
    public ResponseEntity<Object> getByDepartmentCode(String departmentCode) {
        return teacherService.getByDepartmentCode(departmentCode);
    }

    /**
     * 分页查询教师信息
     *
     * @param teacherVO
     * @return
     */
    @ApiOperation("分页查询教师信息")
    @PostMapping("/queryPage")
    public ResponseEntity<Object> queryPage(HttpServletRequest request, @RequestBody TeacherVO teacherVO) {
        return teacherService.queryPage(request, teacherVO);
    }

    /**
     * 新增教师信息
     *
     * @param teacherVO
     * @return
     */
    @ApiOperation("新增教师信息")
    @PostMapping("/insert")
    public ResponseEntity<Object> insert(HttpServletRequest request, @RequestBody TeacherVO teacherVO) {
        return teacherService.insert(request, teacherVO);
    }

    /**
     * 修改教师信息
     *
     * @param teacherVO
     * @return
     */
    @ApiOperation("修改教师信息")
    @PostMapping("/update")
    public ResponseEntity<Object> update(HttpServletRequest request, @RequestBody TeacherVO teacherVO) {
        return teacherService.update(request, teacherVO);
    }

    /**
     * 删除教师信息
     *
     * @param request
     * @param id
     * @return
     */
    @ApiOperation("删除教师信息")
    @GetMapping("/delete")
    public ResponseEntity<Object> delete(HttpServletRequest request, @RequestParam("id") String id) {
        return teacherService.delete(request, id);
    }
} 