package com.edu.www.convert;

import com.edu.www.constants.Constant;
import com.edu.www.dto.DutyDTO;
import com.edu.www.enums.EduDPDutyTypeEnum;
import com.edu.www.enums.EduWeekdayEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.po.DutyDetailPO;
import com.edu.www.po.DutyStatisticExcelPO;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.vo.DutyStatisticVO;
import com.edu.www.vo.DutyVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 值日信息Converter
 */
public class DutyConverter {

    private static final Logger logger = LoggerFactory.getLogger(DutyConverter.class);

    /**
     * 基于基组数据创建值日DTO
     *
     * @param baseData
     * @param dutyDate
     * @param seqWeek
     * @return
     */
    public static DutyDTO createDutyFromBase(DutyVO baseData, Date dutyDate, int seqWeek) {
        DutyDTO dutyDTO = new DutyDTO();

        // 复制基组数据的基础信息
        dutyDTO.setDepartment(baseData.getDepartment());
        dutyDTO.setDepartmentCode(baseData.getDepartmentCode());
        dutyDTO.setYear(baseData.getYear());
        dutyDTO.setSemester(baseData.getSemester());
        dutyDTO.setDutyType(baseData.getDutyType());
        dutyDTO.setWeekday(baseData.getWeekday());
        dutyDTO.setTeacherId(baseData.getTeacherId());
        dutyDTO.setTeacherName(baseData.getTeacherName());
        dutyDTO.setBaseGroupCode(baseData.getBaseGroupCode());

        // 设置新增数据的特有字段
        dutyDTO.setDutyDate(dutyDate);
        dutyDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据
        dutyDTO.setSeqWeek(String.valueOf(seqWeek)); // 使用传入的周序号

        // COLOR字段不设置，保持为空，用于后续教师调换位置时的标记
        dutyDTO.setColor("");
        dutyDTO.setDescription("");

        String userName = RequestMsgUtil.getSessionUserName();
        dutyDTO.setCreatedBy(userName);
        dutyDTO.setUpdatedBy(userName);
        dutyDTO.setCreatedAt(new Date());
        dutyDTO.setUpdatedAt(new Date());
        return dutyDTO;
    }

    /**
     * 转换为Excel导出数据格式
     *
     * @param statisticData 统计数据
     * @return Excel导出数据
     */
    public static List<DutyStatisticExcelPO> convertToExcelData(Map<String, DutyStatisticVO> statisticData) {
        return statisticData.values().stream()
                .sorted(Comparator.comparing(DutyStatisticVO::getSeq))
                .map(po -> DutyConverter.convertToExcelPO(po))
                .collect(Collectors.toList());
    }

    /**
     * 转换单个统计VO为Excel PO
     *
     * @param statisticVO 统计VO
     * @return Excel PO
     */
    public static DutyStatisticExcelPO convertToExcelPO(DutyStatisticVO statisticVO) {
        DutyStatisticExcelPO excelPO = new DutyStatisticExcelPO();
        excelPO.setSeq(statisticVO.getSeq());
        excelPO.setTeacherName(statisticVO.getTeacherName());
        excelPO.setRegularDutyCount(statisticVO.getRegularDutyCount());
        excelPO.setFridayDutyCount(statisticVO.getFridayDutyCount());
        excelPO.setLateDutyDay(statisticVO.getLateDutyDay());
        excelPO.setSundayDutyCount(statisticVO.getSundayDutyCount());
        excelPO.setRegularDutyDateStr(statisticVO.getRegularDutyDateStr());
        excelPO.setFridayDutyDateStr(statisticVO.getFridayDutyDateStr());
        excelPO.setLateDutyDateStr(statisticVO.getLateDutyDateStr());
        excelPO.setSundayDutyDateStr(statisticVO.getSundayDutyDateStr());
        return excelPO;
    }

    /**
     * 创建值日统计信息
     *
     * @param teacherDuties 教师的值日记录列表
     * @return 统计信息
     */
    public static DutyStatisticVO createDutyStatistic(List<DutyVO> teacherDuties) {
        DutyStatisticVO statisticVO = new DutyStatisticVO();

        // 获取教师姓名（取第一条记录的教师姓名）
        if (!teacherDuties.isEmpty()) {
            statisticVO.setTeacherName(teacherDuties.get(0).getTeacherName());
        }

        // 初始化计数器和日期列表
        int regularDutyCount = 0;
        int fridayDutyCount = 0;
        int lateDutyDay = 0;
        int sundayDutyCount = 0;

        List<String> regularDutyDateList = new ArrayList<>();
        List<String> fridayDutyDateList = new ArrayList<>();
        List<String> lateDutyDateList = new ArrayList<>();
        List<String> sundayDutyDateList = new ArrayList<>();

        // 遍历值日记录进行分类统计
        for (DutyVO duty : teacherDuties) {
            String dutyType = duty.getDutyType();
            String weekday = duty.getWeekday();
            Date dutyDate = duty.getDutyDate();

            // 根据值日类型和星期进行分类
            if (EduDPDutyTypeEnum.DUTY.getKey().equals(dutyType)) {
                // 值日类型为0的情况
                if (EduWeekdayEnum.MONDAY.getKey().equals(weekday) || EduWeekdayEnum.TUESDAY.getKey().equals(weekday) ||
                        EduWeekdayEnum.WEDNESDAY.getKey().equals(weekday) || EduWeekdayEnum.THURSDAY.getKey().equals(weekday)) {
                    // 周一至周四值日
                    regularDutyCount++;
                    if (dutyDate != null) {
                        regularDutyDateList.add(DateUtil.FormatDate(dutyDate, DateUtil.FORMAT_DATE_CHINESE_MONTH_DAY));
                    }
                } else if (EduWeekdayEnum.FRIDAY.getKey().equals(weekday)) {
                    // 周五值日
                    fridayDutyCount++;
                    if (dutyDate != null) {
                        fridayDutyDateList.add(DateUtil.FormatDate(dutyDate, DateUtil.FORMAT_DATE_CHINESE_MONTH_DAY));
                    }
                } else if (EduWeekdayEnum.SUNDAY.getKey().equals(weekday)) {
                    // 周日值日
                    sundayDutyCount++;
                    if (dutyDate != null) {
                        sundayDutyDateList.add(DateUtil.FormatDate(dutyDate, DateUtil.FORMAT_DATE_CHINESE_MONTH_DAY));
                    }
                }
            } else {
                // 值日类型不为0的情况（值晚自修）
                lateDutyDay++;
                if (dutyDate != null) {
                    lateDutyDateList.add(DateUtil.FormatDate(dutyDate, DateUtil.FORMAT_DATE_CHINESE_MONTH_DAY));
                }
            }
        }

        // 设置统计结果
        statisticVO.setRegularDutyCount(regularDutyCount);
        statisticVO.setFridayDutyCount(fridayDutyCount);
        statisticVO.setLateDutyDay(lateDutyDay);
        statisticVO.setSundayDutyCount(sundayDutyCount);

        // 对日期列表进行排序（按月日排序）
        regularDutyDateList.sort(String::compareTo);
        fridayDutyDateList.sort(String::compareTo);
        lateDutyDateList.sort(String::compareTo);
        sundayDutyDateList.sort(String::compareTo);

        statisticVO.setRegularDutyDateStr(regularDutyDateList.stream().collect(Collectors.joining(Constant.COMMA)));
        statisticVO.setFridayDutyDateStr(fridayDutyDateList.stream().collect(Collectors.joining(Constant.COMMA)));
        statisticVO.setLateDutyDateStr(lateDutyDateList.stream().collect(Collectors.joining(Constant.COMMA)));
        statisticVO.setSundayDutyDateStr(sundayDutyDateList.stream().collect(Collectors.joining(Constant.COMMA)));

        return statisticVO;
    }

    /**
     * 按实际日期连续性分组数据，而不是依赖数据库中的seqWeek字段
     * 格式：Map<weekKey, Map<dutyType_weekday_dutyDate, teacherName>>
     */
    public static Map<String, Map<String, String>> groupDataBySeqWeek(List<DutyVO> dutyData) {
        if (CollectionUtils.isEmpty(dutyData)) {
            logger.warn("值日数据为空，返回空结果");
            return new LinkedHashMap<>();
        }

        // 1. 按日期排序，确保数据按时间顺序处理
        List<DutyVO> sortedData = dutyData.stream()
                .filter(duty -> duty.getDutyDate() != null)
                .sorted(Comparator.comparing(DutyVO::getDutyDate))
                .collect(Collectors.toList());

        // 2. 按实际周分组（基于日期的连续性）
        Map<String, List<DutyVO>> weekGroups = groupByActualWeeks(sortedData);

        // 3. 转换为最终格式
        Map<String, Map<String, String>> groupedMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<DutyVO>> weekEntry : weekGroups.entrySet()) {
            String weekKey = weekEntry.getKey();
            List<DutyVO> weekDuties = weekEntry.getValue();

            Map<String, String> weekData = buildWeekData(weekDuties);
            groupedMap.put(weekKey, weekData);
        }
        return groupedMap;
    }

    /**
     * 构建单周的数据
     */
    public static Map<String, String> buildWeekData(List<DutyVO> weekDuties) {
        Map<String, String> weekData = new LinkedHashMap<>();

        // 按照指定顺序排序：值日类型 + 星期几 + 值日类型内部顺序
        List<DutyVO> sortedDuties = weekDuties.stream()
                .sorted((a, b) -> {
                    // 首先按星期几排序
                    int weekdayCompare = Integer.compare(
                            Integer.parseInt(a.getWeekday()),
                            Integer.parseInt(b.getWeekday())
                    );
                    if (weekdayCompare != 0) {
                        return weekdayCompare;
                    }

                    // 同一天内按值日类型排序：值日(0) → Year10A+B(1) → Year11A+B(2) → Year12+春季(3)
                    return getDutyTypeOrder(a.getDutyType()) - getDutyTypeOrder(b.getDutyType());
                })
                .collect(Collectors.toList());

        for (DutyVO duty : sortedDuties) {
            try {
                String innerKey = buildInnerKey(duty);
                String teacherName = duty.getTeacherName() != null ? duty.getTeacherName() : "";
                weekData.put(innerKey, teacherName);
            } catch (Exception e) {
                logger.warn("构建数据失败，跳过该条记录: dutyDate={}, dutyType={}, weekday={}",
                        duty.getDutyDate(), duty.getDutyType(), duty.getWeekday(), e);
            }
        }

        return weekData;
    }

    /**
     * 获取值日类型的排序顺序
     */
    public static int getDutyTypeOrder(String dutyType) {
        if (EduDPDutyTypeEnum.DUTY.getKey().equals(dutyType)) {
            return 0; // 值日
        } else if (EduDPDutyTypeEnum.YEAR_10_AB.getKey().equals(dutyType)) {
            return 1; // Year10A+B
        } else if (EduDPDutyTypeEnum.YEAR_11_AB.getKey().equals(dutyType)) {
            return 2; // Year11A+B
        } else if (EduDPDutyTypeEnum.YEAR_12_SPRING.getKey().equals(dutyType)) {
            return 3; // Year12+春季
        } else {
            return 999; // 其他类型排在最后
        }
    }

    /**
     * 构建内层key：dutyType_weekday_dutyDate
     */
    public static String buildInnerKey(DutyVO duty) {
        // 构建值日类型描述
        String dutyType = "";
        EduDPDutyTypeEnum dpDutyTypeEnum = EduDPDutyTypeEnum.getByKey(duty.getDutyType());
        if (dpDutyTypeEnum != null) {
            dutyType = dpDutyTypeEnum.getDesc() + Constant.HYPHEN + dpDutyTypeEnum.getTimeDesc();
        }

        // 构建星期描述
        String weekday = "";
        EduWeekdayEnum weekdayEnum = EduWeekdayEnum.getByKey(duty.getWeekday());
        if (weekdayEnum != null) {
            weekday = String.valueOf(weekdayEnum.getDesc().charAt(1));
        }

        // 格式化日期
        String formattedDate = DateUtil.FormatDate(duty.getDutyDate(), DateUtil.FORMAT_DATE_CHINESE_YEAR_MONTH_DAY);

        // 组装最终key
        return dutyType + Constant.UNDERSCORE + weekday + Constant.UNDERSCORE + formattedDate;
    }

    /**
     * 按实际周分组（基于日期的连续性）
     * 核心逻辑：计算每个日期所在周的周一日期作为周标识，确保同一自然周的数据分到同一组
     */
    public static Map<String, List<DutyVO>> groupByActualWeeks(List<DutyVO> dutyData) {
        Map<String, List<DutyVO>> weekGroups = new LinkedHashMap<>();
        for (DutyVO duty : dutyData) {
            if (duty.getDutyDate() == null) {
                continue;
            }

            try {
                // 计算该日期所在周的周一日期作为周标识
                String weekKey = calculateMondayOfWeek(duty.getDutyDate());
                // 按周分组
                weekGroups.computeIfAbsent(weekKey, k -> new ArrayList<>()).add(duty);
            } catch (Exception e) {
                logger.warn("计算周标识失败，跳过该记录: dutyDate={}, teacherName={}",
                        duty.getDutyDate(), duty.getTeacherName(), e);
            }
        }

        return weekGroups;
    }

    /**
     * 计算指定日期所在周的周一日期
     *
     * @param date 指定日期
     * @return 周一日期的字符串表示
     */
    public static String calculateMondayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取当前是星期几（1=周日, 2=周一, ..., 7=周六）
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        // 计算到周一需要减去的天数
        int daysToMonday;
        if (dayOfWeek == Calendar.SUNDAY) {
            // 如果是周日，需要减去6天到达周一
            daysToMonday = 6;
        } else {
            // 其他情况，减去(dayOfWeek - Calendar.MONDAY)天
            daysToMonday = dayOfWeek - Calendar.MONDAY;
        }

        // 计算周一的日期
        calendar.add(Calendar.DAY_OF_MONTH, -daysToMonday);
        return DateUtil.FormatDate(calendar.getTime(), DateUtil.FORMAT_DATE_SHORT);
    }

    /**
     * 重新编号为连续的Week序号，去掉年月前缀
     *
     * @param originalData
     * @return
     */
    public static Map<String, Map<String, String>> renumberWeeks(Map<String, Map<String, String>> originalData) {
        Map<String, Map<String, String>> renumberedData = new LinkedHashMap<>();

        // 按key排序（确保时间顺序）
        List<String> sortedKeys = new ArrayList<>(originalData.keySet());
        sortedKeys.sort(String::compareTo);

        // 重新编号
        int weekNumber = 1;
        for (String originalKey : sortedKeys) {
            String newKey = Constant.WEEK_PREFIX + weekNumber;
            renumberedData.put(newKey, originalData.get(originalKey));
            weekNumber++;
        }
        return renumberedData;
    }

    /**
     * 按实际日期连续性分组数据，带ID格式
     * 格式：Map<weekKey, Map<recordId, List<Map<String, String>>>>
     */
    public static Map<String, Map<String, List<Map<String, String>>>> groupDataBySeqWeekWithId(List<DutyVO> dutyData) {
        if (CollectionUtils.isEmpty(dutyData)) {
            logger.warn("值日数据为空，返回空结果");
            return new LinkedHashMap<>();
        }

        // 1. 按日期排序，确保数据按时间顺序处理
        List<DutyVO> sortedData = dutyData.stream()
                .filter(duty -> duty.getDutyDate() != null)
                .sorted(Comparator.comparing(DutyVO::getDutyDate))
                .collect(Collectors.toList());

        // 2. 按实际周分组（基于日期的连续性）
        Map<String, List<DutyVO>> weekGroups = groupByActualWeeks(sortedData);

        // 3. 转换为带ID的格式
        Map<String, Map<String, List<Map<String, String>>>> groupedMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<DutyVO>> weekEntry : weekGroups.entrySet()) {
            String weekKey = weekEntry.getKey();
            List<DutyVO> weekDuties = weekEntry.getValue();

            Map<String, List<Map<String, String>>> weekDataWithId = buildWeekDataWithId(weekDuties);
            groupedMap.put(weekKey, weekDataWithId);
        }
        return groupedMap;
    }

    /**
     * 构建单周的数据，带ID格式
     */
    public static Map<String, List<Map<String, String>>> buildWeekDataWithId(List<DutyVO> weekDuties) {
        Map<String, List<Map<String, String>>> weekDataWithId = new LinkedHashMap<>();

        // 按照指定顺序排序：值日类型 + 星期几 + 值日类型内部顺序
        List<DutyVO> sortedDuties = weekDuties.stream()
                .sorted((a, b) -> {
                    // 首先按星期几排序
                    int weekdayCompare = Integer.compare(
                            Integer.parseInt(a.getWeekday()),
                            Integer.parseInt(b.getWeekday())
                    );
                    if (weekdayCompare != 0) {
                        return weekdayCompare;
                    }

                    // 同一天内按值日类型排序：值日(0) → Year10A+B(1) → Year11A+B(2) → Year12+春季(3)
                    return getDutyTypeOrder(a.getDutyType()) - getDutyTypeOrder(b.getDutyType());
                })
                .collect(Collectors.toList());

        for (DutyVO duty : sortedDuties) {
            try {
                String recordId = duty.getId(); // 使用记录ID作为key
                String innerKey = buildInnerKey(duty);
                String teacherName = duty.getTeacherName() != null ? duty.getTeacherName() : "";
                String color = duty.getColor() != null ? duty.getColor() : "";
                String description = duty.getDescription() != null ? duty.getDescription() : "";

                // 创建单条记录的Map，包含key、teacherName、color、description
                Map<String, String> recordData = new HashMap<>();
                recordData.put("key", innerKey);
                recordData.put("teacherName", teacherName);
                recordData.put("color", color);
                recordData.put("description", description);

                // 将记录放入List中
                List<Map<String, String>> recordList = new ArrayList<>();
                recordList.add(recordData);

                weekDataWithId.put(recordId, recordList);
            } catch (Exception e) {
                logger.warn("构建带ID数据失败，跳过该条记录: dutyDate={}, dutyType={}, weekday={}",
                        duty.getDutyDate(), duty.getDutyType(), duty.getWeekday(), e);
            }
        }

        return weekDataWithId;
    }

    /**
     * 重新编号为连续的Week序号，带ID格式
     */
    public static Map<String, Map<String, List<Map<String, String>>>> renumberWeeksWithId(Map<String, Map<String, List<Map<String, String>>>> originalData) {
        Map<String, Map<String, List<Map<String, String>>>> renumberedData = new LinkedHashMap<>();

        // 按key排序（确保时间顺序）
        List<String> sortedKeys = new ArrayList<>(originalData.keySet());
        sortedKeys.sort(String::compareTo);

        // 重新编号
        int weekNumber = 1;
        for (String originalKey : sortedKeys) {
            String newKey = Constant.WEEK_PREFIX + weekNumber;
            renumberedData.put(newKey, originalData.get(originalKey));
            weekNumber++;
        }
        return renumberedData;
    }

    /**
     * 将DutyDTO列表转换为DutyVO列表
     */
    public static List<DutyVO> convertDTOListToVOList(List<DutyDTO> dutyDTOList) {
        List<DutyVO> dutyVOList = new ArrayList<>();

        for (DutyDTO dutyDTO : dutyDTOList) {
            DutyVO dutyVO = new DutyVO();

            // 复制基本字段
            dutyVO.setId(dutyDTO.getId());
            dutyVO.setDepartment(dutyDTO.getDepartment());
            dutyVO.setDepartmentCode(dutyDTO.getDepartmentCode());
            dutyVO.setYear(dutyDTO.getYear());
            dutyVO.setSemester(dutyDTO.getSemester());
            dutyVO.setSeqWeek(dutyDTO.getSeqWeek());
            dutyVO.setWeekday(dutyDTO.getWeekday());
            dutyVO.setDutyDate(dutyDTO.getDutyDate());
            dutyVO.setDutyType(dutyDTO.getDutyType());
            dutyVO.setTeacherName(dutyDTO.getTeacherName());
            dutyVO.setTeacherId(dutyDTO.getTeacherId());
            dutyVO.setColor(dutyDTO.getColor());
            dutyVO.setIsBaseGroup(dutyDTO.getIsBaseGroup());
            dutyVO.setBaseGroupCode(dutyDTO.getBaseGroupCode());
            dutyVO.setDescription(dutyDTO.getDescription());
            dutyVO.setCreatedAt(dutyDTO.getCreatedAt());
            dutyVO.setCreatedBy(dutyDTO.getCreatedBy());
            dutyVO.setUpdatedAt(dutyDTO.getUpdatedAt());
            dutyVO.setUpdatedBy(dutyDTO.getUpdatedBy());
            dutyVOList.add(dutyVO);
        }

        return dutyVOList;
    }

    /**
     * 按实际日期连续性分组数据，保留完整的DutyVO信息
     * 格式：Map<weekKey, Map<dutyType_weekday_dutyDate, DutyDetail>>
     */
    public static Map<String, Map<String, DutyDetailPO>> groupDataBySeqWeekWithDetails(List<DutyVO> dutyData) {
        if (CollectionUtils.isEmpty(dutyData)) {
            logger.warn("值日数据为空，返回空结果");
            return new LinkedHashMap<>();
        }

        // 1. 按日期排序，确保数据按时间顺序处理
        List<DutyVO> sortedData = dutyData.stream()
                .filter(duty -> duty.getDutyDate() != null)
                .sorted(Comparator.comparing(DutyVO::getDutyDate))
                .collect(Collectors.toList());

        // 2. 按实际周分组（基于日期的连续性）
        Map<String, List<DutyVO>> weekGroups = groupByActualWeeks(sortedData);

        // 3. 转换为带详细信息的格式
        Map<String, Map<String, DutyDetailPO>> groupedMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<DutyVO>> weekEntry : weekGroups.entrySet()) {
            String weekKey = weekEntry.getKey();
            List<DutyVO> weekDuties = weekEntry.getValue();

            Map<String, DutyDetailPO> weekData = buildWeekDataWithDetails(weekDuties);
            groupedMap.put(weekKey, weekData);
        }
        return groupedMap;
    }

    /**
     * 构建单周的数据，保留完整信息
     */
    public static Map<String, DutyDetailPO> buildWeekDataWithDetails(List<DutyVO> weekDuties) {
        Map<String, DutyDetailPO> weekData = new LinkedHashMap<>();

        // 按照指定顺序排序：值日类型 + 星期几 + 值日类型内部顺序
        List<DutyVO> sortedDuties = weekDuties.stream()
                .sorted((a, b) -> {
                    // 首先按星期几排序
                    int weekdayCompare = Integer.compare(
                            Integer.parseInt(a.getWeekday()),
                            Integer.parseInt(b.getWeekday())
                    );
                    if (weekdayCompare != 0) {
                        return weekdayCompare;
                    }

                    // 同一天内按值日类型排序：值日(0) → Year10A+B(1) → Year11A+B(2) → Year12+春季(3)
                    return getDutyTypeOrder(a.getDutyType()) - getDutyTypeOrder(b.getDutyType());
                })
                .collect(Collectors.toList());

        for (DutyVO duty : sortedDuties) {
            try {
                String innerKey = buildInnerKey(duty);
                DutyDetailPO dutyDetail = new DutyDetailPO(
                        duty.getTeacherName(),
                        duty.getColor(),
                        duty.getDescription(),
                        duty.getTeacherId()
                );
                weekData.put(innerKey, dutyDetail);
            } catch (Exception e) {
                logger.warn("构建详细数据失败，跳过该条记录: dutyDate={}, dutyType={}, weekday={}",
                        duty.getDutyDate(), duty.getDutyType(), duty.getWeekday(), e);
            }
        }

        return weekData;
    }

    /**
     * 重新编号为连续的Week序号，保留详细信息
     */
    public static Map<String, Map<String, DutyDetailPO>> renumberWeeksWithDetails(Map<String, Map<String, DutyDetailPO>> originalData) {
        Map<String, Map<String, DutyDetailPO>> renumberedData = new LinkedHashMap<>();

        // 按key排序（确保时间顺序）
        List<String> sortedKeys = new ArrayList<>(originalData.keySet());
        sortedKeys.sort(String::compareTo);

        // 重新编号
        int weekNumber = 1;
        for (String originalKey : sortedKeys) {
            String newKey = Constant.WEEK_PREFIX + weekNumber;
            renumberedData.put(newKey, originalData.get(originalKey));
            weekNumber++;
        }
        return renumberedData;
    }

    /**
     * 将DutyDetail格式转换为Handler需要的Object格式
     */
    public static Map<String, Map<String, Object>> convertToHandlerFormat(Map<String, Map<String, DutyDetailPO>> detailData) {
        Map<String, Map<String, Object>> convertedData = new LinkedHashMap<>();

        for (Map.Entry<String, Map<String, DutyDetailPO>> weekEntry : detailData.entrySet()) {
            String weekKey = weekEntry.getKey();
            Map<String, DutyDetailPO> weekData = weekEntry.getValue();

            Map<String, Object> convertedWeekData = new LinkedHashMap<>();
            for (Map.Entry<String, DutyDetailPO> dutyEntry : weekData.entrySet()) {
                convertedWeekData.put(dutyEntry.getKey(), dutyEntry.getValue());
            }

            convertedData.put(weekKey, convertedWeekData);
        }

        return convertedData;
    }
}
