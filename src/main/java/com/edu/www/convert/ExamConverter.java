package com.edu.www.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.dto.ExamDTO;
import com.edu.www.enums.*;
import com.edu.www.po.CompositionInfoPO;
import com.edu.www.po.SubjectInfoPO;
import com.edu.www.po.TimeIntervalPO;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.StringUtil;
import com.edu.www.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ExamConverter {
    private static final Logger logger = LoggerFactory.getLogger(ExamConverter.class);

    /**
     * 解析时间段字符串
     *
     * @param startTime 开始时间 (格式: "HH:mm")
     * @param endTime   结束时间 (格式: "HH:mm")
     * @return 时间段对象，解析失败返回null
     */
    public static TimeIntervalPO parseTimeInterval(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            logger.warn("时间段为空: startTime={}, endTime={}", startTime, endTime);
            return null;
        }

        try {
            // 解析时间格式 HH:mm
            String[] startParts = startTime.split(":");
            String[] endParts = endTime.split(":");

            if (startParts.length != 2 || endParts.length != 2) {
                logger.warn("时间格式错误: startTime={}, endTime={}", startTime, endTime);
                return null;
            }

            int startHour = Integer.parseInt(startParts[0]);
            int startMinute = Integer.parseInt(startParts[1]);
            int endHour = Integer.parseInt(endParts[0]);
            int endMinute = Integer.parseInt(endParts[1]);

            // 转换为分钟数（从00:00开始计算）
            int startMinutes = startHour * 60 + startMinute;
            int endMinutes = endHour * 60 + endMinute;

            // 处理跨天的情况（如果结束时间小于开始时间，认为是第二天）
            if (endMinutes <= startMinutes) {
                endMinutes += 24 * 60; // 加上24小时
            }

            return new TimeIntervalPO(startMinutes, endMinutes, startTime, endTime);

        } catch (NumberFormatException e) {
            logger.warn("时间格式解析失败: startTime={}, endTime={}", startTime, endTime, e);
            return null;
        }
    }

    /**
     * 计算合并后的时间段总时长
     *
     * @param timeIntervals 时间段列表
     * @return 总时长（分钟）
     */
    public static int calculateMergedDuration(List<TimeIntervalPO> timeIntervals) {
        if (CollectionUtils.isEmpty(timeIntervals)) {
            return 0;
        }

        // 按开始时间排序
        timeIntervals.sort(Comparator.comparingInt(TimeIntervalPO::getStartMinutes));

        List<TimeIntervalPO> mergedIntervals = new ArrayList<>();
        TimeIntervalPO current = timeIntervals.get(0);

        for (int i = 1; i < timeIntervals.size(); i++) {
            TimeIntervalPO next = timeIntervals.get(i);

            // 检查是否有重叠或相邻
            if (current.getEndMinutes() >= next.getStartMinutes()) {
                // 合并时间段
                current = new TimeIntervalPO(
                        current.getStartMinutes(),
                        Math.max(current.getEndMinutes(), next.getEndMinutes()),
                        current.getStartTime(),
                        next.getEndTime()
                );
                logger.debug("合并时间段: {} - {}", current.getStartTime(), current.getEndTime());
            } else {
                // 没有重叠，添加当前时间段到结果中
                mergedIntervals.add(current);
                current = next;
            }
        }
        // 添加最后一个时间段
        mergedIntervals.add(current);

        // 计算总时长
        int totalDuration = mergedIntervals.stream()
                .mapToInt(TimeIntervalPO::getDurationMinutes)
                .sum();

        logger.debug("合并后的时间段数量: {}, 总时长: {}分钟", mergedIntervals.size(), totalDuration);
        return totalDuration;
    }

    /**
     * 计算总时长
     *
     * @param examDTO
     * @return
     */
    public static int calTotalDuration(ExamDTO examDTO) {
        // 解析考试组成信息，用于计算总时长
        String compositionInfo = examDTO.getCompositionInfo();
        if (StringUtils.isBlank(compositionInfo)) {
            return 0;
        }

        try {
            // 解析JSON为CompositionInfoPO对象列表
            List<CompositionInfoPO> compositionList = JSON.parseArray(compositionInfo, CompositionInfoPO.class);
            if (CollectionUtils.isEmpty(compositionList)) {
                return 0;
            }

            // 存储所有有效的时间段
            List<TimeIntervalPO> timeIntervals = new ArrayList<>();

            // 循环每个考试组成信息，解析时间段
            for (CompositionInfoPO composition : compositionList) {
                String startTime = composition.getStartTime(); // 格式: "08:00"
                String endTime = composition.getEndTime();     // 格式: "10:30"

                // 验证时间格式并解析
                TimeIntervalPO interval = parseTimeInterval(startTime, endTime);
                if (interval != null) {
                    timeIntervals.add(interval);
                    logger.debug("解析时间段: {} - {} ({}分钟)",
                            interval.getStartTime(), interval.getEndTime(), interval.getDurationMinutes());
                }
            }

            // 合并重叠的时间段并计算总时长
            int totalDuration = calculateMergedDuration(timeIntervals);
            logger.info("考试总时长: {}分钟", totalDuration);
            return totalDuration;

        } catch (Exception e) {
            logger.warn("解析考试组成信息失败: {}", compositionInfo, e);
            return 0;
        }
    }

    /**
     * 将JSON字符串转换为巡考人List
     *
     * @param examVO 考试VO对象
     */
    public static void convertInspectorToList(ExamVO examVO) {
        if (examVO != null && StringUtils.isNotBlank(examVO.getInspector())) {
            try {
                // 尝试解析JSON数组
                JSONArray jsonArray = JSON.parseArray(examVO.getInspector());
                List<String> inspectors = jsonArray.toJavaList(String.class);
                examVO.setInspectors(inspectors);
            } catch (Exception e) {
                // 如果JSON解析失败，按逗号分隔处理（兼容旧数据）
                List<String> inspectors = Arrays.asList(examVO.getInspector().split(Constant.SEPARATOR));
                examVO.setInspectors(inspectors);
            }
        }
    }

    /**
     * 将巡考人List转换为JSON字符串
     *
     * @param examDTO 考试DTO对象
     */
    public static void convertInspectorsToString(ExamDTO examDTO) {
        if (Objects.nonNull(examDTO) && !CollectionUtils.isEmpty(examDTO.getInspectors())) {
            // 将List转换为JSON字符串存储
            String inspectorJsonString = JSON.toJSONString(examDTO.getInspectors());
            examDTO.setInspector(inspectorJsonString);
        }
    }

    /**
     * List<ExamVO>转List<ExamTagVO>
     *
     * @param examVOList
     * @return
     */
    public static List<ExamTagVO> examVOList2ExamTagVOList(List<ExamVO> examVOList) {
        // 输入参数校验
        if (CollectionUtils.isEmpty(examVOList)) {
            return new ArrayList<>();
        }

        List<ExamTagVO> examTagVOList = new ArrayList<>();
        for (ExamVO examVO : examVOList) {
            // 跳过null的examVO
            if (examVO == null) {
                logger.warn("跳过null的ExamVO对象");
                continue;
            }

            ExamTagVO examTagVO = new ExamTagVO();
            // 安全设置基础字段
            examTagVO.setGradeCode(examVO.getGradeCode() + (StringUtils.isNotBlank(examVO.getClassCode()) ? examVO.getClassCode() : ""));
            examTagVO.setClassroomCode(examVO.getClassroomCode());

            // 处理考试组成信息
            processCompositionInfo(examVO, examTagVO);

            // 安全设置其他字段
            setOtherFields(examVO, examTagVO);

            examTagVOList.add(examTagVO);
        }
        return examTagVOList;
    }

    /**
     * 处理考试组成信息
     *
     * @param examVO    源对象
     * @param examTagVO 目标对象
     */
    private static void processCompositionInfo(ExamVO examVO, ExamTagVO examTagVO) {
        String compositionInfo = examVO.getCompositionInfo();
        if (StringUtils.isBlank(compositionInfo)) {
            return;
        }

        try {
            // 解析JSON为CompositionInfoPO对象列表
            List<CompositionInfoPO> compositionList = JSON.parseArray(compositionInfo, CompositionInfoPO.class);
            if (CollectionUtils.isEmpty(compositionList)) {
                return;
            }

            // 提取不重复的学科编码
            String distinctSubjectCodes = compositionList.stream()
                    .filter(Objects::nonNull)
                    .map(CompositionInfoPO::getSubjectCode)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.joining(Constant.PLUS));
            examTagVO.setSubjectCode(distinctSubjectCodes);

            // 处理学科部分和时间部分
            List<String> subjectPartsList = new ArrayList<>();
            Map<String, String> timePartsMap = new HashMap<>();

            compositionList.stream()
                    .filter(Objects::nonNull)
                    .forEach(composition -> processComposition(composition, subjectPartsList, timePartsMap));

            examTagVO.setSubjectParts(subjectPartsList);
            examTagVO.setTimeParts(timePartsMap);

        } catch (Exception e) {
            logger.warn("解析考试组成信息失败: compositionInfo={}", compositionInfo, e);
        }
    }

    /**
     * 处理单个考试组成信息
     *
     * @param composition      考试组成信息
     * @param subjectPartsList 学科部分列表
     * @param timePartsMap     时间部分映射
     */
    private static void processComposition(CompositionInfoPO composition,
                                           List<String> subjectPartsList,
                                           Map<String, String> timePartsMap) {
        // 处理试卷类型枚举转换
        String subjectPaper = composition.getSubjectPaper();
        if (StringUtils.isNotBlank(subjectPaper)) {
            EduPaperTypeEnum paperTypeEnum = EduPaperTypeEnum.getByKey(subjectPaper);
            if (paperTypeEnum != null) {
                composition.setSubjectPaper(paperTypeEnum.getDesc());
                subjectPaper = paperTypeEnum.getDesc(); // 使用转换后的值
            }
        }

        // 安全构建keyPart，处理null值
        String subjectType = StringUtils.defaultString(composition.getSubjectType(), "");
        String subjectLevel = StringUtils.defaultString(composition.getSubjectLevel(), "");
        String paperDesc = StringUtils.defaultString(subjectPaper, "");

        // 构建keyPart，去除多余的空格
        StringBuilder keyPartBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(subjectType)) {
            keyPartBuilder.append(subjectType);
        }
        if (StringUtils.isNotBlank(subjectLevel)) {
            if (keyPartBuilder.length() > 0) {
                keyPartBuilder.append(Constant.SPACE);
            }
            keyPartBuilder.append(subjectLevel);
        }
        if (StringUtils.isNotBlank(paperDesc)) {
            if (keyPartBuilder.length() > 0) {
                keyPartBuilder.append(Constant.SPACE);
            }
            keyPartBuilder.append(paperDesc);
        }

        String keyPart = keyPartBuilder.toString().trim();

        // 只有当keyPart不为空时才添加
        if (StringUtils.isNotBlank(keyPart)) {
            subjectPartsList.add(keyPart);

            // 安全构建时间范围
            String startTime = StringUtils.defaultString(composition.getStartTime(), "");
            String endTime = StringUtils.defaultString(composition.getEndTime(), "");
            String timeRange = "";

            if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                timeRange = startTime + Constant.HYPHEN + endTime;
            } else if (StringUtils.isNotBlank(startTime)) {
                timeRange = startTime;
            } else if (StringUtils.isNotBlank(endTime)) {
                timeRange = endTime;
            }

            timePartsMap.put(keyPart, timeRange);
        } else {
            logger.debug("跳过空的keyPart: subjectType={}, subjectLevel={}, subjectPaper={}",
                    subjectType, subjectLevel, paperDesc);
        }
    }

    /**
     * 设置其他字段
     *
     * @param examVO    源对象
     * @param examTagVO 目标对象
     */
    public static void setOtherFields(ExamVO examVO, ExamTagVO examTagVO) {
        // 安全设置考试日期
        try {
            if (examVO.getExamDate() != null) {
                examTagVO.setExamDate(DateUtil.FormatDate(examVO.getExamDate(), DateUtil.FORMAT_MONTH_DAY_DOT));
            }
        } catch (Exception e) {
            logger.warn("格式化考试日期失败: examDate={}", examVO.getExamDate(), e);
        }

        // 安全设置星期
        try {
            String weekday = examVO.getWeekday();
            if (StringUtils.isNotBlank(weekday)) {
                Map<String, String> map = CommonConstant.weekdayMap;
                examTagVO.setWeekday(StringUtil.capitalizeFirstLetter(map.get(weekday)));
            }
        } catch (Exception e) {
            logger.warn("处理星期字段失败: weekday={}", examVO.getWeekday(), e);
        }

        // 安全设置备注
        examTagVO.setNote(examVO.getDescription());
    }

    /**
     * 计算监考人时长统计
     *
     * @param examVOList 考试列表
     * @param teacherMap 教师信息映射
     * @param queryParam 查询参数
     * @return 监考时长统计映射
     */
    public static Map<String, ExamDurationVO> calDuration(List<ExamVO> examVOList,
                                                          Map<String, TeacherVO> teacherMap,
                                                          ExamVO queryParam) {
        Map<String, ExamDurationVO> durationMap = new HashMap<>();

        for (ExamVO examVO : examVOList) {
            String invigilator = examVO.getInvigilator();
            if (StringUtils.isBlank(invigilator)) {
                continue;
            }

            // 获取考试总时长
            Integer totalDuration = examVO.getTotalDuration();
            if (totalDuration == null || totalDuration <= 0) {
                continue;
            }

            // 获取考试类型
            String examType = examVO.getType();
            if (StringUtils.isBlank(examType)) {
                continue;
            }

            // 获取或创建监考人统计记录
            ExamDurationVO durationVO = durationMap.computeIfAbsent(invigilator, k -> {
                ExamDurationVO vo = new ExamDurationVO();
                vo.setYear(queryParam.getYear());
                vo.setSemester(queryParam.getSemester());
                vo.setDepartmentCode(queryParam.getDepartmentCode());
                vo.setInvigilatorId(invigilator);

                // 设置教师姓名
                TeacherVO teacher = teacherMap.get(invigilator);
                if (teacher != null) {
                    vo.setInvigilatorNameZh(teacher.getNameZh());
                    vo.setInvigilatorNameEn(teacher.getNameEn());
                } else {
                    vo.setInvigilatorNameZh(invigilator);
                    vo.setInvigilatorNameEn(invigilator);
                }

                // 初始化各类型时长为0
                vo.setTotalMonthlyDuration(0);
                vo.setTotalMidDuration(0);
                vo.setTotalMockDuration(0);
                vo.setTotalIBDuration(0);
                vo.setTotalFinalDuration(0);
                vo.setTotalDuration(0);

                return vo;
            });

            // 根据考试类型累加时长
            addDurationByExamType(durationVO, examType, totalDuration);
        }

        // 计算每个监考人的总时长
        durationMap.values().forEach(ExamConverter::calTotalDuration);

        return durationMap;
    }

    /**
     * 根据考试类型累加时长
     *
     * @param durationVO 时长统计对象
     * @param examType   考试类型
     * @param duration   时长
     */
    public static void addDurationByExamType(ExamDurationVO durationVO, String examType, Integer duration) {
        switch (examType) {
            case "MONTHLY":
                durationVO.setTotalMonthlyDuration(
                        (durationVO.getTotalMonthlyDuration() != null ? durationVO.getTotalMonthlyDuration() : 0) + duration);
                break;
            case "MID_TERM":
                durationVO.setTotalMidDuration(
                        (durationVO.getTotalMidDuration() != null ? durationVO.getTotalMidDuration() : 0) + duration);
                break;
            case "MOCK":
                durationVO.setTotalMockDuration(
                        (durationVO.getTotalMockDuration() != null ? durationVO.getTotalMockDuration() : 0) + duration);
                break;
            case "IB_TERM":
                durationVO.setTotalIBDuration(
                        (durationVO.getTotalIBDuration() != null ? durationVO.getTotalIBDuration() : 0) + duration);
                break;
            case "FINAL_TERM":
                durationVO.setTotalFinalDuration(
                        (durationVO.getTotalFinalDuration() != null ? durationVO.getTotalFinalDuration() : 0) + duration);
                break;
            default:
                logger.warn("未知的考试类型: {}", examType);
                break;
        }
    }

    /**
     * 计算总时长
     *
     * @param durationVO 时长统计对象
     */
    public static void calTotalDuration(ExamDurationVO durationVO) {
        int total = 0;
        total += durationVO.getTotalMonthlyDuration() != null ? durationVO.getTotalMonthlyDuration() : 0;
        total += durationVO.getTotalMidDuration() != null ? durationVO.getTotalMidDuration() : 0;
        total += durationVO.getTotalMockDuration() != null ? durationVO.getTotalMockDuration() : 0;
        total += durationVO.getTotalIBDuration() != null ? durationVO.getTotalIBDuration() : 0;
        total += durationVO.getTotalFinalDuration() != null ? durationVO.getTotalFinalDuration() : 0;
        durationVO.setTotalDuration(total);
    }

    /**
     * 转换考试记录为监考时长详情VO列表
     *
     * @param examVOList 考试记录列表
     * @return 监考时长详情VO列表
     */
    public static List<DurationDetailVO> convertToDetailVOList(List<ExamVO> examVOList) {
        List<DurationDetailVO> detailVOList = new ArrayList<>();

        for (ExamVO examVO : examVOList) {
            DurationDetailVO detailVO = new DurationDetailVO();

            // ========== 基本信息 ==========
            // 考试日期
            if (examVO.getExamDate() != null) {
                detailVO.setExamDate(DateUtil.FormatDate(examVO.getExamDate(), DateUtil.FORMAT_DATE_CHINESE_YEAR_MONTH_DAY));
            }

            // 星期
            EduWeekdayEnum weekdayEnum = EduWeekdayEnum.getByKey(examVO.getWeekday());
            if (weekdayEnum != null) {
                detailVO.setWeekday(weekdayEnum.getDesc());
            }

            // 年度
            detailVO.setYear(examVO.getYear());
            // 学期
            EduSemesterEnum semesterEnum = EduSemesterEnum.getByKey(examVO.getYear());
            if (semesterEnum != null) {
                detailVO.setSemester(semesterEnum.getDesc());
            }

            // 考试类型
            EduExamTypeEnum examTypeEnum = EduExamTypeEnum.getByKey(examVO.getType());
            if (examTypeEnum != null) {
                detailVO.setExamType(examTypeEnum.getDesc());
            }


            // ========== 考场信息 ==========
            // 部门
            EduDeptCodeEnum eduDeptCodeEnum = EduDeptCodeEnum.getByKey(examVO.getDepartmentCode());
            if (eduDeptCodeEnum != null) {
                detailVO.setDepartmentCode(eduDeptCodeEnum.getDesc());
            }


            // 年级班级
            String classCode = examVO.getClassCode();
            if (StringUtils.isNotBlank(classCode)) {
                EduClazzCodeEnum clazzCodeEnum = EduClazzCodeEnum.getByKey(examVO.getClassCode());
                if (clazzCodeEnum != null) {
                    detailVO.setGradeClass(clazzCodeEnum.getDesc());
                }
            } else {
                EduGradeCodeEnum gradeCodeEnum = EduGradeCodeEnum.getByKey(examVO.getGradeCode());
                if (gradeCodeEnum != null) {
                    detailVO.setGradeClass(gradeCodeEnum.getDesc());
                }
            }

            // 教室
            detailVO.setClassroom(examVO.getClassroomCode());

            // 考生人数
            detailVO.setExamCandidate(examVO.getExamCandidate());

            // 总时长
            if (examVO.getTotalDuration() != null) {
                detailVO.setTotalDuration(formatDuration(examVO.getTotalDuration()));
            }
            // 状态
            EduSwitchEnum switchEnum = EduSwitchEnum.getByKey(examVO.getStatus());
            if (switchEnum != null) {
                detailVO.setStatus(switchEnum.getDesc());
            }
            // ========== 科目安排 ==========
            List<SubjectInfoPO> arrangements = parseSubjectArrangements(examVO.getCompositionInfo());
            detailVO.setSubjectInfoPOList(arrangements);

            detailVOList.add(detailVO);
        }

        return detailVOList;
    }

    /**
     * 格式化时长显示
     */
    public static String formatDuration(Integer minutes) {
        if (minutes == null || minutes <= 0) {
            return "0分钟";
        }

        int hours = minutes / 60;
        int mins = minutes % 60;

        if (hours > 0) {
            return hours + "小时" + (mins > 0 ? mins + "分钟" : "");
        } else {
            return mins + "分钟";
        }
    }

    /**
     * 解析科目安排信息
     */
    public static List<SubjectInfoPO> parseSubjectArrangements(String compositionInfo) {
        List<SubjectInfoPO> arrangements = new ArrayList<>();

        if (StringUtils.isBlank(compositionInfo)) {
            return arrangements;
        }

        try {
            // 解析JSON为CompositionInfoPO对象列表
            List<CompositionInfoPO> compositionList = JSON.parseArray(compositionInfo, CompositionInfoPO.class);

            if (!CollectionUtils.isEmpty(compositionList)) {
                for (CompositionInfoPO composition : compositionList) {
                    SubjectInfoPO arrangement = new SubjectInfoPO();

                    // 构建学科名称（如：数学 HL）
                    arrangement.setSubjectInfo(buildSubjectName(composition));

                    // 试卷类型
                    EduPaperTypeEnum paperTypeEnum = EduPaperTypeEnum.getByKey(composition.getSubjectPaper());
                    if (paperTypeEnum != null) {
                        arrangement.setSubjectPaper(paperTypeEnum.getDesc());
                    }

                    // 考试时间（如：08:30 - 10:30）
                    arrangement.setTimePeriod(buildExamTime(composition.getStartTime(), composition.getEndTime()));

                    // 计算时长
                    String duration = calculateSubjectDuration(composition.getStartTime(), composition.getEndTime());
                    arrangement.setIntervalDuration(duration);

                    arrangements.add(arrangement);
                }
            }
        } catch (Exception e) {
            logger.warn("解析考试组成信息失败: {}", compositionInfo, e);
        }

        return arrangements;
    }

    /**
     * 构建学科名称
     */
    public static String buildSubjectName(CompositionInfoPO composition) {
        StringBuilder nameBuilder = new StringBuilder();

        // 学科编码
        if (StringUtils.isNotBlank(composition.getSubjectCode())) {
            EduSubSubjectEnum subSubjectEnum = EduSubSubjectEnum.getByKey(composition.getSubjectCode());
            if (subSubjectEnum != null) {
                nameBuilder.append(subSubjectEnum.getDesc());
            }
        }

        // 学科类型
        if (StringUtils.isNotBlank(composition.getSubjectType())) {
            if (nameBuilder.length() > 0) {
                nameBuilder.append(" ");
            }
            nameBuilder.append(composition.getSubjectType());
        }

        // 学科层级
        if (StringUtils.isNotBlank(composition.getSubjectLevel())) {
            if (nameBuilder.length() > 0) {
                nameBuilder.append(" ");
            }
            nameBuilder.append(composition.getSubjectLevel());
        }

        return nameBuilder.toString();
    }

    /**
     * 构建考试时间显示
     */
    public static String buildExamTime(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return "";
        }
        return startTime + Constant.HYPHEN + endTime;
    }

    /**
     * 计算科目时长
     */
    public static String calculateSubjectDuration(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return "";
        }

        try {
            // 解析时间格式 "HH:mm"
            String[] startParts = startTime.split(Constant.COLON);
            String[] endParts = endTime.split(Constant.COLON);

            int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);

            // 处理跨天情况
            if (endMinutes <= startMinutes) {
                endMinutes += 24 * 60;
            }

            int durationMinutes = endMinutes - startMinutes;
            return durationMinutes + "分钟";

        } catch (Exception e) {
            logger.warn("计算科目时长失败: startTime={}, endTime={}", startTime, endTime, e);
            return "";
        }
    }
}
