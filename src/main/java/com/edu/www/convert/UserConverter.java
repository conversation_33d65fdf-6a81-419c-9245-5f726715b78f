package com.edu.www.convert;

import com.edu.www.dto.UserDTO;
import com.edu.www.vo.UserVO;

import java.util.Objects;

/**
 * 用户信息转换
 */
public class UserConverter {
    /**
     * UserVO转换UserDTO
     *
     * @param userVO
     * @return
     */
    public static UserDTO userVO2DTO(UserVO userVO) {
        UserDTO userDTO = new UserDTO();
        if (Objects.nonNull(userVO)) {
            userDTO.setId(userVO.getId());
            userDTO.setLastName(userVO.getLastName());
            userDTO.setFirstName(userVO.getFirstName());
            userDTO.setNameZh(userVO.getNameZh());
            userDTO.setNameEn(userVO.getNameEn());
            userDTO.setGender(userVO.getGender());
            userDTO.setBirthDate(userVO.getBirthDate());
            userDTO.setBirthdayType(userVO.getBirthdayType());
            userDTO.setBirthday(userVO.getBirthday());
            userDTO.setDepartmentName(userVO.getDepartmentName());
            userDTO.setDepartmentCode(userVO.getDepartmentCode());
            userDTO.setEmpNo(userVO.getEmpNo());
            userDTO.setEmail(userVO.getEmail());
            userDTO.setCreatedAt(userVO.getCreatedAt());
            userDTO.setCreatedBy(userVO.getCreatedBy());
            userDTO.setUpdatedAt(userVO.getUpdatedAt());
            userDTO.setUpdatedBy(userVO.getUpdatedBy());
        }
        return userDTO;
    }
}
