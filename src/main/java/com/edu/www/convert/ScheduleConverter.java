package com.edu.www.convert;

import com.alibaba.fastjson.JSON;
import com.edu.www.constants.Constant;
import com.edu.www.dto.TmpScheduleDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ScheduleConverter {
    /**
     * 创建临时课程表DTO对象
     *
     * @param positionKey   位置键值
     * @param courses       课程列表
     * @param tmpScheduleVO 临时课程表VO对象
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param userName      用户名
     * @return 临时课程表DTO对象
     */
    public static TmpScheduleDTO createTmpScheduleDTO(String positionKey, List<ScheduleVO> courses,
                                                      TmpScheduleVO tmpScheduleVO, Date startDate,
                                                      Date endDate, String userName) {
        // 创建新的DTO对象
        TmpScheduleDTO tmpScheduleDTO = new TmpScheduleDTO();
        BeanUtils.copyProperties(tmpScheduleVO, tmpScheduleDTO);

        // 设置开始和结束日期
        tmpScheduleDTO.setStartDate(startDate);
        tmpScheduleDTO.setEndDate(endDate);

        // 设置部门描述
        if (StringUtils.isNotBlank(tmpScheduleDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(tmpScheduleDTO.getDepartmentCode());
            if (deptEnum != null) {
                tmpScheduleDTO.setDepartment(deptEnum.getDesc());
            }
        }

        // 设置position为 "monday-Period 1" 这样的格式
        tmpScheduleDTO.setPosition(positionKey);

        // 收集科目名称并去重
        List<String> uniqueSubjects = courses.stream()
                .map(ScheduleVO::getSubjectName)
                .distinct()
                .collect(Collectors.toList());

        // 构建科目键值
        String combinedSubjectKey = ScheduleConverter.buildCombinedSubjectKey(uniqueSubjects);

        // 构建新的JSON内容格式
        ClassContentVO classContentVO = new ClassContentVO();
        // 设置title字段
        classContentVO.setTitle(combinedSubjectKey);
        // 构建data数组
        List<ClassContentDataVO> contentDataVOList = new ArrayList<>();
        for (ScheduleVO course : courses) {
            ClassContentDataVO dataVO = new ClassContentDataVO();
            dataVO.setSubjectLevel(course.getSubjectLevel());
            dataVO.setTeacherName(course.getTeacherName());
            dataVO.setSubjectName(course.getSubjectName());
            dataVO.setClassRoomCode(course.getClassRoomCode());
            contentDataVOList.add(dataVO);
        }
        // 将data列表添加到contentMap
        classContentVO.setData(contentDataVOList);

        // 将内容Map转换为JSON字符串并设置到content字段
        tmpScheduleDTO.setContent(JSON.toJSONString(classContentVO));

        // 设置创建人和更新人
        tmpScheduleDTO.setCreatedBy(userName);
        tmpScheduleDTO.setUpdatedBy(userName);

        return tmpScheduleDTO;
    }

    /**
     * 构建组合科目键值
     *
     * @param uniqueSubjects 唯一科目列表
     * @return 组合后的科目键值
     */
    public static String buildCombinedSubjectKey(List<String> uniqueSubjects) {
        // 如果有多个科目名称且其中包含"Self-study"，则排除"Self-study"进行拼接
        if (uniqueSubjects.size() > 1 && uniqueSubjects.contains(Constant.SELF_STUDY)) {
            return uniqueSubjects.stream()
                    .filter(subject -> !Constant.SELF_STUDY.equals(subject))
                    .sorted()
                    .collect(Collectors.joining(Constant.SLASH));
        } else {
            // 所有科目名称拼接
            return uniqueSubjects.stream()
                    .sorted()
                    .collect(Collectors.joining(Constant.SLASH));
        }
    }

    /**
     * 将List<TmpScheduleVO> 转换为 List<ActiveClassVO>
     *
     * @param tmpScheduleVOList
     * @return
     */
    public static List<ActiveClassVO> tmpScheduleVOList2ActiveClassVOList(List<TmpScheduleVO> tmpScheduleVOList) {
        List<ActiveClassVO> resultList = new ArrayList<>();
        tmpScheduleVOList.forEach(vo -> {
            ClassContentVO classContentVO = JSON.parseObject(vo.getContent(), ClassContentVO.class);

            if (Objects.nonNull(classContentVO) && !CollectionUtils.isEmpty(classContentVO.getData())) {
                classContentVO.getData().forEach(data -> {
                    ActiveClassVO activeClassVO = new ActiveClassVO();
                    activeClassVO.setTeacherName(data.getTeacherName());
                    activeClassVO.setSubjectName(data.getSubjectName());
                    activeClassVO.setClassRoomCode(data.getClassRoomCode());
                    activeClassVO.setGradeName(vo.getGradeCode());
                    activeClassVO.setClassName(vo.getClassCode());
                    activeClassVO.setStartTime(DateUtil.dateToTime(vo.getStartDate()));
                    activeClassVO.setEndTime(DateUtil.dateToTime(vo.getEndDate()));
                    resultList.add(activeClassVO);
                });
            }
        });

        return resultList;
    }

    /**
     * 将List<ScheduleVO> 转换为 List<ActiveClassVO>
     *
     * @param scheduleVOList
     * @return
     */
    public static List<ActiveClassVO> scheduleVOList2ActiveClassVOList(List<ScheduleVO> scheduleVOList) {
        List<ActiveClassVO> activeClassList = new ArrayList<>();
        scheduleVOList.forEach(vo -> {
            ActiveClassVO activeClassVO = new ActiveClassVO();
            activeClassVO.setTeacherName(vo.getTeacherName());
            activeClassVO.setSubjectName(vo.getSubjectName());
            activeClassVO.setGradeName(vo.getGradeCode());
            activeClassVO.setClassName(vo.getClassCode());
            activeClassVO.setClassRoomCode(vo.getClassRoomCode());
            activeClassVO.setStartTime(vo.getStartTime());
            activeClassVO.setEndTime(vo.getEndTime());

            activeClassList.add(activeClassVO);
        });
        return activeClassList;
    }
}
