package com.edu.www.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.edu.www.constants.Constant;
import com.edu.www.dto.BookDTO;
import com.edu.www.vo.BookVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 书籍信息转换器
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public class BookConverter {

    /**
     * 将JSON字符串转换为年级List
     *
     * @param bookVO 书籍VO对象
     */
    public static void convertGradeLevelToList(BookVO bookVO) {
        if (bookVO != null && StringUtils.isNotBlank(bookVO.getGradeLevel())) {
            try {
                // 尝试解析JSON数组
                JSONArray jsonArray = JSON.parseArray(bookVO.getGradeLevel());
                List<String> gradeLevels = jsonArray.toJavaList(String.class);
                bookVO.setGradeLevels(gradeLevels);
            } catch (Exception e) {
                // 如果JSON解析失败，按逗号分隔处理（兼容旧数据）
                List<String> gradeLevels = Arrays.asList(bookVO.getGradeLevel().split(Constant.SEPARATOR));
                bookVO.setGradeLevels(gradeLevels);
            }
        }
    }

    /**
     * 将年级List转换为JSON字符串
     *
     * @param bookDTO 书籍DTO对象
     */
    public static void convertGradeLevelsToString(BookDTO bookDTO) {
        if (Objects.nonNull(bookDTO) && !CollectionUtils.isEmpty(bookDTO.getGradeLevels())) {
            // 将List转换为JSON字符串存储
            String gradeLevelJsonString = JSON.toJSONString(bookDTO.getGradeLevels());
            bookDTO.setGradeLevel(gradeLevelJsonString);
        }
    }
}
