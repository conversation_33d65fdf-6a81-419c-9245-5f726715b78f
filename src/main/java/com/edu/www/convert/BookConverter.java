package com.edu.www.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.edu.www.constants.Constant;
import com.edu.www.dto.BookDTO;
import com.edu.www.vo.BookVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 书籍信息转换器
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public class BookConverter {

    /**
     * 将JSON字符串转换为年级List
     *
     * @param bookVO 书籍VO对象
     */
    public static void convertGradeLevelToList(BookVO bookVO) {
        if (bookVO != null && StringUtils.isNotBlank(bookVO.getGradeLevel())) {
            try {
                // 尝试解析JSON数组
                JSONArray jsonArray = JSON.parseArray(bookVO.getGradeLevel());
                List<String> gradeLevels = jsonArray.toJavaList(String.class);
                bookVO.setGradeLevels(gradeLevels);
            } catch (Exception e) {
                // 如果JSON解析失败，按逗号分隔处理（兼容旧数据）
                List<String> gradeLevels = Arrays.asList(bookVO.getGradeLevel().split(Constant.SEPARATOR));
                bookVO.setGradeLevels(gradeLevels);
            }
        }
    }

    /**
     * 将年级List转换为JSON字符串
     *
     * @param bookDTO 书籍DTO对象
     */
    public static void convertGradeLevelsToString(BookDTO bookDTO) {
        if (Objects.nonNull(bookDTO) && !CollectionUtils.isEmpty(bookDTO.getGradeLevels())) {
            // 将List转换为JSON字符串存储
            String gradeLevelJsonString = JSON.toJSONString(bookDTO.getGradeLevels());
            bookDTO.setGradeLevel(gradeLevelJsonString);
        }
    }

    /**
     * 从请求参数Map转换为BookVO对象
     * 包含BookVO的所有字段
     *
     * @param paramMap 请求参数Map
     * @param frontCoverImageFile 封面图片文件
     * @param backCoverImageFile 封底图片文件
     * @return BookVO对象
     * @throws ParseException 日期解析异常
     */
    public static BookVO convertFromRequestParams(Map<String, String> paramMap,
                                                  MultipartFile frontCoverImageFile,
                                                  MultipartFile backCoverImageFile) throws ParseException {
        BookVO bookVO = new BookVO();

        // 基本字符串字段 - 包含BookVO的所有String字段
        bookVO.setId(paramMap.get("id"));
        bookVO.setBookName(paramMap.get("bookName"));
        bookVO.setAuthor(paramMap.get("author"));
        bookVO.setIsbn(paramMap.get("isbn"));
        bookVO.setPublisher(paramMap.get("publisher"));
        bookVO.setBookCategory(paramMap.get("bookCategory"));
        bookVO.setSubjectCode(paramMap.get("subjectCode"));
        bookVO.setGradeLevel(paramMap.get("gradeLevel"));
        bookVO.setSupplierId(paramMap.get("supplierId"));
        bookVO.setStorageLocation(paramMap.get("storageLocation"));
        bookVO.setBookCondition(paramMap.get("bookCondition"));
        bookVO.setFrontCoverImage(paramMap.get("frontCoverImage"));
        bookVO.setBackCoverImage(paramMap.get("backCoverImage"));
        bookVO.setScannedBy(paramMap.get("scannedBy"));
        bookVO.setBookStatus(paramMap.get("bookStatus"));
        bookVO.setDescription(paramMap.get("description"));
        bookVO.setCreatedBy(paramMap.get("createdBy"));
        bookVO.setUpdatedBy(paramMap.get("updatedBy"));

        // 处理年级列表
        String gradeLevels = paramMap.get("gradeLevels");
        if (StringUtils.isNotBlank(gradeLevels)) {
            bookVO.setGradeLevels(Arrays.asList(gradeLevels.split(",")));
        }

        // 处理BigDecimal类型字段
        convertStringToBigDecimal(paramMap.get("price"), bookVO::setPrice);
        convertStringToBigDecimal(paramMap.get("purchasePrice"), bookVO::setPurchasePrice);

        // 处理Integer类型字段
        convertStringToInteger(paramMap.get("currentStock"), bookVO::setCurrentStock);
        convertStringToInteger(paramMap.get("totalInStock"), bookVO::setTotalInStock);
        convertStringToInteger(paramMap.get("totalOutStock"), bookVO::setTotalOutStock);
        convertStringToInteger(paramMap.get("minStockAlert"), bookVO::setMinStockAlert);
        convertStringToInteger(paramMap.get("maxStockLimit"), bookVO::setMaxStockLimit);

        // 处理Date类型字段
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        convertStringToDate(paramMap.get("publicationDate"), sdf, bookVO::setPublicationDate);
        convertStringToDate(paramMap.get("scanDate"), sdf, bookVO::setScanDate);
        convertStringToDate(paramMap.get("createdAt"), sdf, bookVO::setCreatedAt);
        convertStringToDate(paramMap.get("updatedAt"), sdf, bookVO::setUpdatedAt);

        // 设置文件
        bookVO.setFrontCoverImageFile(frontCoverImageFile);
        bookVO.setBackCoverImageFile(backCoverImageFile);

        return bookVO;
    }

    /**
     * 字符串转BigDecimal的辅助方法
     */
    private static void convertStringToBigDecimal(String value, java.util.function.Consumer<BigDecimal> setter) {
        if (StringUtils.isNotBlank(value)) {
            try {
                setter.accept(new BigDecimal(value));
            } catch (NumberFormatException e) {
                // 忽略格式错误，保持字段为null
            }
        }
    }

    /**
     * 字符串转Integer的辅助方法
     */
    private static void convertStringToInteger(String value, java.util.function.Consumer<Integer> setter) {
        if (StringUtils.isNotBlank(value)) {
            try {
                setter.accept(Integer.parseInt(value));
            } catch (NumberFormatException e) {
                // 忽略格式错误，保持字段为null
            }
        }
    }

    /**
     * 字符串转Date的辅助方法
     */
    private static void convertStringToDate(String value, SimpleDateFormat sdf, java.util.function.Consumer<Date> setter) {
        if (StringUtils.isNotBlank(value)) {
            try {
                setter.accept(sdf.parse(value));
            } catch (ParseException e) {
                // 忽略格式错误，保持字段为null
            }
        }
    }
}
