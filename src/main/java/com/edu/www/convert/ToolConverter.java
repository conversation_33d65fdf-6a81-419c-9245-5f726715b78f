package com.edu.www.convert;

import com.edu.www.enums.*;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.ScheduleVO;
import com.edu.www.vo.TeacherScheduleVO;
import com.edu.www.vo.TeacherVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 教师课表信息转换
 */
public class ToolConverter {
    /**
     * scheduleVOList转换TeacherScheduleVOList
     *
     * @param map
     * @param scheduleVOList
     * @return
     */
    public static List<TeacherScheduleVO> scheduleVOList2TeacherScheduleVOList(Map<String, TeacherVO> map, List<ScheduleVO> scheduleVOList) {
        List<TeacherScheduleVO> teacherScheduleVOS = new ArrayList<>();

        Map<String, String> subjectMap = EduSubjectCodeEnum.getAll();
        Map<String, String> gradeMap = EduGradeCodeEnum.getAll();
        Map<String, String> weekdayMap = EduWeekdayEnum.getAllDesc();
        Map<String, String> dayPartMap = EduDayPartEnum.getAll();
        Map<String, String> periodMap = EduPeriodEnum.getAll();

        scheduleVOList.forEach(e -> {
            TeacherVO teacherVO = map.get(e.getTeacherId());
            TeacherScheduleVO teacherScheduleVO = new TeacherScheduleVO();
            teacherScheduleVO.setTeacherId(e.getTeacherId());
            teacherScheduleVO.setTeacherNameZh(teacherVO.getNameZh());
            teacherScheduleVO.setTeacherNameEn(teacherVO.getNameEn());
            teacherScheduleVO.setGender(EduGenderEnum.MALE.getKey().equals(teacherVO.getGender()) ? "男" : "女");
            teacherScheduleVO.setYear(e.getYear());
            teacherScheduleVO.setSemester(e.getSemester());
            teacherScheduleVO.setDepartment(e.getDepartment());
            teacherScheduleVO.setSubjectName(subjectMap.get(e.getSubjectName()));
            teacherScheduleVO.setGradeCode(gradeMap.get(e.getGradeCode()));
            teacherScheduleVO.setClassCode(e.getClassCode());
            teacherScheduleVO.setSubjectLevel(e.getSubjectLevel());
            teacherScheduleVO.setClassRoomCode(e.getClassRoomCode());
            teacherScheduleVO.setWeekday(weekdayMap.get(e.getWeekday()));
            teacherScheduleVO.setDayPart(dayPartMap.get(e.getDayPart()));
            teacherScheduleVO.setPeriod(periodMap.get(e.getPeriod()));
            teacherScheduleVO.setStartTime(DateUtil.formatTime(e.getStartTime(), DateUtil.FORMAT_TIME_HOUR_MINUTE));
            teacherScheduleVO.setEndTime(DateUtil.formatTime(e.getEndTime(), DateUtil.FORMAT_TIME_HOUR_MINUTE));
            teacherScheduleVOS.add(teacherScheduleVO);
        });
        return teacherScheduleVOS;
    }
}
