package com.edu.www.convert;

import com.edu.www.constants.Constant;
import com.edu.www.dto.StudentDTO;
import com.edu.www.po.MBStudentPO;
import com.edu.www.service.StudentService;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.ClassesVO;
import com.edu.www.vo.GroupReadingCertificateVO;
import com.edu.www.vo.StudentCardNumVO;
import com.edu.www.vo.StudentVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class StudentConverter {
    private static final Logger logger = LoggerFactory.getLogger(StudentConverter.class);

    /**
     * 处理并设置入学日期信息
     *
     * @param studentList 学生列表
     * @param certificate 证书对象
     */
    public static void processAdmissionDates(List<StudentVO> studentList, GroupReadingCertificateVO certificate) {
        // 获取去重后的admissionDate，先转换为年月格式再去重排序
        List<String> formattedDates = studentList.stream()
                .map(StudentVO::getAdmissionDate)
                .filter(Objects::nonNull)
                .map(DateUtil::toYearMonthDate)
                .filter(Objects::nonNull)
                .distinct()
                .sorted(Date::compareTo)
                .map(date -> DateUtil.formatReadingCertificateDate(date, false, true))
                .collect(Collectors.toList());

        if (formattedDates.isEmpty()) {
            return;
        }

        String result = switch (formattedDates.size()) {
            case 1 -> formattedDates.get(0);
            case 2 -> String.join("和", formattedDates);
            default -> {
                // 除最后一个元素外的所有元素，用"、"连接
                String prefix = formattedDates.subList(0, formattedDates.size() - 1)
                        .stream()
                        .collect(Collectors.joining(Constant.COMMA));
                // 最后一个元素用"和"连接
                yield prefix + "和" + formattedDates.get(formattedDates.size() - 1);
            }
        };

        certificate.setAdmissionDate(result);
    }

    /**
     * 处理学生信息
     *
     * @param studentList 学生列表
     * @return 学生证件信息列表
     */
    public static List<StudentCardNumVO> getStudentCardNumVOList(List<StudentVO> studentList) {
        AtomicInteger counter = new AtomicInteger(1);

        return studentList.stream()
                .map(student -> {
                    StudentCardNumVO vo = new StudentCardNumVO();
                    vo.setIndex(counter.getAndIncrement());
                    vo.setName(student.getNameZh());
                    vo.setCardNum(student.getCardNum());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建更新学生DTO
     *
     * @param mbStudent   ManageBac学生数据
     * @param studentId   现有学生ID
     * @param currentUser 当前用户
     * @return 更新DTO
     */
    public static StudentDTO buildUpdateStudentDTO(MBStudentPO mbStudent, String studentId, String currentUser) {
        StudentDTO updateDTO = new StudentDTO();
        updateDTO.setId(studentId);
        updateDTO.setStudentCode(mbStudent.getStudentId());
        updateDTO.setNameEn(buildFullName(mbStudent.getFirstName(), mbStudent.getLastName()));
        updateDTO.setNameZh(mbStudent.getFirstName() + " " + mbStudent.getLastName()); // 如果有中文名映射可以调整
        updateDTO.setEmail(mbStudent.getEmail());
        updateDTO.setGender(convertGender(mbStudent.getGender()).byteValue());
        updateDTO.setUpdatedBy(currentUser);

        return updateDTO;
    }

    /**
     * 构建新增学生DTO
     *
     * @param mbStudent   ManageBac学生数据
     * @param currentUser 当前用户
     * @return 新增DTO
     */
    public static StudentDTO buildInsertStudentDTO(MBStudentPO mbStudent, String currentUser) {
        StudentDTO insertDTO = new StudentDTO();
        insertDTO.setStudentCode(mbStudent.getStudentId());
        insertDTO.setNameEn(buildFullName(mbStudent.getFirstName(), mbStudent.getLastName()));
        insertDTO.setNameZh(mbStudent.getFirstName() + " " + mbStudent.getLastName()); // 如果有中文名映射可以调整
        insertDTO.setEmail(mbStudent.getEmail());
        insertDTO.setGender(convertGender(mbStudent.getGender()).byteValue());
        insertDTO.setCreatedBy(currentUser);
        insertDTO.setUpdatedBy(currentUser);

        return insertDTO;
    }

    /**
     * 创建同步结果
     *
     * @param totalCount  总数
     * @param insertCount 新增数
     * @param updateCount 更新数
     * @return 同步结果Map
     */
    public static Map<String, Object> createSyncResult(int totalCount, int insertCount, int updateCount) {
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("insertCount", insertCount);
        result.put("updateCount", updateCount);
        result.put("syncTime", new Date());
        return result;
    }

    /**
     * 构建API过滤条件信息
     *
     * @param studentIds       学生ID列表
     * @param yearGroupId      年级组ID
     * @return 过滤条件信息
     */
    public static Map<String, Object> buildApiFilterInfo(List<String> studentIds, Long yearGroupId) {
        Map<String, Object> filterInfo = new HashMap<>();
        filterInfo.put("studentIds", studentIds);
        filterInfo.put("yearGroupId", yearGroupId);
        filterInfo.put("onlyActiveStudents", true);
        filterInfo.put("filterType", "ManageBac API Parameters");
        return filterInfo;
    }

    /**
     * 构建API查询参数
     *
     * @param studentIds  学生ID列表
     * @param yearGroupId 年级组ID
     * @param page        页码
     * @return 查询参数Map
     */
    public static Map<String, String> buildApiQueryParams(List<String> studentIds, Long yearGroupId, int page) {
        Map<String, String> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("per_page", "100"); // 每页100条记录

        // 如果指定了年级组ID，使用专门的参数
        if (yearGroupId != null) {
            params.put("year_group_ids", String.valueOf(yearGroupId));
        }

        // 处理学生ID搜索
        if (!CollectionUtils.isEmpty(studentIds)) {
            // 限制每次搜索的学生ID数量，避免URL过长和搜索效果不佳
            final int MAX_STUDENT_IDS_PER_QUERY = 5;

            if (studentIds.size() <= MAX_STUDENT_IDS_PER_QUERY) {
                // 少量学生ID，直接用空格连接进行搜索
                String searchQuery = String.join(" ", studentIds);
                params.put("q", searchQuery);
                logger.debug("使用q参数搜索学生ID: {}", searchQuery);
            } else {
                // 大量学生ID，记录警告并只取前几个
                List<String> limitedIds = studentIds.subList(0, MAX_STUDENT_IDS_PER_QUERY);
                String searchQuery = String.join(" ", limitedIds);
                params.put("q", searchQuery);
                logger.warn("学生ID数量过多({}个)，只搜索前{}个: {}",
                           studentIds.size(), MAX_STUDENT_IDS_PER_QUERY, searchQuery);
            }
        }

        // 只获取活跃的在读学生
        // params.put("archived", "0");
        // params.put("status", "enrolled");

        logger.debug("构建的API查询参数: {}", params);
        return params;
    }

    /**
     * 过滤API学生数据
     *
     * @param students    学生列表
     * @param studentIds  学生ID列表
     * @param yearGroupId 年级组ID
     * @return 过滤后的学生列表
     */
    public static List<MBStudentPO> filterApiStudents(List<MBStudentPO> students, List<String> studentIds, Long yearGroupId) {
        if (CollectionUtils.isEmpty(students)) {
            return new ArrayList<>();
        }

        return students.stream()
                .filter(student -> {

                    // 过滤学生ID
                    if (!CollectionUtils.isEmpty(studentIds) &&
                            (student.getStudentId() == null || !studentIds.contains(student.getStudentId()))) {
                        return false;
                    }

                    // 过滤年级组ID
                    if (yearGroupId != null && !yearGroupId.equals(student.getYearGroupId())) {
                        return false;
                    }

                    // 只要活跃学生
                    return student.getArchived() == null || !student.getArchived();
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建完整姓名
     *
     * @param firstName 名
     * @param lastName  姓
     * @return 完整姓名
     */
    public static String buildFullName(String firstName, String lastName) {
        if (StringUtils.isBlank(firstName) && StringUtils.isBlank(lastName)) {
            return "";
        }
        if (StringUtils.isBlank(firstName)) {
            return lastName;
        }
        if (StringUtils.isBlank(lastName)) {
            return firstName;
        }
        return firstName + " " + lastName;
    }

    /**
     * 转换性别
     *
     * @param gender ManageBac性别字符串
     * @return 系统性别代码
     */
    private static Integer convertGender(String gender) {
        if (StringUtils.isBlank(gender)) {
            return null;
        }
        return "Female".equalsIgnoreCase(gender) ? 0 : 1;
    }
}
