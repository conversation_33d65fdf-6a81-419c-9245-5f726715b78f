package com.edu.www.handler;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.enums.EduDPDutyTypeEnum;
import com.edu.www.enums.EduWeekdayEnum;
import com.edu.www.po.ExcelStylesPO;
import com.edu.www.po.DutyDetailPO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 值日Excel自定义写入处理器
 * 使用现代Java特性优化的版本，支持颜色设置
 */
public class DutyExcelWriteHandler implements SheetWriteHandler, CellWriteHandler {

    private static final Logger logger = LoggerFactory.getLogger(DutyExcelWriteHandler.class);

    private final Map<String, Map<String, Object>> weeklyData;
    private Workbook workbook;
    private Sheet sheet;

    private ExcelStylesPO styles;

    // 使用常量定义
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("M月d日");
    private static final short HEADER_ROW_HEIGHT = 40;
    private static final short DATA_ROW_HEIGHT = 40;
    private static final int WEEK_COLUMN_WIDTH = 3000;
    private static final int TIME_COLUMN_WIDTH = 4000;
    private static final int WEEKDAY_COLUMN_WIDTH = 3000;

    // 颜色相关常量
    private static final String COLOR_HEX_PATTERN = "[0-9A-Fa-f]{6}";
    private static final int COLOR_HEX_LENGTH = 6;
    private static final String COLOR_PREFIX = "#";

    // 字体相关常量
    private static final short SMALL_FONT_SIZE = 8;

    public DutyExcelWriteHandler(Map<String, Map<String, Object>> weeklyData) {
        this.weeklyData = Objects.requireNonNull(weeklyData, "weeklyData cannot be null");
    }

    // ==================== 工具方法 ====================

    /**
     * 验证颜色字符串格式
     */
    private static boolean isValidColorFormat(String colorHex) {
        if (colorHex == null || colorHex.isEmpty()) {
            return false;
        }

        String normalized = colorHex.startsWith(COLOR_PREFIX)
                ? colorHex.substring(1)
                : colorHex;

        return normalized.length() == COLOR_HEX_LENGTH && normalized.matches(COLOR_HEX_PATTERN);
    }

    /**
     * 标准化颜色字符串（移除#号，转为小写）
     */
    private static String normalizeColorString(String colorHex) {
        if (colorHex == null || colorHex.isEmpty()) {
            return "";
        }

        String normalized = colorHex.startsWith(COLOR_PREFIX)
                ? colorHex.substring(1)
                : colorHex;

        return normalized.toLowerCase();
    }

    /**
     * 安全地解析十六进制字符串为整数
     */
    private static int parseHexSafely(String hex, int defaultValue) {
        try {
            return Integer.parseInt(hex, 16);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        this.workbook = writeWorkbookHolder.getWorkbook();
        this.sheet = writeSheetHolder.getSheet();

        // 初始化样式
        this.styles = initStyles();

        // 写入所有数据
        writeAllData();
    }

    /**
     * 初始化样式 - 使用构建器模式
     */
    private ExcelStylesPO initStyles() {
        return ExcelStylesPO.builder()
                .headerStyle(createHeaderStyle())
                .weekTitleStyle(createWeekTitleStyle())
                .dataStyle(createDataStyle())
                .dutyTypeStyle(createDutyTypeStyle())
                .build();
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle() {
        var style = workbook.createCellStyle();
        configureBorders(style);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setWrapText(true);

        var font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建Week标题样式
     */
    private CellStyle createWeekTitleStyle() {
        var style = workbook.createCellStyle();
        configureBorders(style);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        var font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle() {
        var style = workbook.createCellStyle();
        configureBorders(style);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        return style;
    }

    /**
     * 创建值日类型样式
     */
    private CellStyle createDutyTypeStyle() {
        var style = workbook.createCellStyle();
        configureBorders(style);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        var font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        return style;
    }

    /**
     * 配置边框 - 提取公共逻辑
     */
    private void configureBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
    }

    /**
     * 写入所有数据 - 使用Stream API
     */
    private void writeAllData() {
        // 按Week顺序处理
        var sortedWeeks = weeklyData.keySet().stream()
                .sorted(Comparator.comparingInt(week ->
                        Integer.parseInt(week.replace(Constant.WEEK_PREFIX, ""))))
                .collect(Collectors.toList());

        // 使用累加器跟踪当前行
        var rowAccumulator = new int[]{0};

        // 处理每个周的数据
        sortedWeeks.forEach(weekKey -> {
            var weekData = weeklyData.get(weekKey);

            // 写入Week块数据（左侧Week标题 + 右侧数据表格）
            rowAccumulator[0] = writeWeekBlock(rowAccumulator[0], weekKey, weekData);

            // 添加空行分隔
            rowAccumulator[0]++;
        });

        // 设置列宽
        setColumnWidths();
    }

    /**
     * 写入Week块数据（按照原设计图格式）- 使用现代Java特性
     */
    private int writeWeekBlock(int startRow, String weekKey, Map<String, Object> weekData) {
        // 提取日期范围
        var dateRange = extractDateRangeFromWeekData(weekData);

        // 按值日类型分组
        var dutyTypeGroups = groupByDutyType(weekData);

        // 使用List.of替代Arrays.asList
        var dutyTypeOrder = List.of(
                EduDPDutyTypeEnum.DUTY.getDesc(),
                EduDPDutyTypeEnum.YEAR_10_AB.getDesc(),
                EduDPDutyTypeEnum.YEAR_11_AB.getDesc(),
                EduDPDutyTypeEnum.YEAR_12_SPRING.getDesc()
        );

        // 使用Stream计算数据行数
        var dataRowCount = (int) dutyTypeOrder.stream()
                .filter(dutyType -> dutyTypeGroups.containsKey(dutyType) &&
                        !dutyTypeGroups.get(dutyType).isEmpty())
                .count();

        var totalRows = 1 + dataRowCount; // 表头1行 + 数据行

        // 1. 写入表头行：星期日期/时间 + 一到日 + 具体日期
        Row headerRow = sheet.createRow(startRow);
        headerRow.setHeightInPoints(HEADER_ROW_HEIGHT);

        // Week标题（左侧第一列，垂直合并）
        createCell(headerRow, 0, weekKey, styles.weekTitleStyle());

        // 星期日期/时间标题（第二列）
        createCell(headerRow, 1, "星期日期/时间\nDutyType/Date", styles.headerStyle());

        // 星期几标题和日期（第3-9列）
        // 使用枚举值直接获取星期几缩写
        var weekdayHeaders = new String[]{
                EduWeekdayEnum.MONDAY.getAbbr(),
                EduWeekdayEnum.TUESDAY.getAbbr(),
                EduWeekdayEnum.WEDNESDAY.getAbbr(),
                EduWeekdayEnum.THURSDAY.getAbbr(),
                EduWeekdayEnum.FRIDAY.getAbbr(),
                EduWeekdayEnum.SATURDAY.getAbbr(),
                EduWeekdayEnum.SUNDAY.getAbbr()
        };

        // 使用IntStream处理星期几列
        IntStream.range(0, weekdayHeaders.length).forEach(i -> {
            var date = dateRange.getOrDefault(String.valueOf(i + 1), "");
            var formattedDate = formatDateDisplay(date);
            var cell = headerRow.createCell(i + 2);

            if (formattedDate.isEmpty()) {
                // 如果日期为空，只显示星期几
                cell.setCellValue(weekdayHeaders[i]);
            } else {
                // 使用富文本格式：星期为正常字体，日期为小号灰色字体
                var richText = workbook.getCreationHelper()
                        .createRichTextString(weekdayHeaders[i] + "\n(" + formattedDate + ")");

                // 创建小号浅灰色字体
                var smallGrayFont = workbook.createFont();
                smallGrayFont.setFontHeightInPoints((short) 8); // 更小的字体
                smallGrayFont.setColor(IndexedColors.GREY_25_PERCENT.getIndex()); // 更浅的灰色

                // 应用小号灰色字体到日期部分（括号和日期）
                int startIndex = weekdayHeaders[i].length() + 1; // "\n("的位置
                richText.applyFont(startIndex, richText.length(), smallGrayFont);

                cell.setCellValue(richText);
            }
            cell.setCellStyle(styles.headerStyle());
        });

        // 为Week标题创建垂直合并区域
        if (totalRows > 1) {
            sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + totalRows - 1, 0, 0));
        }

        // 2. 写入值日类型数据行
        final int[] currentDataRow = {startRow + 1};

        // 使用Stream API处理值日类型
        dutyTypeOrder.forEach(dutyType -> {
            var typeData = dutyTypeGroups.get(dutyType);
            if (typeData != null && !typeData.isEmpty()) {
                var row = sheet.createRow(currentDataRow[0]);
                row.setHeightInPoints(DATA_ROW_HEIGHT);

                // Week标题列已经合并，创建空单元格保持样式
                createCell(row, 0, "", styles.weekTitleStyle());

                // 值日类型 + 时间描述（第二列）
                var timeDesc = CommonConstant.DUTY_TYPE_TIME_MAP.getOrDefault(dutyType, "");

                // 创建富文本
                var richText = createDutyTypeRichText(dutyType, timeDesc);

                // 创建单元格
                var dutyTypeCell = row.createCell(1);
                dutyTypeCell.setCellValue(richText);
                dutyTypeCell.setCellStyle(styles.dutyTypeStyle());

                // 各天的教师姓名（第3-9列）- 使用IntStream，支持颜色
                IntStream.rangeClosed(1, 7).forEach(i -> {
                    var dutyDetail = typeData.get(String.valueOf(i));
                    if (dutyDetail != null) {
                        createCellWithColor(row, i + 1, dutyDetail.getTeacherName(),
                                dutyDetail.getColor(), styles.dataStyle());
                    } else {
                        createCell(row, i + 1, "", styles.dataStyle());
                    }
                });

                currentDataRow[0]++;
            }
        });

        return startRow + totalRows;
    }

    /**
     * 创建单元格的辅助方法
     */
    private void createCell(Row row, int columnIndex, String value, CellStyle style) {
        var cell = row.createCell(columnIndex);
        cell.setCellValue(value);
        cell.setCellStyle(style);
    }

    /**
     * 创建带颜色的单元格
     */
    private void createCellWithColor(Row row, int columnIndex, String value, String color, CellStyle baseStyle) {
        var cell = row.createCell(columnIndex);
        cell.setCellValue(value);

        // 添加调试日志
        if (color != null && !color.isEmpty()) {
            logger.info("设置单元格颜色: 位置=({},{}), 值='{}', 颜色='{}'",
                    row.getRowNum(), columnIndex, value, color);
        }

        // 如果有颜色设置，创建新的样式
        if (color != null && !color.isEmpty()) {
            var colorStyle = createColoredCellStyle(baseStyle, color);
            cell.setCellStyle(colorStyle);
        } else {
            cell.setCellStyle(baseStyle);
        }
    }

    /**
     * 创建带颜色的单元格样式
     */
    private CellStyle createColoredCellStyle(CellStyle baseStyle, String colorHex) {
        var colorStyle = workbook.createCellStyle();

        // 复制基础样式的所有属性
        colorStyle.cloneStyleFrom(baseStyle);

        try {
            // 解析颜色并设置背景色
            if (colorHex != null && !colorHex.isEmpty()) {
                logger.info("开始解析颜色: {}", colorHex);

                // 检查工作簿类型，XSSF支持自定义颜色
                if (workbook instanceof org.apache.poi.xssf.usermodel.XSSFWorkbook) {
                    var customColor = createCustomColor(colorHex);
                    if (customColor != null && colorStyle instanceof org.apache.poi.xssf.usermodel.XSSFCellStyle) {
                        logger.info("使用自定义颜色: {}", colorHex);
                        ((org.apache.poi.xssf.usermodel.XSSFCellStyle) colorStyle).setFillForegroundColor(customColor);
                        colorStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    } else {
                        // 备用方案：使用索引颜色
                        logger.info("自定义颜色创建失败，尝试索引颜色: {}", colorHex);
                        var indexedColor = parseIndexedColor(colorHex);
                        if (indexedColor != null) {
                            logger.info("使用索引颜色: {} -> {}", colorHex, indexedColor);
                            colorStyle.setFillForegroundColor(indexedColor);
                            colorStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        } else {
                            logger.warn("颜色 {} 无法解析，不设置背景色", colorHex);
                        }
                    }
                } else {
                    // 对于HSSF工作簿，只能使用索引颜色
                    logger.info("HSSF工作簿，使用索引颜色: {}", colorHex);
                    var indexedColor = parseIndexedColor(colorHex);
                    if (indexedColor != null) {
                        logger.info("使用索引颜色: {} -> {}", colorHex, indexedColor);
                        colorStyle.setFillForegroundColor(indexedColor);
                        colorStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    } else {
                        logger.warn("颜色 {} 无法解析，不设置背景色", colorHex);
                    }
                }
            }
        } catch (Exception e) {
            // 如果颜色解析失败，使用默认样式
            logger.warn("无法解析颜色: {}", colorHex, e);
        }

        return colorStyle;
    }

    /**
     * 创建自定义颜色（适用于XSSF工作簿）
     * 使用工具方法改进错误处理和输入验证
     */
    private XSSFColor createCustomColor(String colorHex) {
        if (!isValidColorFormat(colorHex)) {
            logger.debug("颜色字符串格式无效: {}", colorHex);
            return null;
        }

        try {
            String normalizedColor = normalizeColorString(colorHex);

            // 解析RGB值，使用安全解析方法
            int r = parseHexSafely(normalizedColor.substring(0, 2), 0);
            int g = parseHexSafely(normalizedColor.substring(2, 4), 0);
            int b = parseHexSafely(normalizedColor.substring(4, 6), 0);

            logger.debug("解析颜色 {} 为 RGB({}, {}, {})", colorHex, r, g, b);

            // 创建自定义颜色
            byte[] rgb = new byte[]{(byte) r, (byte) g, (byte) b};
            return new XSSFColor(rgb, null);

        } catch (Exception e) {
            logger.error("创建自定义颜色时发生未预期错误: {}", colorHex, e);
            return null;
        }
    }

    /**
     * 解析颜色字符串为POI索引颜色（备用方案）
     * 使用Map查找和工具方法，提高性能和可维护性
     */
    private Short parseIndexedColor(String colorHex) {
        if (colorHex == null || colorHex.isEmpty()) {
            return null;
        }

        String normalizedColor = normalizeColorString(colorHex);
        logger.debug("尝试解析颜色为索引颜色: {}", normalizedColor);

        // 使用Map查找颜色映射
        Short result = CommonConstant.FRONTEND_COLOR_MAPPING.get(normalizedColor);

        if (result == null) {
            logger.warn("颜色 {} 无法映射到索引颜色，将使用自定义颜色", colorHex);
        } else {
            logger.debug("颜色 {} 成功映射到索引颜色: {}", colorHex, result);
        }

        return result;
    }

    /**
     * 创建值日类型富文本
     * 使用常量提高可维护性
     */
    private RichTextString createDutyTypeRichText(String dutyType, String timeDesc) {
        var richText = workbook.getCreationHelper()
                .createRichTextString(dutyType + "\n" + timeDesc);

        // 创建加粗字体用于值日类型
        var boldFont = createBoldFont();

        // 创建小号浅灰色字体用于时间
        var smallGrayFont = createSmallGrayFont();

        // 应用字体样式
        richText.applyFont(0, dutyType.length(), boldFont);
        richText.applyFont(dutyType.length() + 1, richText.length(), smallGrayFont);

        return richText;
    }

    /**
     * 创建加粗字体
     */
    private Font createBoldFont() {
        var font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.BLACK.getIndex());
        return font;
    }

    /**
     * 创建小号灰色字体
     */
    private Font createSmallGrayFont() {
        var font = workbook.createFont();
        font.setFontHeightInPoints(SMALL_FONT_SIZE);
        font.setColor(IndexedColors.GREY_25_PERCENT.getIndex());
        return font;
    }

    /**
     * 设置列宽 - 使用常量
     */
    private void setColumnWidths() {
        sheet.setColumnWidth(0, WEEK_COLUMN_WIDTH); // Week标题列
        sheet.setColumnWidth(1, TIME_COLUMN_WIDTH); // 星期日期/时间列稍宽

        // 使用IntStream设置星期几列宽度
        IntStream.range(2, 9).forEach(i ->
                sheet.setColumnWidth(i, WEEKDAY_COLUMN_WIDTH));
    }

    /**
     * 从周数据中提取日期范围 - 使用Stream API优化
     */
    private Map<String, String> extractDateRangeFromWeekData(Map<String, Object> weekData) {
        return weekData.entrySet().stream()
                .filter(entry -> entry.getKey().contains(Constant.UNDERSCORE))
                .map(entry -> entry.getKey().split(Constant.UNDERSCORE))
                .filter(parts -> parts.length >= 3)
                .collect(Collectors.toMap(
                        parts -> CommonConstant.WEEKDAY_MAP.getOrDefault(parts[1], ""),
                        parts -> parts[2],
                        (existing, replacement) -> existing, // 保留第一个值
                        HashMap::new
                ))
                .entrySet().stream()
                .filter(entry -> !entry.getKey().isEmpty())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
    }

    /**
     * 格式化日期显示 - 使用Optional处理空值
     */
    private String formatDateDisplay(String dateStr) {
        return Optional.ofNullable(dateStr)
                .filter(str -> !str.isEmpty())
                .map(this::normalizeDate)
                .map(LocalDate::parse)
                .map(date -> date.format(DATE_FORMATTER))
                .orElse("");
    }

    /**
     * 标准化日期格式
     */
    private String normalizeDate(String dateStr) {
        if (dateStr.contains("年") && dateStr.contains("月") && dateStr.contains("日")) {
            return dateStr.replace("年", Constant.SUBTRACT)
                    .replace("月", Constant.SUBTRACT)
                    .replace("日", "");
        }
        return dateStr;
    }

    /**
     * 按值日类型分组 - 使用Stream API和函数式编程
     */
    private Map<String, Map<String, DutyDetailPO>> groupByDutyType(Map<String, Object> weekData) {
        return weekData.entrySet().stream()
                .filter(entry -> entry.getKey().contains(Constant.UNDERSCORE))
                .map(entry -> new DutyEntry(entry.getKey(), entry.getValue()))
                .filter(dutyEntry -> dutyEntry.isValid())
                .collect(Collectors.groupingBy(
                        DutyEntry::getDutyTypeName,
                        Collectors.toMap(
                                DutyEntry::getWeekdayNum,
                                DutyEntry::getDutyDetail,
                                (existing, replacement) -> existing,
                                HashMap::new
                        )
                ));
    }

    /**
     * 值日条目记录类 - 使用记录类封装数据
     */
    private record DutyEntry(String key, Object value) {

        public boolean isValid() {
            var parts = key.split(Constant.UNDERSCORE);
            return parts.length >= 3 &&
                    getDutyTypeName() != null &&
                    getWeekdayNum() != null;
        }

        public String getDutyTypeName() {
            var parts = key.split(Constant.UNDERSCORE);
            if (parts.length >= 3) {
                var dutyTypeWithTime = parts[0];
                return dutyTypeWithTime.contains(Constant.SUBTRACT)
                        ? dutyTypeWithTime.split(Constant.SUBTRACT)[0]
                        : dutyTypeWithTime;
            }
            return null;
        }

        public String getWeekdayNum() {
            var parts = key.split(Constant.UNDERSCORE);
            if (parts.length >= 3) {
                return CommonConstant.WEEKDAY_MAP.get(parts[1]);
            }
            return null;
        }

        public DutyDetailPO getDutyDetail() {
            if (value instanceof String) {
                // 兼容旧格式，只有教师姓名
                return new DutyDetailPO((String) value, "", "", "");
            } else if (value instanceof DutyDetailPO) {
                // 新格式，包含完整信息
                return (DutyDetailPO) value;
            }
            // 默认返回空对象
            return new DutyDetailPO("", "", "", "");
        }
    }
}
