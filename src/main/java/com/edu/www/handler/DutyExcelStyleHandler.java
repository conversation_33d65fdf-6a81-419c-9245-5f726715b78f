package com.edu.www.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 值日Excel样式处理器
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class DutyExcelStyleHandler implements CellWriteHandler {

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                               Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在单元格创建后设置样式
        if (isHead) {
            // 设置表头样式
            setHeaderStyle(writeSheetHolder, cell);
        } else {
            // 设置数据行样式
            setDataStyle(writeSheetHolder, cell, relativeRowIndex);
        }
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                     WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 数据转换后的处理
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 单元格处理完成后的操作
        if (!isHead && relativeRowIndex != null) {
            // 为每个Week添加分组边框
            addWeekGroupBorder(writeSheetHolder, cell, relativeRowIndex);
        }
    }

    /**
     * 设置表头样式
     */
    private void setHeaderStyle(WriteSheetHolder writeSheetHolder, Cell cell) {
        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        CellStyle headerStyle = workbook.createCellStyle();
        
        // 设置背景色
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        
        // 设置字体
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        headerStyle.setFont(headerFont);
        
        // 设置对齐方式
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        cell.setCellStyle(headerStyle);
    }

    /**
     * 设置数据行样式
     */
    private void setDataStyle(WriteSheetHolder writeSheetHolder, Cell cell, Integer relativeRowIndex) {
        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        CellStyle dataStyle = workbook.createCellStyle();

        // 设置边框
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 10);

        // 根据行类型设置不同的背景色
        if (isWeekHeaderRow(relativeRowIndex)) {
            // Week标题行 - 浅蓝色背景
            dataStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            dataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            dataFont.setBold(true);
            dataStyle.setFont(dataFont);
        } else if (isDutyTypeRow(relativeRowIndex)) {
            // 值日类型行，根据类型设置不同颜色
            String cellValue = "";
            try {
                cellValue = cell.getStringCellValue();
            } catch (Exception e) {
                // 忽略异常，使用空字符串
            }

            if (cellValue != null && cellValue.contains("值日")) {
                // 值日行 - 浅绿色背景
                dataStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                dataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            }

            dataStyle.setFont(dataFont);
        } else {
            dataStyle.setFont(dataFont);
        }

        cell.setCellStyle(dataStyle);
    }

    /**
     * 添加Week分组边框
     */
    private void addWeekGroupBorder(WriteSheetHolder writeSheetHolder, Cell cell, Integer relativeRowIndex) {
        // 每个Week组之间添加粗边框分隔
        if (isWeekEndRow(relativeRowIndex)) {
            CellStyle style = cell.getCellStyle();
            if (style != null) {
                style.setBorderBottom(BorderStyle.THICK);
            }
        }
    }

    /**
     * 判断是否为Week标题行
     */
    private boolean isWeekHeaderRow(Integer relativeRowIndex) {
        // Week标题行的逻辑：每5行一个Week，第0行是Week标题
        return relativeRowIndex % 5 == 0;
    }

    /**
     * 判断是否为值日类型行
     */
    private boolean isDutyTypeRow(Integer relativeRowIndex) {
        // 值日类型行：Week标题行后的4行
        int positionInWeek = relativeRowIndex % 5;
        return positionInWeek >= 1 && positionInWeek <= 4;
    }

    /**
     * 判断是否为Week结束行
     */
    private boolean isWeekEndRow(Integer relativeRowIndex) {
        // Week的最后一行
        return relativeRowIndex % 5 == 4;
    }
}
