package com.edu.www.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 值日统计Excel样式处理器
 * 设置边框和居中对齐
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class DutyStatisticExcelStyleHandler implements CellWriteHandler {

    private CellStyle headCellStyle;
    private CellStyle contentCellStyle;

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
                                Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // 初始化样式
        if (headCellStyle == null || contentCellStyle == null) {
            initStyles(writeSheetHolder.getSheet().getWorkbook());
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell,
                               Head head, Integer relativeRowIndex, Boolean isHead) {
        // 应用样式
        if (isHead != null && isHead) {
            cell.setCellStyle(headCellStyle);
        } else {
            cell.setCellStyle(contentCellStyle);
        }
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                     WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 确保样式被应用
        if (isHead != null && isHead) {
            cell.setCellStyle(headCellStyle);
        } else {
            cell.setCellStyle(contentCellStyle);
        }
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 最终确保样式被应用
        if (isHead != null && isHead) {
            cell.setCellStyle(headCellStyle);
        } else {
            cell.setCellStyle(contentCellStyle);
        }
    }

    /**
     * 初始化样式
     */
    private void initStyles(Workbook workbook) {
        // 创建表头样式
        headCellStyle = createHeadStyle(workbook);
        // 创建内容样式
        contentCellStyle = createContentStyle(workbook);
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeadStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色为黑色
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建字体
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建内容样式
     */
    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色为黑色
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建字体
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }
}
