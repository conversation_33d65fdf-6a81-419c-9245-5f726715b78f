package com.edu.www.vo;

import java.util.Date;

/**
 * 书籍领用值对象
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class BookBorrowingVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 部门(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 年度
     */
    private String year;

    /**
     * 学期(S1：第一学期、S2：第二学期)
     */
    private String semester;

    /**
     * 领用单号
     */
    private String borrowNo;

    /**
     * 书籍ID
     */
    private String bookId;

    /**
     * 领用人姓名
     */
    private String borrowerName;

    /**
     * 领用人类型
     */
    private String borrowerType;

    /**
     * 领用人(教师/学生)ID
     */
    private String borrowerId;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 领用数量
     */
    private Integer borrowQuantity;

    /**
     * 领用日期
     */
    private Date borrowDate;

    /**
     * 是否需要归还(0：否、1：是)
     */
    private String isNeedReturn;

    /**
     * 预计归还日期
     */
    private Date expectedReturnDate;

    /**
     * 实际归还日期
     */
    private Date actualReturnDate;

    /**
     * 领用用途(1：教学用、2：学习用、3：研究用、4：其他)
     */
    private String borrowPurpose;

    /**
     * 领用原因说明
     */
    private String borrowReason;

    /**
     * 领用状态(0：遗失、1：已借出、2：已归还、3：逾期未还)
     */
    private String borrowStatus;

    /**
     * 归还状况(0：遗失、1：完好、2：轻微损坏、3：严重损坏)
     */
    private String returnCondition;

    /**
     * 审批人姓名
     */
    private String approvedName;

    /**
     * 审批日期
     */
    private Date approvalDate;

    /**
     * 备注描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getBorrowNo() {
        return borrowNo;
    }

    public void setBorrowNo(String borrowNo) {
        this.borrowNo = borrowNo;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBorrowerName() {
        return borrowerName;
    }

    public void setBorrowerName(String borrowerName) {
        this.borrowerName = borrowerName;
    }

    public String getBorrowerType() {
        return borrowerType;
    }

    public void setBorrowerType(String borrowerType) {
        this.borrowerType = borrowerType;
    }

    public String getBorrowerId() {
        return borrowerId;
    }

    public void setBorrowerId(String borrowerId) {
        this.borrowerId = borrowerId;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public Integer getBorrowQuantity() {
        return borrowQuantity;
    }

    public void setBorrowQuantity(Integer borrowQuantity) {
        this.borrowQuantity = borrowQuantity;
    }

    public Date getBorrowDate() {
        return borrowDate;
    }

    public void setBorrowDate(Date borrowDate) {
        this.borrowDate = borrowDate;
    }

    public String getIsNeedReturn() {
        return isNeedReturn;
    }

    public void setIsNeedReturn(String isNeedReturn) {
        this.isNeedReturn = isNeedReturn;
    }

    public Date getExpectedReturnDate() {
        return expectedReturnDate;
    }

    public void setExpectedReturnDate(Date expectedReturnDate) {
        this.expectedReturnDate = expectedReturnDate;
    }

    public Date getActualReturnDate() {
        return actualReturnDate;
    }

    public void setActualReturnDate(Date actualReturnDate) {
        this.actualReturnDate = actualReturnDate;
    }

    public String getBorrowPurpose() {
        return borrowPurpose;
    }

    public void setBorrowPurpose(String borrowPurpose) {
        this.borrowPurpose = borrowPurpose;
    }

    public String getBorrowReason() {
        return borrowReason;
    }

    public void setBorrowReason(String borrowReason) {
        this.borrowReason = borrowReason;
    }

    public String getBorrowStatus() {
        return borrowStatus;
    }

    public void setBorrowStatus(String borrowStatus) {
        this.borrowStatus = borrowStatus;
    }

    public String getReturnCondition() {
        return returnCondition;
    }

    public void setReturnCondition(String returnCondition) {
        this.returnCondition = returnCondition;
    }

    public String getApprovedName() {
        return approvedName;
    }

    public void setApprovedName(String approvedName) {
        this.approvedName = approvedName;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
