package com.edu.www.vo;

import java.util.Date;

/**
 * 教室信息值对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class ClassroomVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 部门编号(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 楼名
     */
    private String buildingName;

    /**
     * 楼号
     */
    private String buildingCode;

    /**
     * 楼层(1：1楼、2：2楼、3：3楼、4：4楼、5：5楼、6：6楼、7：7楼、8：8楼、9：9楼、10：10楼)
     */
    private String floor;

    /**
     * 教室名称
     */
    private String name;

    /**
     * 教室编号
     */
    private String code;

    /**
     * 是否有门禁(0：否、1：是)
     */
    private String isControlled;

    /**
     * 标签
     */
    private String tag;

    /**
     * 备注
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsControlled() {
        return isControlled;
    }

    public void setIsControlled(String isControlled) {
        this.isControlled = isControlled;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
} 