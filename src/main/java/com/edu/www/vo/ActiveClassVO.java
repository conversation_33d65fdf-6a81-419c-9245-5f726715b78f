package com.edu.www.vo;

import java.sql.Time;

/**
 * 实时上课教师VO
 *
 * <AUTHOR>
 */
public class ActiveClassVO {
    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 学科名称
     */
    private String subjectName;

    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeName;

    /**
     * 班级编码(A：A班、B：B班)
     */
    private String className;

    /**
     * 教室编号
     */
    private String classRoomCode;

    /**
     * 开始时间
     */
    private Time startTime;

    /**
     * 结束时间
     */
    private Time endTime;

    /**
     * 剩余分钟数
     */
    private int remainingMinutes;
    
    /**
     * 准备上课倒计时（秒），仅对即将上课的课程有效
     */
    private Integer countdown;

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getClassRoomCode() {
        return classRoomCode;
    }

    public void setClassRoomCode(String classRoomCode) {
        this.classRoomCode = classRoomCode;
    }

    public Time getStartTime() {
        return startTime;
    }

    public void setStartTime(Time startTime) {
        this.startTime = startTime;
    }

    public Time getEndTime() {
        return endTime;
    }

    public void setEndTime(Time endTime) {
        this.endTime = endTime;
    }

    public int getRemainingMinutes() {
        return remainingMinutes;
    }

    public void setRemainingMinutes(int remainingMinutes) {
        this.remainingMinutes = remainingMinutes;
    }
    
    public Integer getCountdown() {
        return countdown;
    }
    
    public void setCountdown(Integer countdown) {
        this.countdown = countdown;
    }
} 