package com.edu.www.vo;

import java.util.Date;

/**
 * 值日信息值对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class DutyVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 部门编号(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 年度
     */
    private String year;

    /**
     * 学期(S1：第一学期、S2：第二学期)
     */
    private String semester;

    /**
     * 周数
     */
    private String seqWeek;

    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private String weekday;

    /**
     * 值日日期
     */
    private Date dutyDate;

    /**
     * 值日类型(0：值日、1：Year10A+B、2：Year11A+B、3：Year12+春季)
     */
    private String dutyType;

    /**
     * 值日教师姓名
     */
    private String teacherName;

    /**
     * 值日教师ID
     */
    private String teacherId;

    /**
     * 置换颜色
     */
    private String color;

    /**
     * 是否是基组(0：否、1：是，默认否)
     */
    private String isBaseGroup;

    /**
     * 基组编码(A：A组、B：B组)
     */
    private String baseGroupCode;

    /**
     * 说明
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getSeqWeek() {
        return seqWeek;
    }

    public void setSeqWeek(String seqWeek) {
        this.seqWeek = seqWeek;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public Date getDutyDate() {
        return dutyDate;
    }

    public void setDutyDate(Date dutyDate) {
        this.dutyDate = dutyDate;
    }

    public String getDutyType() {
        return dutyType;
    }

    public void setDutyType(String dutyType) {
        this.dutyType = dutyType;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getIsBaseGroup() {
        return isBaseGroup;
    }

    public void setIsBaseGroup(String isBaseGroup) {
        this.isBaseGroup = isBaseGroup;
    }

    public String getBaseGroupCode() {
        return baseGroupCode;
    }

    public void setBaseGroupCode(String baseGroupCode) {
        this.baseGroupCode = baseGroupCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
