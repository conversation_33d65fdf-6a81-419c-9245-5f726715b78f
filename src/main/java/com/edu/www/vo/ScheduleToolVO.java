package com.edu.www.vo;

import java.sql.Time;
import java.util.List;

/**
 * 调课工具 VO
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class ScheduleToolVO {

    /**
     * 学年
     */
    private String year;

    /**
     * 学期(1：第一学期、2：第二学期)
     */
    private String semester;

    /**
     * 部门编码(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private List<String> gradeCodes;

    /**
     * 年级比较关系符号
     */
    private String gradeCmpSym;

    /**
     * 教师ID
     */
    private String teacherId;

    /**
     * 教师ID列表，支持多个值
     */
    private List<String> teacherIds;

    /**
     * 教师比较关系符号
     */
    private String teacherCmpSym;

    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private List<String> weekdays;

    /**
     * 星期比较关系符号
     */
    private String weekdayCmpSym;

    /**
     * 课段(1：第一节课、2：第二节课、3：第三节课、4：第四节课、5：第五节课、6：第六节课、7：第七节课、8：第八节课、9：第九节课、10：第十节课)
     */
    private List<String> periods;

    /**
     * 课段比较关系符号
     */
    private String periodCmpSym;

    /**
     * 开始时间
     */
    private Time startTime;

    /**
     * 结束时间
     */
    private Time endTime;

    /**
     * 是否有课(0：否、1：是)
     */
    private String hasClass;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public List<String> getGradeCodes() {
        return gradeCodes;
    }

    public void setGradeCodes(List<String> gradeCodes) {
        this.gradeCodes = gradeCodes;
    }

    public String getGradeCmpSym() {
        return gradeCmpSym;
    }

    public void setGradeCmpSym(String gradeCmpSym) {
        this.gradeCmpSym = gradeCmpSym;
    }

    public String getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }

    public List<String> getTeacherIds() {
        return teacherIds;
    }

    public void setTeacherIds(List<String> teacherIds) {
        this.teacherIds = teacherIds;
    }

    public String getTeacherCmpSym() {
        return teacherCmpSym;
    }

    public void setTeacherCmpSym(String teacherCmpSym) {
        this.teacherCmpSym = teacherCmpSym;
    }

    public List<String> getWeekdays() {
        return weekdays;
    }

    public void setWeekdays(List<String> weekdays) {
        this.weekdays = weekdays;
    }

    public String getWeekdayCmpSym() {
        return weekdayCmpSym;
    }

    public void setWeekdayCmpSym(String weekdayCmpSym) {
        this.weekdayCmpSym = weekdayCmpSym;
    }

    public List<String> getPeriods() {
        return periods;
    }

    public void setPeriods(List<String> periods) {
        this.periods = periods;
    }

    public String getPeriodCmpSym() {
        return periodCmpSym;
    }

    public void setPeriodCmpSym(String periodCmpSym) {
        this.periodCmpSym = periodCmpSym;
    }

    public Time getStartTime() {
        return startTime;
    }

    public void setStartTime(Time startTime) {
        this.startTime = startTime;
    }

    public Time getEndTime() {
        return endTime;
    }

    public void setEndTime(Time endTime) {
        this.endTime = endTime;
    }

    public String getHasClass() {
        return hasClass;
    }

    public void setHasClass(String hasClass) {
        this.hasClass = hasClass;
    }
}
