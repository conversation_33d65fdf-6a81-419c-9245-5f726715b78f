package com.edu.www.vo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分摊表VO
 */
public class SplitExpenseVO {

    /**
     * 表格名称
     */
    private String sheetName;

    /**
     * 标题
     */
    private String title;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 学生ID
     */
    private List<String> studentIds;

    /**
     * 合计(元)
     */
    private BigDecimal sum;

    /**
     * 制表人
     */
    private String creator;

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<String> getStudentIds() {
        return studentIds;
    }

    public void setStudentIds(List<String> studentIds) {
        this.studentIds = studentIds;
    }

    public BigDecimal getSum() {
        return sum;
    }

    public void setSum(BigDecimal sum) {
        this.sum = sum;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
}
