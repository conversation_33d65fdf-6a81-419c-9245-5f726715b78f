package com.edu.www.vo;

/**
 * 考试时长 PO
 */
public class ExamDurationVO {
    /**
     * 年度
     */
    private String year;

    /**
     * 学期(S1：第一学期、S2：第二学期)
     */
    private String semester;

    /**
     * 部门编号(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 监考人ID
     */
    private String invigilatorId;

    /**
     * 中文名
     */
    private String invigilatorNameZh;

    /**
     * 英文名
     */
    private String invigilatorNameEn;

    /**
     * 月考监考总时长
     */
    private Integer totalMonthlyDuration;

    /**
     * 期中监考总时长
     */
    private Integer totalMidDuration;

    /**
     * 模考监考总时长
     */
    private Integer totalMockDuration;

    /**
     * IB监考总时长
     */
    private Integer totalIBDuration;

    /**
     * 期末监考总时长
     */
    private Integer totalFinalDuration;

    /**
     * 监考总时长
     */
    private Integer totalDuration;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getInvigilatorId() {
        return invigilatorId;
    }

    public void setInvigilatorId(String invigilatorId) {
        this.invigilatorId = invigilatorId;
    }

    public String getInvigilatorNameZh() {
        return invigilatorNameZh;
    }

    public void setInvigilatorNameZh(String invigilatorNameZh) {
        this.invigilatorNameZh = invigilatorNameZh;
    }

    public String getInvigilatorNameEn() {
        return invigilatorNameEn;
    }

    public void setInvigilatorNameEn(String invigilatorNameEn) {
        this.invigilatorNameEn = invigilatorNameEn;
    }

    public Integer getTotalMonthlyDuration() {
        return totalMonthlyDuration;
    }

    public void setTotalMonthlyDuration(Integer totalMonthlyDuration) {
        this.totalMonthlyDuration = totalMonthlyDuration;
    }

    public Integer getTotalMidDuration() {
        return totalMidDuration;
    }

    public void setTotalMidDuration(Integer totalMidDuration) {
        this.totalMidDuration = totalMidDuration;
    }

    public Integer getTotalMockDuration() {
        return totalMockDuration;
    }

    public void setTotalMockDuration(Integer totalMockDuration) {
        this.totalMockDuration = totalMockDuration;
    }

    public Integer getTotalIBDuration() {
        return totalIBDuration;
    }

    public void setTotalIBDuration(Integer totalIBDuration) {
        this.totalIBDuration = totalIBDuration;
    }

    public Integer getTotalFinalDuration() {
        return totalFinalDuration;
    }

    public void setTotalFinalDuration(Integer totalFinalDuration) {
        this.totalFinalDuration = totalFinalDuration;
    }

    public Integer getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Integer totalDuration) {
        this.totalDuration = totalDuration;
    }
}
