package com.edu.www.vo;

import java.util.Date;

/**
 * 入库记录值对象
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class InboundRecordVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 采购ID
     */
    private String purchaseId;

    /**
     * 书籍ID
     */
    private String bookId;

    /**
     * 入库数量
     */
    private Integer inboundQuantity;

    /**
     * 入库日期
     */
    private Date inboundDate;

    /**
     * 质量检查结果(0：不合格、1：合格、2：待检查)
     */
    private String qualityCheckResult;

    /**
     * 质量检查人姓名
     */
    private String qualityCheckName;

    /**
     * 质量检查日期
     */
    private Date qualityCheckDate;

    /**
     * 存放位置
     */
    private String storageLocation;

    /**
     * 入库人姓名
     */
    private String inboundName;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 入库类型(1：采购入库、2：调拨入库、3：退回入库)
     */
    private String inboundType;

    /**
     * 备注描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPurchaseId() {
        return purchaseId;
    }

    public void setPurchaseId(String purchaseId) {
        this.purchaseId = purchaseId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Integer getInboundQuantity() {
        return inboundQuantity;
    }

    public void setInboundQuantity(Integer inboundQuantity) {
        this.inboundQuantity = inboundQuantity;
    }

    public Date getInboundDate() {
        return inboundDate;
    }

    public void setInboundDate(Date inboundDate) {
        this.inboundDate = inboundDate;
    }

    public String getQualityCheckResult() {
        return qualityCheckResult;
    }

    public void setQualityCheckResult(String qualityCheckResult) {
        this.qualityCheckResult = qualityCheckResult;
    }

    public String getQualityCheckName() {
        return qualityCheckName;
    }

    public void setQualityCheckName(String qualityCheckName) {
        this.qualityCheckName = qualityCheckName;
    }

    public Date getQualityCheckDate() {
        return qualityCheckDate;
    }

    public void setQualityCheckDate(Date qualityCheckDate) {
        this.qualityCheckDate = qualityCheckDate;
    }

    public String getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(String storageLocation) {
        this.storageLocation = storageLocation;
    }

    public String getInboundName() {
        return inboundName;
    }

    public void setInboundName(String inboundName) {
        this.inboundName = inboundName;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getInboundType() {
        return inboundType;
    }

    public void setInboundType(String inboundType) {
        this.inboundType = inboundType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
