package com.edu.www.vo;

import java.util.List;

/**
 * 座位安排VO
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class SeatVO {
    /**
     * 部门(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 选中的科目信息列表
     */
    private List<SubjectSelectionVO> subjectSelection;

    /**
     * 科目选择信息
     */
    public static class SubjectSelectionVO {
        /**
         * 所选科目编号（Physics、Biology、Math）
         */
        private String subjectCode;

        /**
         * 该科目下选中的分类和层级组合列表
         */
        private List<SubjectDetailVO> subjectDetail;

        public String getSubjectCode() {
            return subjectCode;
        }

        public void setSubjectCode(String subjectCode) {
            this.subjectCode = subjectCode;
        }

        public List<SubjectDetailVO> getSubjectDetail() {
            return subjectDetail;
        }

        public void setSubjectDetail(List<SubjectDetailVO> subjectDetail) {
            this.subjectDetail = subjectDetail;
        }
    }

    /**
     * 科目详细信息（分类和层级的组合）
     */
    public static class SubjectDetailVO {
        /**
         * 学科分类(AA：A&A、AI：A&I、LL：L&L、LP：L&P)
         * 对于Physics和Biology，此字段可为空
         * 对于Math，必须指定AA或AI
         */
        private String subjectType;

        /**
         * 科目层级(SL：SL、HL：HL)
         */
        private String subjectLevel;

        public String getSubjectType() {
            return subjectType;
        }

        public void setSubjectType(String subjectType) {
            this.subjectType = subjectType;
        }

        public String getSubjectLevel() {
            return subjectLevel;
        }

        public void setSubjectLevel(String subjectLevel) {
            this.subjectLevel = subjectLevel;
        }
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public List<SubjectSelectionVO> getSubjectSelection() {
        return subjectSelection;
    }

    public void setSubjectSelection(List<SubjectSelectionVO> subjectSelection) {
        this.subjectSelection = subjectSelection;
    }
}
