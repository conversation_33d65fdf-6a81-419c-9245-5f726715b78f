package com.edu.www.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购合同值对象
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class PurchaseContractVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 采购ID
     */
    private String purchaseId;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 合同标题
     */
    private String contractTitle;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 预付款金额
     */
    private BigDecimal prepaymentAmount;

    /**
     * 预付款比例
     */
    private BigDecimal prepaymentRatio;

    /**
     * 合同状态(0：已终止、1：草稿、2：待审批、3：审批中、4：已审批、5：已签订、6：已执行、7：已完成)
     */
    private String contractStatus;

    /**
     * OA流程ID
     */
    private String oaProcessId;

    /**
     * 合同文件路径
     */
    private String contractFilePath;

    /**
     * 签订日期
     */
    private Date signDate;

    /**
     * 生效日期
     */
    private Date effectiveDate;

    /**
     * 到期日期
     */
    private Date expiryDate;

    /**
     * 付款条件
     */
    private String paymentTerms;

    /**
     * 交货条件
     */
    private String deliveryTerms;

    /**
     * 质量标准
     */
    private String qualityStandards;

    /**
     * 备注描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getPurchaseId() {
        return purchaseId;
    }

    public void setPurchaseId(String purchaseId) {
        this.purchaseId = purchaseId;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getContractTitle() {
        return contractTitle;
    }

    public void setContractTitle(String contractTitle) {
        this.contractTitle = contractTitle;
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getPrepaymentAmount() {
        return prepaymentAmount;
    }

    public void setPrepaymentAmount(BigDecimal prepaymentAmount) {
        this.prepaymentAmount = prepaymentAmount;
    }

    public BigDecimal getPrepaymentRatio() {
        return prepaymentRatio;
    }

    public void setPrepaymentRatio(BigDecimal prepaymentRatio) {
        this.prepaymentRatio = prepaymentRatio;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public String getOaProcessId() {
        return oaProcessId;
    }

    public void setOaProcessId(String oaProcessId) {
        this.oaProcessId = oaProcessId;
    }

    public String getContractFilePath() {
        return contractFilePath;
    }

    public void setContractFilePath(String contractFilePath) {
        this.contractFilePath = contractFilePath;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getPaymentTerms() {
        return paymentTerms;
    }

    public void setPaymentTerms(String paymentTerms) {
        this.paymentTerms = paymentTerms;
    }

    public String getDeliveryTerms() {
        return deliveryTerms;
    }

    public void setDeliveryTerms(String deliveryTerms) {
        this.deliveryTerms = deliveryTerms;
    }

    public String getQualityStandards() {
        return qualityStandards;
    }

    public void setQualityStandards(String qualityStandards) {
        this.qualityStandards = qualityStandards;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
