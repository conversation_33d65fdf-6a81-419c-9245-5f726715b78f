package com.edu.www.vo;


import java.util.List;
import java.util.Map;

/**
 * 考试标签贴信息VO
 */
public class ExamTagVO {
    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeCode;

    /**
     * 教室编号
     */
    private String classroomCode;

    /**
     * 学科编码·即子学科编码
     */
    private String subjectCode;

    /**
     * 学科组成部分
     */
    private List<String> subjectParts;

    /**
     * 时间组成部分
     */
    private Map<String, String> timeParts;

    /**
     * 考试日期
     */
    private String examDate;

    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private String weekday;

    /**
     * 描述
     */
    private String note;

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getClassroomCode() {
        return classroomCode;
    }

    public void setClassroomCode(String classroomCode) {
        this.classroomCode = classroomCode;
    }

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public List<String> getSubjectParts() {
        return subjectParts;
    }

    public void setSubjectParts(List<String> subjectParts) {
        this.subjectParts = subjectParts;
    }

    public Map<String, String> getTimeParts() {
        return timeParts;
    }

    public void setTimeParts(Map<String, String> timeParts) {
        this.timeParts = timeParts;
    }

    public String getExamDate() {
        return examDate;
    }

    public void setExamDate(String examDate) {
        this.examDate = examDate;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
}
