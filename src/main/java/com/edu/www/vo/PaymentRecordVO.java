package com.edu.www.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付记录值对象
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class PaymentRecordVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 支付类型(1：预付款、2：尾款、3:其他)
     */
    private String paymentType;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付日期
     */
    private Date paymentDate;

    /**
     * 支付方式(1：银行转账、2：支票、3:现金)
     */
    private String paymentMethod;

    /**
     * OA流程ID
     */
    private String oaProcessId;

    /**
     * 支付状态(0：支付失败、1：待审批、2：已审批、3:已支付)
     */
    private String paymentStatus;

    /**
     * 支付凭证
     */
    private String paymentVoucher;

    /**
     * 备注描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getOaProcessId() {
        return oaProcessId;
    }

    public void setOaProcessId(String oaProcessId) {
        this.oaProcessId = oaProcessId;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getPaymentVoucher() {
        return paymentVoucher;
    }

    public void setPaymentVoucher(String paymentVoucher) {
        this.paymentVoucher = paymentVoucher;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
