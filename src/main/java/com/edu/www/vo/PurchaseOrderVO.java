package com.edu.www.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购订单VO
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class PurchaseOrderVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 部门(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 年度
     */
    private String year;

    /**
     * 学期(S1：第一学期、S2：第二学期)
     */
    private String semester;

    /**
     * 采购标题
     */
    private String purchaseTitle;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 采购日期
     */
    private Date purchaseDate;

    /**
     * 申请人姓名
     */
    private String requesterName;

    /**
     * 书籍类别(1:中文教材、2:外文教材)
     */
    private String bookCategory;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 货币类型(CNY：人民币、JPY：日元、USD：美元、EUR：欧元)
     */
    private String currency;

    /**
     * 付款方式(1:先付款后发货、2:先发货后付款)
     */
    private String paymentMethod;

    /**
     * 采购状态(0：已取消、1：待报价、2：已报价、3：待审批、4：已审批、5：已下单、6：已发货、7：已收货、8：已完成)
     */
    private String purchaseStatus;

    /**
     * 是否需要合同(0：否、1：是)
     */
    private String isNeedContract;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 预计交货日期
     */
    private Date expectedDeliveryDate;

    /**
     * 实际交货日期
     */
    private Date actualDeliveryDate;

    /**
     * 备注描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getPurchaseTitle() {
        return purchaseTitle;
    }

    public void setPurchaseTitle(String purchaseTitle) {
        this.purchaseTitle = purchaseTitle;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public Date getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Date purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public String getRequesterName() {
        return requesterName;
    }

    public void setRequesterName(String requesterName) {
        this.requesterName = requesterName;
    }

    public String getBookCategory() {
        return bookCategory;
    }

    public void setBookCategory(String bookCategory) {
        this.bookCategory = bookCategory;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPurchaseStatus() {
        return purchaseStatus;
    }

    public void setPurchaseStatus(String purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    public String getIsNeedContract() {
        return isNeedContract;
    }

    public void setIsNeedContract(String isNeedContract) {
        this.isNeedContract = isNeedContract;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public Date getExpectedDeliveryDate() {
        return expectedDeliveryDate;
    }

    public void setExpectedDeliveryDate(Date expectedDeliveryDate) {
        this.expectedDeliveryDate = expectedDeliveryDate;
    }

    public Date getActualDeliveryDate() {
        return actualDeliveryDate;
    }

    public void setActualDeliveryDate(Date actualDeliveryDate) {
        this.actualDeliveryDate = actualDeliveryDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
