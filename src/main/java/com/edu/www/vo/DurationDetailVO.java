package com.edu.www.vo;

import com.edu.www.po.CompositionInfoPO;
import com.edu.www.po.SubjectInfoPO;

import java.util.List;

/**
 * 监考时长详情VO
 */
public class DurationDetailVO {

    // ========== 基本信息 ==========
    /**
     * 年度
     */
    private String year;

    /**
     * 学期
     */
    private String semester;

    /**
     * 考试日期
     */
    private String examDate;

    /**
     * 星期
     */
    private String weekday;

    /**
     * 考试类型
     */
    private String examType;

    // ========== 考场信息 ==========
    /**
     * 部门
     */
    private String departmentCode;

    /**
     * 年级 班级
     */
    private String gradeClass;

    /**
     * 教室
     */
    private String classroom;

    /**
     * 考生人数
     */
    private Integer examCandidate;

    /**
     * 总时长
     */
    private String totalDuration;

    /**
     * 状态
     */
    private String status;

    // ========== 科目安排 ==========
    /**
     * 科目安排列表
     */
    private List<SubjectInfoPO> subjectInfoPOList;

    // Getter and Setter methods
    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getExamDate() {
        return examDate;
    }

    public void setExamDate(String examDate) {
        this.examDate = examDate;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public String getExamType() {
        return examType;
    }

    public void setExamType(String examType) {
        this.examType = examType;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getGradeClass() {
        return gradeClass;
    }

    public void setGradeClass(String gradeClass) {
        this.gradeClass = gradeClass;
    }

    public String getClassroom() {
        return classroom;
    }

    public void setClassroom(String classroom) {
        this.classroom = classroom;
    }

    public Integer getExamCandidate() {
        return examCandidate;
    }

    public void setExamCandidate(Integer examCandidate) {
        this.examCandidate = examCandidate;
    }

    public String getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(String totalDuration) {
        this.totalDuration = totalDuration;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<SubjectInfoPO> getSubjectInfoPOList() {
        return subjectInfoPOList;
    }

    public void setSubjectInfoPOList(List<SubjectInfoPO> subjectInfoPOList) {
        this.subjectInfoPOList = subjectInfoPOList;
    }
}
