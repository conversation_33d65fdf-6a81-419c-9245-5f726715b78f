package com.edu.www.vo;

import java.util.Date;

/**
 * 招生信息值对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class AdmissionVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 招生名称
     */
    private String name;

    /**
     * 联系电话
     */
    private String phoneNum;

    /**
     * 招生年级编码
     */
    private String admissionGradeCode;

    /**
     * 学制编码
     */
    private String yearSystemCode;

    /**
     * 是否入读(0：否、1：是)
     */
    private String isEnrolled;

    /**
     * 邀请说明
     */
    private String inviteDescription;

    /**
     * 考试信息
     */
    private String examInfo;

    /**
     * 说明
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getAdmissionGradeCode() {
        return admissionGradeCode;
    }

    public void setAdmissionGradeCode(String admissionGradeCode) {
        this.admissionGradeCode = admissionGradeCode;
    }

    public String getYearSystemCode() {
        return yearSystemCode;
    }

    public void setYearSystemCode(String yearSystemCode) {
        this.yearSystemCode = yearSystemCode;
    }

    public String getIsEnrolled() {
        return isEnrolled;
    }

    public void setIsEnrolled(String isEnrolled) {
        this.isEnrolled = isEnrolled;
    }

    public String getInviteDescription() {
        return inviteDescription;
    }

    public void setInviteDescription(String inviteDescription) {
        this.inviteDescription = inviteDescription;
    }

    public String getExamInfo() {
        return examInfo;
    }

    public void setExamInfo(String examInfo) {
        this.examInfo = examInfo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}