package com.edu.www.vo;

import java.util.Date;
import java.util.List;

public class SubjectVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 学科组名称
     */
    private String groupName;
    
    /**
     * 学科组编号
     */
    private String groupCode;
    
    /**
     * 学科名称
     */
    private String name;
    
    /**
     * 学科编号
     */
    private String code;
    
    /**
     * 学科层级
     */
    private String level;
    
    /**
     * 子学科名称
     */
    private String subSubjectName;
    
    /**
     * 学科教师ID
     */
    private String subjectTeacherId;
    
    /**
     * 教授班级ID（多个班级）
     */
    private List<String> teachingClassIds;
    
    /**
     * 教授班级ID（存储用，以逗号分隔）
     */
    private String teachingClassId;
    
    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 部门编码(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;
    
    /**
     * 部门名称翻译
     */
    private String departmentName;
    
    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 修改时间
     */
    private Date updatedAt;
    
    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getSubSubjectName() {
        return subSubjectName;
    }

    public void setSubSubjectName(String subSubjectName) {
        this.subSubjectName = subSubjectName;
    }

    public String getSubjectTeacherId() {
        return subjectTeacherId;
    }

    public void setSubjectTeacherId(String subjectTeacherId) {
        this.subjectTeacherId = subjectTeacherId;
    }

    public List<String> getTeachingClassIds() {
        return teachingClassIds;
    }

    public void setTeachingClassIds(List<String> teachingClassIds) {
        this.teachingClassIds = teachingClassIds;
    }

    public String getTeachingClassId() {
        return teachingClassId;
    }

    public void setTeachingClassId(String teachingClassId) {
        this.teachingClassId = teachingClassId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}