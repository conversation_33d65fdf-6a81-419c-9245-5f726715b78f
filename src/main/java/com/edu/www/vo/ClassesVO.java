package com.edu.www.vo;

import java.util.Date;

public class ClassesVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 部门编码(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;
    
    /**
     * 年级名称
     */
    private String gradeName;
    
    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeCode;
    
    /**
     * 班级名称
     */
    private String name;
    
    /**
     * 班级编号(A：A班、B：B班)
     */
    private String code;
    
    /**
     * 正班主任ID
     */
    private String headTeacherId;
    
    /**
     * 副班主任ID
     */
    private String asstHeadTeacherId;
    
    /**
     * 教室编号
     */
    private String classroomCode;
    
    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 修改时间
     */
    private Date updatedAt;
    
    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getHeadTeacherId() {
        return headTeacherId;
    }

    public void setHeadTeacherId(String headTeacherId) {
        this.headTeacherId = headTeacherId;
    }

    public String getAsstHeadTeacherId() {
        return asstHeadTeacherId;
    }

    public void setAsstHeadTeacherId(String asstHeadTeacherId) {
        this.asstHeadTeacherId = asstHeadTeacherId;
    }
    
    public String getClassroomCode() {
        return classroomCode;
    }

    public void setClassroomCode(String classroomCode) {
        this.classroomCode = classroomCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
} 