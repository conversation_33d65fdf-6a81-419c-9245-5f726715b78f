package com.edu.www.vo;

import java.util.Date;

public class TeacherVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 中文名
     */
    private String nameZh;
    
    /**
     * 英文名
     */
    private String nameEn;

    /**
     * 性别(0：女、1：男)
     */
    private String gender;
    
    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 部门编码(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;
    
    /**
     * 工号
     */
    private String empNo;
    
    /**
     * 状态(0：离职、1：在职、2：出差、3：请假、4：休假)
     */
    private String status;
    
    /**
     * 类型(0：外教、1：中教)
     */
    private String type;
    
    /**
     * 是否为学科组组长(0：否、1：是)
     */
    private String isSubjectGroupLeader;
    
    /**
     * 学科组编号
     */
    private String subjectGroupCode;
    
    /**
     * 办公室编码
     */
    private String officeCode;
    
    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 修改时间
     */
    private Date updatedAt;
    
    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    public String getIsSubjectGroupLeader() {
        return isSubjectGroupLeader;
    }

    public void setIsSubjectGroupLeader(String isSubjectGroupLeader) {
        this.isSubjectGroupLeader = isSubjectGroupLeader;
    }

    public String getSubjectGroupCode() {
        return subjectGroupCode;
    }

    public void setSubjectGroupCode(String subjectGroupCode) {
        this.subjectGroupCode = subjectGroupCode;
    }
    
    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
} 