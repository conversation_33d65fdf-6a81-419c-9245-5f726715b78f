package com.edu.www.vo;

import java.math.BigDecimal;
import java.util.List;

public class TableContentVO {
    /**
     * 表格名称
     */
    private String sheetName;

    /**
     * 标题
     */
    private String title;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 左侧的名称
     */
    private String partName;

    /**
     * 合计(元)
     */
    private BigDecimal sum;

    /**
     * 制表人
     */
    private String creator;

    /**
     * 项目集合
     */
    private List<ItemVO> items;

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public BigDecimal getSum() {
        return sum;
    }

    public void setSum(BigDecimal sum) {
        this.sum = sum;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public List<ItemVO> getItems() {
        return items;
    }

    public void setItems(List<ItemVO> items) {
        this.items = items;
    }
}
