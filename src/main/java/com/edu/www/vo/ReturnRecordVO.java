package com.edu.www.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 归还记录值对象
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class ReturnRecordVO extends PageVO {
    /**
     * id
     */
    private String id;

    /**
     * 领用ID
     */
    private String borrowId;

    /**
     * 归还数量
     */
    private Integer returnQuantity;

    /**
     * 归还日期
     */
    private Date returnDate;

    /**
     * 归还状况(1：完好、2：轻微损坏、3:严重损坏)
     */
    private String returnCondition;

    /**
     * 质检人姓名
     */
    private String qualityCheckName;

    /**
     * 损坏描述
     */
    private String damageDescription;

    /**
     * 赔偿金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 归还人姓名
     */
    private String returnName;

    /**
     * 接收人姓名
     */
    private String receivedName;

    /**
     * 备注描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBorrowId() {
        return borrowId;
    }

    public void setBorrowId(String borrowId) {
        this.borrowId = borrowId;
    }

    public Integer getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(Integer returnQuantity) {
        this.returnQuantity = returnQuantity;
    }

    public Date getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
    }

    public String getReturnCondition() {
        return returnCondition;
    }

    public void setReturnCondition(String returnCondition) {
        this.returnCondition = returnCondition;
    }

    public String getQualityCheckName() {
        return qualityCheckName;
    }

    public void setQualityCheckName(String qualityCheckName) {
        this.qualityCheckName = qualityCheckName;
    }

    public String getDamageDescription() {
        return damageDescription;
    }

    public void setDamageDescription(String damageDescription) {
        this.damageDescription = damageDescription;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public String getReturnName() {
        return returnName;
    }

    public void setReturnName(String returnName) {
        this.returnName = returnName;
    }

    public String getReceivedName() {
        return receivedName;
    }

    public void setReceivedName(String receivedName) {
        this.receivedName = receivedName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
