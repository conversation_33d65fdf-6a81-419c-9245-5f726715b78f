package com.edu.www.vo;

import java.util.List;

public class ItemVO {
    /**
     * 序号
     */
    private int seq;

    /**
     * 中文名
     */
    private String nameZh;

    /**
     * 英文名
     */
    private String nameEn;

    /**
     * 学生编号
     */
    private String studentCode;

    /**
     * 证件号
     */
    private String cardNum;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目详情列表
     */
    private List<ItemDetailVO> itemDetails;

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getStudentCode() {
        return studentCode;
    }

    public void setStudentCode(String studentCode) {
        this.studentCode = studentCode;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public List<ItemDetailVO> getItemDetails() {
        return itemDetails;
    }

    public void setItemDetails(List<ItemDetailVO> itemDetails) {
        this.itemDetails = itemDetails;
    }
}
