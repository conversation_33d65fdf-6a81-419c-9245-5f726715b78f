package com.edu.www.vo;

/**
 * 教师课表VO
 */
public class TeacherScheduleVO {
    /**
     * 教师ID
     */
    private String teacherId;

    /**
     * 教师中文名
     */
    private String teacherNameZh;

    /**
     * 教师英文名
     */
    private String teacherNameEn;

    /**
     * 性别(0：女、1：男)
     */
    private String gender;

    /**
     * 学年
     */
    private String year;

    /**
     * 学期(1：第一学期、2：第二学期)
     */
    private String semester;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 学科名称·即子学科
     */
    private String subjectName;

    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeCode;

    /**
     * 班级编码(A：A班、B：B班)
     */
    private String classCode;

    /**
     * 学科层级(SL：SL、HL：HL)
     */
    private String subjectLevel;

    /**
     * 教室编号
     */
    private String classRoomCode;

    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private String weekday;

    /**
     * 时段(0：上午、1：中午、2：下午)
     */
    private String dayPart;

    /**
     * 课段(1：第一节课、2：第二节课、3：第三节课、4：第四节课、5：第五节课、6：第六节课、7：第七节课、8：第八节课、9：第九节课、10：第十节课)
     */
    private String period;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    public String getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }

    public String getTeacherNameZh() {
        return teacherNameZh;
    }

    public void setTeacherNameZh(String teacherNameZh) {
        this.teacherNameZh = teacherNameZh;
    }

    public String getTeacherNameEn() {
        return teacherNameEn;
    }

    public void setTeacherNameEn(String teacherNameEn) {
        this.teacherNameEn = teacherNameEn;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getSubjectLevel() {
        return subjectLevel;
    }

    public void setSubjectLevel(String subjectLevel) {
        this.subjectLevel = subjectLevel;
    }

    public String getClassRoomCode() {
        return classRoomCode;
    }

    public void setClassRoomCode(String classRoomCode) {
        this.classRoomCode = classRoomCode;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public String getDayPart() {
        return dayPart;
    }

    public void setDayPart(String dayPart) {
        this.dayPart = dayPart;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
