package com.edu.www.vo;

import java.sql.Time;

public class RescheduleVO {

    /**
     * 教师ID
     */
    private String teacherId;
    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 部门编码(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 学年
     */
    private String year;

    /**
     * 学期(1：第一学期、2：第二学期)
     */
    private String semester;

    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeCode;

    /**
     * 班级编码(A：A班、B：B班)
     */
    private String classCode;

    /**
     * 学科名称·即子学科
     */
    private String subjectName;

    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private String weekday;

    /**
     * 课段(1：第一节课、2：第二节课、3：第三节课、4：第四节课、5：第五节课、6：第六节课、7：第七节课、8：第八节课、9：第九节课、10：第十节课)
     */
    private String period;

    /**
     * 开始时间
     */
    private Time startTime;

    /**
     * 结束时间
     */
    private Time endTime;

    public String getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Time getStartTime() {
        return startTime;
    }

    public void setStartTime(Time startTime) {
        this.startTime = startTime;
    }

    public Time getEndTime() {
        return endTime;
    }

    public void setEndTime(Time endTime) {
        this.endTime = endTime;
    }
}
