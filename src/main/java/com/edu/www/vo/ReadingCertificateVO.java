package com.edu.www.vo;

/**
 * 在读证明
 */
public class ReadingCertificateVO {
    /**
     * 标题(中文)
     */
    private String titleZh;

    /**
     * 学校名称(中文)
     */
    private String schoolNameZh;

    /**
     * 姓名(中文)
     */
    private String nameZh;

    /**
     * 性别(中文)
     */
    private String genderZh;

    /**
     * 出生日期(中文)
     */
    private String birthDateZh;

    /**
     * 入学日期(中文)
     */
    private String admissionDateZh;

    /**
     * 当前年月(中文)
     */
    private String currentDateZh;

    /**
     * 提示语(中文)
     */
    private String cardTypeTipZh;

    /**
     * 年级(中文)
     */
    private String gradeZh;

    /**
     * 证件号(身份证号或护照号)
     */
    private String cardNum;

    /**
     * 标题(英文)
     */
    private String titleEn;

    /**
     * 学校名称(英文)
     */
    private String schoolNameEn;
    /**
     * 姓名(英文)
     */
    private String nameEn;
    /**
     * 性别(英文)
     */
    private String genderEn;
    /**
     * 出生日期(英文)
     */
    private String birthDateEn;
    /**
     * 入学日期(英文)
     */
    private String admissionDateEn;
    /**
     * 当前年月(英文)
     */
    private String currentDateEn;

    /**
     * 提示语(英文)
     */
    private String cardTypeTipEn;

    /**
     * 年级(英文)
     */
    private String gradeEn;

    /**
     * 代词(英文)
     */
    private String pronounEn;

    /**
     * 部门编号(SY：双语部、IC：融合部、DP：高中部)
     */
    private String deptCode;

    public String getTitleZh() {
        return titleZh;
    }

    public void setTitleZh(String titleZh) {
        this.titleZh = titleZh;
    }

    public String getSchoolNameZh() {
        return schoolNameZh;
    }

    public void setSchoolNameZh(String schoolNameZh) {
        this.schoolNameZh = schoolNameZh;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public String getGenderZh() {
        return genderZh;
    }

    public void setGenderZh(String genderZh) {
        this.genderZh = genderZh;
    }

    public String getBirthDateZh() {
        return birthDateZh;
    }

    public void setBirthDateZh(String birthDateZh) {
        this.birthDateZh = birthDateZh;
    }

    public String getAdmissionDateZh() {
        return admissionDateZh;
    }

    public void setAdmissionDateZh(String admissionDateZh) {
        this.admissionDateZh = admissionDateZh;
    }

    public String getCurrentDateZh() {
        return currentDateZh;
    }

    public void setCurrentDateZh(String currentDateZh) {
        this.currentDateZh = currentDateZh;
    }

    public String getCardTypeTipZh() {
        return cardTypeTipZh;
    }

    public void setCardTypeTipZh(String cardTypeTipZh) {
        this.cardTypeTipZh = cardTypeTipZh;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public String getSchoolNameEn() {
        return schoolNameEn;
    }

    public void setSchoolNameEn(String schoolNameEn) {
        this.schoolNameEn = schoolNameEn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getGenderEn() {
        return genderEn;
    }

    public void setGenderEn(String genderEn) {
        this.genderEn = genderEn;
    }

    public String getBirthDateEn() {
        return birthDateEn;
    }

    public void setBirthDateEn(String birthDateEn) {
        this.birthDateEn = birthDateEn;
    }

    public String getAdmissionDateEn() {
        return admissionDateEn;
    }

    public void setAdmissionDateEn(String admissionDateEn) {
        this.admissionDateEn = admissionDateEn;
    }

    public String getCurrentDateEn() {
        return currentDateEn;
    }

    public void setCurrentDateEn(String currentDateEn) {
        this.currentDateEn = currentDateEn;
    }

    public String getCardTypeTipEn() {
        return cardTypeTipEn;
    }

    public void setCardTypeTipEn(String cardTypeTipEn) {
        this.cardTypeTipEn = cardTypeTipEn;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getGradeZh() {
        return gradeZh;
    }

    public void setGradeZh(String gradeZh) {
        this.gradeZh = gradeZh;
    }

    public String getGradeEn() {
        return gradeEn;
    }

    public void setGradeEn(String gradeEn) {
        this.gradeEn = gradeEn;
    }

    public String getPronounEn() {
        return pronounEn;
    }

    public void setPronounEn(String pronounEn) {
        this.pronounEn = pronounEn;
    }
}
