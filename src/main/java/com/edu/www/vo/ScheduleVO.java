package com.edu.www.vo;

import java.sql.Time;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class ScheduleVO extends PageVO {
    /**
     * id
     */
    private String id;
    
    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 部门编码(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;
    
    /**
     * 学科名称·即子学科
     */
    private String subjectName;
    
    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeCode;
    
    /**
     * 班级编码(A：A班、B：B班)
     */
    private String classCode;

    /**
     * 班级编码(A：A班、B：B班)（多个班级）
     */
    private List<String> classCodes;
    
    /**
     * 学科层级(SL：SL、HL：HL)
     */
    private String subjectLevel;
    
    /**
     * 教师ID
     */
    private String teacherId;

    /**
     * 教师名
     */
    private String teacherName;

    /**
     * 教室编号
     */
    private String classRoomCode;
    
    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private String weekday;
    
    /**
     * 时段(0：上午、1：中午、2：下午)
     */
    private String dayPart;
    
    /**
     * 课段(1：第一节课、2：第二节课、3：第三节课、4：第四节课、5：第五节课、6：第六节课、7：第七节课、8：第八节课、9：第九节课、10：第十节课)
     */
    private String period;
    
    /**
     * 开始时间
     */
    private Time startTime;
    
    /**
     * 结束时间
     */
    private Time endTime;
    
    /**
     * 学年
     */
    private String year;
    
    /**
     * 学期(1：第一学期、2：第二学期)
     */
    private String semester;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 修改时间
     */
    private Date updatedAt;
    
    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public List<String> getClassCodes() {
        return classCodes;
    }

    public void setClassCodes(List<String> classCodes) {
        this.classCodes = classCodes;
    }

    public String getSubjectLevel() {
        return subjectLevel;
    }

    public void setSubjectLevel(String subjectLevel) {
        this.subjectLevel = subjectLevel;
    }

    public String getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getClassRoomCode() {
        return classRoomCode;
    }

    public void setClassRoomCode(String classRoomCode) {
        this.classRoomCode = classRoomCode;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public String getDayPart() {
        return dayPart;
    }

    public void setDayPart(String dayPart) {
        this.dayPart = dayPart;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Time getStartTime() {
        return startTime;
    }

    public void setStartTime(Time startTime) {
        this.startTime = startTime;
    }

    public Time getEndTime() {
        return endTime;
    }

    public void setEndTime(Time endTime) {
        this.endTime = endTime;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ScheduleVO that = (ScheduleVO) o;
        
        // 添加id比较，增强唯一性
        return Objects.equals(id, that.id) &&
               Objects.equals(teacherId, that.teacherId) &&
               Objects.equals(weekday, that.weekday) &&
               Objects.equals(period, that.period) &&
               Objects.equals(gradeCode, that.gradeCode) &&
               Objects.equals(classCode, that.classCode) &&
               Objects.equals(year, that.year) &&
               Objects.equals(semester, that.semester);
    }
    
    @Override
    public int hashCode() {
        // 确保hashCode方法使用与equals方法相同的字段，包括id
        return Objects.hash(id, teacherId, weekday, period, gradeCode, classCode, year, semester);
    }
} 