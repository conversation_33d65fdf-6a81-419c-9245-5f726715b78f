package com.edu.www.vo;

/**
 * 值日统计VO
 */
public class DutyStatisticVO {
    /**
     * 序号
     */
    private Integer seq;

    /**
     * 值日教师姓名
     */
    private String teacherName;

    /**
     * 周一至周四值日次数(普通值日次数)
     */
    private Integer regularDutyCount;
    /**
     * 周五值日次数
     */
    private Integer fridayDutyCount;

    /**
     * 值晚自修天数
     */
    private Integer lateDutyDay;

    /**
     * 周日值日次数
     */
    private Integer sundayDutyCount;


    /**
     * 周一至周四值日日期(普通值日日期)
     */
    private String regularDutyDateStr;
    /**
     * 周五值日日期
     */
    private String fridayDutyDateStr;

    /**
     * 值晚自修日期
     */
    private String lateDutyDateStr;

    /**
     * 周日值日日期
     */
    private String sundayDutyDateStr;

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public Integer getRegularDutyCount() {
        return regularDutyCount;
    }

    public void setRegularDutyCount(Integer regularDutyCount) {
        this.regularDutyCount = regularDutyCount;
    }

    public Integer getFridayDutyCount() {
        return fridayDutyCount;
    }

    public void setFridayDutyCount(Integer fridayDutyCount) {
        this.fridayDutyCount = fridayDutyCount;
    }

    public Integer getLateDutyDay() {
        return lateDutyDay;
    }

    public void setLateDutyDay(Integer lateDutyDay) {
        this.lateDutyDay = lateDutyDay;
    }

    public Integer getSundayDutyCount() {
        return sundayDutyCount;
    }

    public void setSundayDutyCount(Integer sundayDutyCount) {
        this.sundayDutyCount = sundayDutyCount;
    }

    public String getRegularDutyDateStr() {
        return regularDutyDateStr;
    }

    public void setRegularDutyDateStr(String regularDutyDateStr) {
        this.regularDutyDateStr = regularDutyDateStr;
    }

    public String getFridayDutyDateStr() {
        return fridayDutyDateStr;
    }

    public void setFridayDutyDateStr(String fridayDutyDateStr) {
        this.fridayDutyDateStr = fridayDutyDateStr;
    }

    public String getLateDutyDateStr() {
        return lateDutyDateStr;
    }

    public void setLateDutyDateStr(String lateDutyDateStr) {
        this.lateDutyDateStr = lateDutyDateStr;
    }

    public String getSundayDutyDateStr() {
        return sundayDutyDateStr;
    }

    public void setSundayDutyDateStr(String sundayDutyDateStr) {
        this.sundayDutyDateStr = sundayDutyDateStr;
    }
}
