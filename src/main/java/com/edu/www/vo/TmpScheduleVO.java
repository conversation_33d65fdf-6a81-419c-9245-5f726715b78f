package com.edu.www.vo;

import java.util.Date;
import java.util.Objects;

/**
 * 临时课程表VO
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public class TmpScheduleVO extends PageVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 年度
     */
    private String year;

    /**
     * 学期
     */
    private String semester;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 部门编码
     */
    private String departmentCode;

    /**
     * 年级名称
     */
    private String gradeName;

    /**
     * 年级编码
     */
    private String gradeCode;

    /**
     * 班级编码
     */
    private String classCode;

    /**
     * 哪周
     */
    private String weekSeq;

    /**
     * 位置
     */
    private String position;

    /**
     * 内容
     */
    private String content;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 置换颜色
     */
    private String color;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 更新人
     */
    private String updatedBy;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TmpScheduleVO that = (TmpScheduleVO) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(departmentCode, that.departmentCode) &&
                Objects.equals(gradeCode, that.gradeCode) &&
                Objects.equals(classCode, that.classCode) &&
                Objects.equals(semester, that.semester) &&
                Objects.equals(year, that.year);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, departmentCode, gradeCode, classCode, semester, year);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getWeekSeq() {
        return weekSeq;
    }

    public void setWeekSeq(String weekSeq) {
        this.weekSeq = weekSeq;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
} 