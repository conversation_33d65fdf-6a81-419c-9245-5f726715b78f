package com.edu.www.validates;

import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.BookVO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class BookValidate {
    /**
     * 验证书籍信息
     *
     * @param bookVO
     * @param isUpdate
     */
    public static void validateBook(BookVO bookVO, boolean isUpdate) {
        ValidateUtil.paramValidate(Objects.isNull(bookVO), "书籍信息不能为空");
        ValidateUtil.paramValidate(isUpdate && StringUtils.isBlank(bookVO.getId()), "书籍ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(bookVO.getBookName()), "书名不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(bookVO.getIsbn()), "ISBN编号不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(bookVO.getBookCategory()), "书籍分类不能为空");
    }
}
