package com.edu.www.validates;

import com.edu.www.utils.FormatValidUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.TmpScheduleVO;
import com.edu.www.vo.UserVO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 指标平台参数校验
 *
 * <AUTHOR>
 * @date 2020/11/20
 */
public class ParamValidate {
    /**
     * 用户注册信息参数校验
     *
     * @param userVO
     */
    public static void validUserRegisterParam(UserVO userVO) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getLastName()), "姓不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getFirstName()), "名不能为空");
        ValidateUtil.paramValidate(Objects.isNull(userVO.getGender()), "性别不能为空");
        ValidateUtil.paramValidate(Objects.isNull(userVO.getBirthDate()), "出生日期不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getBirthdayType()), "生日类型不能为空");
        ValidateUtil.paramValidate(Objects.isNull(userVO.getBirthday()), "生日不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getDepartmentCode()), "部门不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getEmpNo()), "工号不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getEmail()), "用户邮箱不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getPassword()), "密码不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getInviteCode()), "邀请码不能为空");

        ValidateUtil.paramValidate(!FormatValidUtil.isValidEmail(userVO.getEmail()),"邮箱格式不正确");
    }

    /**
     * 用户注册信息参数校验
     *
     * @param userVO
     */
    public static void validUserInsertParam(UserVO userVO) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getLastName()), "姓不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getFirstName()), "名不能为空");
        ValidateUtil.paramValidate(Objects.isNull(userVO.getGender()), "性别不能为空");
        ValidateUtil.paramValidate(Objects.isNull(userVO.getBirthDate()), "出生日期不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getBirthdayType()), "生日类型不能为空");
        ValidateUtil.paramValidate(Objects.isNull(userVO.getBirthday()), "生日不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getDepartmentCode()), "部门不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getEmpNo()), "工号不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getEmail()), "用户邮箱不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getPassword()), "密码不能为空");

        ValidateUtil.paramValidate(!FormatValidUtil.isValidEmail(userVO.getEmail()),"邮箱格式不正确");
    }

    /**
     * 用户修改信息参数校验
     *
     * @param userVO
     */
    public static void validUserModifyParam(UserVO userVO) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getId()), "用户ID不能为空");
    }

    /**
     * 验证新增临时课程表的参数
     *
     * @param tmpScheduleVO 临时课程表VO对象
     */
    public static void validateInsertParams(TmpScheduleVO tmpScheduleVO) {
        ValidateUtil.paramValidate(StringUtils.isNotBlank(tmpScheduleVO.getDescription()) &&
                tmpScheduleVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ValidateUtil.paramValidate(StringUtils.isBlank(tmpScheduleVO.getYear()), "年度不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(tmpScheduleVO.getSemester()), "学期不能为空");
        ValidateUtil.paramValidate(Objects.isNull(tmpScheduleVO.getDepartmentCode()), "部门编码不能为空");
        ValidateUtil.paramValidate(Objects.isNull(tmpScheduleVO.getGradeCode()), "年级编码不能为空");
    }
} 