package com.edu.www.interceptor;

import com.edu.www.annotation.NoLoginRequired;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.Constant;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 登录拦截器 - 负责处理基于注解的身份验证
 * 主要处理 @NoLoginRequired 注解逻辑
 * 实际的 Token 验证由 AuthenticationFilter 负责
 */
@Component
public class LoginInterceptor implements HandlerInterceptor {
    // 添加日志记录
    private static final Logger log = LoggerFactory.getLogger(LoginInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 添加请求路径日志
        // log.debug("Intercepting request: {}", request.getRequestURI());

        // 检查是否有 @NoLoginRequired 注解
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            NoLoginRequired noLoginRequired = handlerMethod.getMethodAnnotation(NoLoginRequired.class);
            if (noLoginRequired != null) {
                return true; // 有方法级注解，不需要登录验证
            }

            // 也检查类级别的注解
            noLoginRequired = handlerMethod.getBeanType().getAnnotation(NoLoginRequired.class);
            if (noLoginRequired != null) {
                return true; // 有类级注解，不需要登录验证
            }
        }

        // 检查是否已登录 (通过 session 检查)
        // 注意：token 验证已经在 AuthenticationFilter 中处理
        boolean isLoggedIn = checkSessionLogin(request);
        if (!isLoggedIn) {
            responseUnauthorized(response);
            return false;
        }

        // 将userId存入请求属性中
        String userId = request.getHeader(Constant.X_USER_ID);
        request.setAttribute(Constant.USER_ID, userId);

        return true;
    }

    /**
     * 检查用户是否已通过 Session 登录
     */
    private boolean checkSessionLogin(HttpServletRequest request) {
        // 检查 Session
        HttpSession session = request.getSession(false);
        if (session != null) {
            Object userObj = session.getAttribute(Constant.SESSION_USER);
            if (userObj != null) {
                // 不做强制类型转换，只要有用户信息就表示已登录
                return true;
            }

            // 同时检查 "user" 键
            userObj = session.getAttribute(Constant.USER);
            if (userObj != null) {
                return true;
            }
        }

        // 如果有 token_expired 属性或者 currentUsername 属性，说明已经通过了 AuthenticationFilter 的验证
        Object tokenStatus = request.getAttribute("token_expired");
        Object username = request.getAttribute(Constant.CURRENT_USER_NAME);

        if (username != null && tokenStatus == null) {
            return true; // 已通过 AuthenticationFilter 验证
        }

        return false; // 未登录
    }

    /**
     * 返回未授权响应
     */
    private void responseUnauthorized(HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_OK);

        ResponseEntity<?> result = ResponseEntity.unauthorized();
        ObjectMapper mapper = new ObjectMapper();
        response.getWriter().write(mapper.writeValueAsString(result));
    }
}
