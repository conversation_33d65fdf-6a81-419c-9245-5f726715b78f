package com.edu.www.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MessageDigestUtil {

    /**
     * MD5加密
     *
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String encryptMD5(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(data);
        byte[] resultBytes = md5.digest();
        String resultString = BytesToHex.fromBytesToHex(resultBytes);
        return resultString;
    }

    /**
     * SHA 加密
     *
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String encryptSHA(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest sha = MessageDigest.getInstance("SHA-512");
        sha.update(data);
        byte[] resultBytes = sha.digest();
        String resultString = BytesToHex.fromBytesToHex(resultBytes);
        return resultString;
    }
}
