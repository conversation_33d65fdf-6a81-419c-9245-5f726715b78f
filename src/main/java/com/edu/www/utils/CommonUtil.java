package com.edu.www.utils;

import java.time.LocalDate;
import java.time.Month;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 公共工具Util
 *
 * <AUTHOR>
 * @date 2020/11/17
 */
public class CommonUtil {

    // 预定义一组明亮、容易区分的颜色
    private static final List<String> DISTINCT_COLORS = Arrays.asList(
            "#FF5733", // 亮橙红
            "#33A8FF", // 亮蓝
            "#33FF57", // 亮绿
            "#FF33A8", // 亮粉
            "#A833FF", // 亮紫
            "#FFDD33", // 亮黄
            "#33FFDD", // 青绿
            "#DD33FF", // 紫粉
            "#FF8C33", // 橙色
            "#3366FF", // 蓝色
            "#FF3366", // 红色
            "#33FF99", // 薄荷绿
            "#9933FF", // 紫色
            "#FF9933", // 金黄
            "#33FFFF", // 青色
            "#FF33FF"  // 品红
    );

    private static final Random random = new Random();

    /**
     * 翻译星期
     *
     * @param weekdayCode
     * @return
     */
    public static String getWeekdayName(String weekdayCode) {
        switch (weekdayCode) {
            case "1":
                return "周一";
            case "2":
                return "周二";
            case "3":
                return "周三";
            case "4":
                return "周四";
            case "5":
                return "周五";
            default:
                return "周" + weekdayCode;
        }
    }

    /**
     * 翻译年级
     *
     * @param gradeCode
     * @return
     */
    public static String getGradeName(String gradeCode) {
        switch (gradeCode) {
            case "6":
                return "六年级";
            case "7":
                return "七年级";
            case "8":
                return "八年级";
            case "9":
                return "九年级";
            case "10":
                return "十年级";
            case "11":
                return "十一年级";
            case "12":
                return "十二年级";
            case "spring":
                return "春季班";
            default:
                return gradeCode;
        }
    }

    /**
     * 根据当前时间获取学期
     *
     * @return
     */
    public static String getCurrentSemester() {
        LocalDate now = LocalDate.now();
        Month currentMonth = now.getMonth();
        int dayOfMonth = now.getDayOfMonth();

        // S2：第2学期，从1月开始，到7月结束。
        if ((currentMonth == Month.JANUARY && dayOfMonth >= 1) ||
                currentMonth.getValue() >= Month.FEBRUARY.getValue() && currentMonth.getValue() <= Month.JUNE.getValue() ||
                (currentMonth == Month.JULY && dayOfMonth <= 31)) {
            return "S2";
        }

        return "S1";
    }

    /**
     * 生成随机显眼的颜色，用于标记交换的表格
     *
     * @return 十六进制颜色代码，如 "#FF5733"
     */
    public static String getRandomColor() {
        // 优先从预定义的显眼颜色中选择
        if (!DISTINCT_COLORS.isEmpty()) {
            return DISTINCT_COLORS.get(random.nextInt(DISTINCT_COLORS.size()));
        }

        // 备选方案：随机生成亮色
        // 确保颜色明亮且饱和度高：至少有一个RGB值大于200，其他不小于50
        int r, g, b;
        do {
            r = 50 + random.nextInt(206);
            g = 50 + random.nextInt(206);
            b = 50 + random.nextInt(206);
        } while (r < 200 && g < 200 && b < 200);

        return String.format("#%02X%02X%02X", r, g, b);
    }
}