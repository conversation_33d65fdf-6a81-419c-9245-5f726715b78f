package com.edu.www.utils;

import com.edu.www.common.ResponseEntity;
import com.edu.www.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EncryptUtil {
    private static final Logger logger = LoggerFactory.getLogger(EncryptUtil.class);

    /**
     * 加密字符
     *
     * @param str
     * @return
     */
    public static ResponseEntity<Object> encrypt(String str) {
        ValidateUtil.paramValidate(StringUtils.isBlank(str), "加密字符不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        try {
            /* MD5+SHA-512 加密*/
            String md5Result = MessageDigestUtil.encryptMD5(str.getBytes());
            String resultStr = MessageDigestUtil.encryptSHA(md5Result.getBytes());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(resultStr);
        } catch (Exception e) {
            logger.error("加密字符时异常信息={}", e.getMessage(), e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("加密字符时异常");
            return responseEntity;
        }
        return responseEntity;
    }
}
