package com.edu.www.utils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Map工具类
 */
public class MapUtil {
    /**
     * 获取 Map 中迭代时遇到的第一个 Key 值。
     *
     * @param map 要操作的 Map
     * @return 第一个 Key 值，如果 Map 为空则返回 null
     */
    public static <K, V> K getFirstKey(Map<K, V> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        return map.keySet().iterator().next();

        // 方案二（等价）：使用 entrySet() 获取 Entry 的 Set 视图，然后获取迭代器的第一个 Entry 的 Key
        // return map.entrySet().iterator().next().getKey();
    }

    /**
     * 从给定的 Map 中提取所有 Key，并以 Set 的形式返回。
     * Set 保证了 Key 的唯一性。
     *
     * @param map 待提取 Key 的 Map 对象
     * @param <K> Map 中 Key 的类型
     * @param <V> Map 中 Value 的类型
     * @return 包含所有 Key 的 Set，如果 Map 为 null 或为空，则返回一个空 Set
     */
    public static <K, V> Set<K> getAllKeyAsSet(Map<K, V> map) {
        if (map == null || map.isEmpty()) {
            return new HashSet<>(); // 返回一个空的 HashSet
        }
        return map.keySet(); // Map 的 keySet() 方法直接返回一个 Set 视图
    }

}
