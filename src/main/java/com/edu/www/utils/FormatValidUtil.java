package com.edu.www.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 校验工具 Util
 *
 * <AUTHOR>
 * @date 2024/09/07
 */
public class FormatValidUtil {
    /**
     * 邮箱格式校验
     *
     * @param email
     * @return
     */
    public static boolean isValidEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return false; // 邮箱为空或null，直接返回false
        }
        // 常见的邮箱格式正则表达式
        String regex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }
}
