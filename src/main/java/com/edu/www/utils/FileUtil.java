package com.edu.www.utils;

import com.edu.www.enums.EduFileTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

public class FileUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 判断文件是否为图片类型
     *
     * @param extension 文件扩展名
     * @return 是否为图片
     */
    public static boolean isImageFile(String extension) {
        if (StringUtils.isBlank(extension)) {
            return false;
        }

        String[] imageExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        for (String imageExt : imageExtensions) {
            if (imageExt.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断文件是否可预览
     *
     * @param extension 文件扩展名
     * @return 是否可预览
     */
    public static boolean isPreviewableFile(String extension) {
        if (StringUtils.isBlank(extension)) {
            return false;
        }
        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
            case "pdf":
            case "txt":
                return true;
            default:
                return false;
        }
    }

    /**
     * 输出文件到HTTP响应
     *
     * @param file     文件对象
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public static void outputFileToResponse(File file, HttpServletResponse response) throws IOException {
        // 设置响应头
        String fileName = file.getName();
        String contentType = getContentType(fileName);
        response.setContentType(contentType);
        response.setContentLength((int) file.length());

        // 设置为内联显示（预览）而不是下载
        response.setHeader("Content-Disposition", "inline; filename=\"" + fileName + "\"");

        // 设置缓存控制
        response.setHeader("Cache-Control", "public, max-age=3600");
        response.setDateHeader("Expires", System.currentTimeMillis() + 3600 * 1000);

        // 输出文件内容
        try (FileInputStream fis = new FileInputStream(file);
             OutputStream os = response.getOutputStream()) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.flush();
        }
    }

    /**
     * 根据URL获取Content-Type
     *
     * @param url URL
     * @return Content-Type
     */
    public static String getContentTypeFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "application/octet-stream";
        }

        String lowerUrl = url.toLowerCase();
        if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerUrl.endsWith(".png")) {
            return "image/png";
        } else if (lowerUrl.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerUrl.endsWith(".bmp")) {
            return "image/bmp";
        } else if (lowerUrl.endsWith(".webp")) {
            return "image/webp";
        } else if (lowerUrl.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerUrl.endsWith(".txt")) {
            return "text/plain";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * 根据文件名获取Content-Type
     *
     * @param fileName 文件名
     * @return Content-Type
     */
    public static String getContentType(String fileName) {
        EduFileTypeEnum fileType = EduFileTypeEnum.getByFileName(fileName);
        String extension = fileType.getExtension().toLowerCase();

        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/avi";
            case "mov":
                return "video/quicktime";
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "pdf":
                return "application/pdf";
            case "txt":
                return "text/plain";
            case "doc":
            case "docx":
                return "application/msword";
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "ppt":
            case "pptx":
                return "application/vnd.ms-powerpoint";
            default:
                return "application/octet-stream";
        }
    }

}
