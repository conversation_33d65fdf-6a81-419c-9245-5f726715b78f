package com.edu.www.utils;

import com.edu.www.dto.UserDTO;
import com.edu.www.constants.Constant;
import com.edu.www.convert.UserConverter;
import com.edu.www.vo.UserVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * 请求信息Util
 *
 * <AUTHOR>
 * @date 2020/11/17
 */
public class RequestMsgUtil {

    /**
     * 获取RedisUtil实例
     */
    private static RedisUtil getRedisUtil() {
        try {
            return SpringContextHolder.getBean(RedisUtil.class);
        } catch (Exception e) {
            // 发生异常时返回null
            return null;
        }
    }

    /**
     * 获取登录用户信息
     *
     * @return 当前登录用户的用户名
     */
    public static String getSessionUserName() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            // 第一步：从session中获取
            HttpSession session = request.getSession(false);
            if (session != null) {
                // 尝试使用Constant.SESSION_USER键获取
                Object userObj = session.getAttribute(Constant.SESSION_USER);
                if (userObj != null) {
                    // 判断对象类型并获取相应的用户名
                    if (userObj instanceof UserDTO) {
                        UserDTO user = (UserDTO) userObj;
                        return StringUtils.isNotEmpty(user.getNameZh()) ? user.getNameZh() : user.getNameEn();
                    } else if (userObj instanceof UserVO) {
                        UserVO user = (UserVO) userObj;
                        return StringUtils.isNotEmpty(user.getNameZh()) ? user.getNameZh() : user.getNameEn();
                    }
                }
                
                // 尝试使用"user"键获取
                userObj = session.getAttribute(Constant.USER);
                if (userObj != null) {
                    if (userObj instanceof UserDTO) {
                        UserDTO user = (UserDTO) userObj;
                        return StringUtils.isNotEmpty(user.getNameZh()) ? user.getNameZh() : user.getNameEn();
                    } else if (userObj instanceof UserVO) {
                        UserVO user = (UserVO) userObj;
                        return StringUtils.isNotEmpty(user.getNameZh()) ? user.getNameZh() : user.getNameEn();
                    }
                }
            }
            
            // 第二步：从request属性中获取username
            String currentUsername = (String) request.getAttribute(Constant.CURRENT_USER_NAME);
            if (StringUtils.isNotEmpty(currentUsername)) {
                // 尝试从Redis中获取用户信息
                RedisUtil redisUtil = getRedisUtil();
                if (redisUtil != null) {
                    Object userObj = redisUtil.get(Constant.LOGIN_REDIS_KEY + currentUsername);
                    if (userObj != null) {
                        if (userObj instanceof UserDTO) {
                            UserDTO user = (UserDTO) userObj;
                            return StringUtils.isNotEmpty(user.getNameZh()) ? user.getNameZh() : user.getNameEn();
                        } else if (userObj instanceof UserVO) {
                            UserVO user = (UserVO) userObj;
                            return StringUtils.isNotEmpty(user.getNameZh()) ? user.getNameZh() : user.getNameEn();
                        }
                    }
                }
            }
        }
        
        // 如果所有方法都无法获取用户名，返回默认值
        return "System";
    }

    /**
     * 获取当前登录用户对象
     *
     * @return 当前登录用户对象，UserDTO类型，可能为null
     */
    public static UserDTO getSessionUser() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            // 从session中获取
            HttpSession session = request.getSession(false);
            if (session != null) {
                // 尝试使用Constant.SESSION_USER键获取
                Object userObj = session.getAttribute(Constant.SESSION_USER);
                if (userObj != null) {
                    if (userObj instanceof UserDTO) {
                        return (UserDTO) userObj;
                    } else if (userObj instanceof UserVO) {
                        return UserConverter.userVO2DTO((UserVO) userObj);
                    }
                }
                
                // 尝试使用"user"键获取
                userObj = session.getAttribute(Constant.USER);
                if (userObj != null) {
                    if (userObj instanceof UserDTO) {
                        return (UserDTO) userObj;
                    } else if (userObj instanceof UserVO) {
                        return UserConverter.userVO2DTO((UserVO) userObj);
                    }
                }
            }
            
            // 从request属性中获取username
            String currentUsername = (String) request.getAttribute(Constant.CURRENT_USER_NAME);
            if (StringUtils.isNotEmpty(currentUsername)) {
                // 尝试从Redis中获取用户信息
                RedisUtil redisUtil = getRedisUtil();
                if (redisUtil != null) {
                    Object userObj = redisUtil.get(Constant.LOGIN_REDIS_KEY + currentUsername);
                    if (userObj != null) {
                        if (userObj instanceof UserDTO) {
                            return (UserDTO) userObj;
                        } else if (userObj instanceof UserVO) {
                            return UserConverter.userVO2DTO((UserVO) userObj);
                        }
                    }
                }
            }
        }
        
        // 如果无法获取用户对象，返回null
        return null;
    }

    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
