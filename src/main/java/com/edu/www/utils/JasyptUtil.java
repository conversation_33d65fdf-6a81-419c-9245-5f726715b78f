package com.edu.www.utils;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

public class JasyptUtil {

    public static String encrypt(String plainText, String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor.encrypt(plainText);
    }

    public static String decrypt(String encryptedText, String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor.decrypt(encryptedText);
    }

    public static void main(String[] args) {
        // todo 后续稳定了可以删除掉
        // 用于生成加密后的密码
        // 91uy2bZGTDeOJK2UgCOqx7QJQfQFH1Kj
        // System.out.println(encrypt("yuyongtao", "QozcXesgJada8+"));
        // D44IPa2rPcOV3YtQjsYBJA==
        // System.out.println(encrypt("root", "QozcXesgJada8+"));
        // ceKIMipZcIs+iLRsgu27XqFGHHHeCWtz2xR5Dv9l8RUF+6U2ZIiiMaXGz5goxsThr9y625acReLxyiNkK5i5YI4S1KDFXPp78wtMUpVsur0= todo 上线前更换密钥
        // System.out.println(encrypt("fe0b465e302768ddaaeba94ad4ed4621f8030b26a14583a24f9d2c950b67c909", "QozcXesgJada8+"));

        // JWT  FiGYydF2lzJf3hf/VoC3FooQEeFJb9PEALB077C4Lfz1iaZGTC18i+OijLiBEpLJ/g4/0ZkXT1M=
        // System.out.println(encrypt("VIcUuCYNrS7/5GBUsdduP6j+6986uQgt5VzQP0U4lII=", "QozcXesgJada8+"));

        // GitHub  Mcn0xxyFTcMeFqdQVB/r58cf4N+rkja/9uYBHzkKScNHw4VAVwhwJAq0QeNw00bNSLEW8rD/2w8OVK5nKNxrHckjyUif2XmWWA1JrxEyn+lzbp1KQdySmkbeQt6z3w93heY2147HnMQ=
        // System.out.println(encrypt("*********************************************************************************************", "QozcXesgJada8+"));
    }
}
