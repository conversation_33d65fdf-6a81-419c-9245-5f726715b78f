package com.edu.www.utils;

import java.nio.ByteBuffer;
import java.util.Date;
import java.util.Random;


public class UniqueCodeUtil {
    private static final String SUPPLIER_CODE_PREFIX = "SP";
    private static final String PURCHASE_ORDER_PREFIX = "PO";
    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final Random RANDOM = new Random();


    // 编码为 Base62
    private static String base62Encode(byte[] input) {
        StringBuilder sb = new StringBuilder();
        ByteBuffer buffer = ByteBuffer.wrap(input);
        while (buffer.hasRemaining()) {
            int value = Byte.toUnsignedInt(buffer.get());
            sb.append(BASE62.charAt(value % 62));
        }
        return sb.toString();
    }

    // 生成邀请码
    public static String generateInviteCode() {
        long timestamp = System.currentTimeMillis(); // 13 位
        int random = RANDOM.nextInt(999999);         // 6 位以内
        ByteBuffer buffer = ByteBuffer.allocate(10); // 合计不会超过 10 字节
        buffer.putLong(timestamp);
        buffer.putShort((short) random);

        return base62Encode(buffer.array()); // 约 16~20 个字符
    }

    /**
     * 生成供应商编码
     * 格式：SP + 年月日时分秒 + 4位随机数
     * 例如：SP20250717001
     *
     * @return 供应商编码
     */
    public static String generateSupplierCode() {
        // 获取当前日期，格式：yyyyMMddHHmmss
        String dateStr = DateUtil.FormatDate(new Date(), DateUtil.FORMAT_DATE_TIME_COMPRESSED);

        // 生成4位随机数
        int randomNum = RANDOM.nextInt(9999) + 1; // 1-9999
        String randomStr = String.format("%04d", randomNum);

        // 组合编码：SP + 日期 + 随机数
        return SUPPLIER_CODE_PREFIX + dateStr + randomStr;
    }

    /**
     * 生成采购订单编码
     * 格式：PO + 年月日时分秒 + 4位随机数
     * 例如：PO20250717001
     *
     * @return 采购订单编码
     */
    public static String generatePurchaseOrderNo() {
        // 获取当前日期，格式：yyyyMMddHHmmss
        String dateStr = DateUtil.FormatDate(new Date(), DateUtil.FORMAT_DATE_TIME_COMPRESSED);

        // 生成4位随机数
        int randomNum = RANDOM.nextInt(9999) + 1; // 1-9999
        String randomStr = String.format("%04d", randomNum);

        // 组合编码：PO + 日期 + 随机数
        return PURCHASE_ORDER_PREFIX + dateStr + randomStr;
    }
}
