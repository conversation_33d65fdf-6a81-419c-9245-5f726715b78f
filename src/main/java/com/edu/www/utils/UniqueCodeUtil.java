package com.edu.www.utils;

import java.nio.ByteBuffer;
import java.util.Random;


public class UniqueCodeUtil {
    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final Random RANDOM = new Random();

    // 编码为 Base62
    private static String base62Encode(byte[] input) {
        StringBuilder sb = new StringBuilder();
        ByteBuffer buffer = ByteBuffer.wrap(input);
        while (buffer.hasRemaining()) {
            int value = Byte.toUnsignedInt(buffer.get());
            sb.append(BASE62.charAt(value % 62));
        }
        return sb.toString();
    }

    // 生成邀请码
    public static String generateInviteCode() {
        long timestamp = System.currentTimeMillis(); // 13 位
        int random = RANDOM.nextInt(999999);         // 6 位以内
        ByteBuffer buffer = ByteBuffer.allocate(10); // 合计不会超过 10 字节
        buffer.putLong(timestamp);
        buffer.putShort((short) random);

        return base62Encode(buffer.array()); // 约 16~20 个字符
    }
}
