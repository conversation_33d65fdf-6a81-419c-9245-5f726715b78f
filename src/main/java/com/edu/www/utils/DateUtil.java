package com.edu.www.utils;

import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;

import java.sql.Time;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 时间工具
 */
public class DateUtil {

    /**
     * Date format: yyyy-MM
     */
    public static final String FORMAT_YEAR_MONTH = "yyyy-MM";

    /**
     * Date format: MM.dd
     */
    public static final String FORMAT_MONTH_DAY_DOT = "MM.dd";

    /**
     * Date format: yyyyMM (YearMonth compressed)
     */
    public static final String FORMAT_YEAR_MONTH_COMPRESSED = "yyyyMM";

    /**
     * Date format: yyyy-MM-dd
     */
    public static final String FORMAT_DATE_SHORT = "yyyy-MM-dd";

    /**
     * Date format: yyyyMMdd (Date compressed)
     */
    public static final String FORMAT_DATE_COMPRESSED = "yyyyMMdd";

    /**
     * Date format: yyyy-MM-dd HH:mm
     */
    public static final String FORMAT_DATE_TIME_MINUTE = "yyyy-MM-dd HH:mm";

    /**
     * Date format: yyyy-MM-dd HH:mm:ss
     */
    public static final String FORMAT_DATE_TIME_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * Date format: yyyyMMddHHmmss (DateTime compressed)
     */
    public static final String FORMAT_DATE_TIME_COMPRESSED = "yyyyMMddHHmmss";

    /**
     * Date format: yyyyMMdd_HHmmss (DateTime compressed with underscore)
     */
    public static final String FORMAT_DATE_TIME_COMPRESSED_UNDERSCORE = "yyyyMMdd_HHmmss";

    /**
     * Date format: yyMMddHHmm (Minute long compressed)
     */
    public static final String FORMAT_DATE_TIME_MINUTE_SHORT_YEAR_COMPRESSED = "yyMMddHHmm";

    /**
     * Date format: MMddHHmmss (MonthDayTime compressed)
     */
    public static final String FORMAT_MONTH_DAY_TIME_COMPRESSED = "MMddHHmmss";

    /**
     * Date format: yyyy年MM月dd日 (Chinese Year Month Day)
     */
    public static final String FORMAT_DATE_CHINESE_YEAR_MONTH_DAY = "yyyy年MM月dd日";

    /**
     * Date format: MM月dd日 (Chinese Month Day)
     */
    public static final String FORMAT_DATE_CHINESE_MONTH_DAY = "MM月dd日";

    /**
     * Date format: yyyy年MM月dd日 HH:mm:ss (Chinese DateTime)
     */
    public static final String FORMAT_DATE_TIME_CHINESE = "yyyy年MM月dd日 HH:mm:ss";

    /**
     * Date format: d MMMM, yyyy (English Year Month Day)
     */
    public static final String FORMAT_DATE_ENGLISH_LONG = "d MMMM, yyyy";

    /**
     * Date format: MMMM, yyyy (English Year Month)
     */
    public static final String FORMAT_MONTH_YEAR_ENGLISH = "MMMM, yyyy";

    /**
     * Date format: yyyy年MM月 (Chinese Year Month)
     */
    public static final String FORMAT_YEAR_MONTH_CHINESE = "yyyy年MM月";

    /**
     * Time format: HH:mm
     */
    public static final String FORMAT_TIME_HOUR_MINUTE = "HH:mm";

    /**
     * 将时间格式化为"HH:mm分"的24小时制格式
     *
     * @param timeStr 原始时间字符串 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 格式化后的时间字符串
     */
    public static String formatTime24Hour(String timeStr) {
        try {
            SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = originalFormat.parse(timeStr);

            SimpleDateFormat targetFormat = new SimpleDateFormat("HH:mm");
            String formattedTime = targetFormat.format(date);

            return formattedTime + "分";
        } catch (Exception e) {
            // 如果解析失败，返回原始时间字符串
            return timeStr;
        }
    }

    /**
     * 对日期进行格式化
     *
     * @param date 日期
     * @param sf   日期格式
     * @return 字符串
     */
    public static String FormatDate(Date date, String sf) {
        if (date == null)
            return "";
        SimpleDateFormat dateformat = new SimpleDateFormat(sf);
        return dateformat.format(date);
    }

    /**
     * 对日期进行格式化
     *
     * @param date
     * @param sf
     * @return
     */
    public static String FormatDateEn(Date date, String sf) {
        if (date == null)
            return "";
        SimpleDateFormat dateformat = new SimpleDateFormat(sf, Locale.US);
        return dateformat.format(date);
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getCurrentDate(String sf) {
        Date date_time = new Date();
        return FormatDate(date_time, sf);
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getCurrentDateEn(String sf) {
        Date date_time = new Date();
        return FormatDateEn(date_time, sf);
    }

    /**
     * 格式化在读证明日期
     *
     * @param date          日期
     * @param isEnglish     是否英文格式
     * @param isCurrentDate 是否当前日期（当前日期只显示年月）
     * @return 格式化后的日期字符串
     */
    public static String formatReadingCertificateDate(Date date, boolean isEnglish, boolean isCurrentDate) {
        if (date == null) {
            return "";
        }

        if (isCurrentDate) {
            return isEnglish ?
                    FormatDateEn(date, FORMAT_MONTH_YEAR_ENGLISH) :
                    FormatDate(date, FORMAT_YEAR_MONTH_CHINESE);
        }

        return isEnglish ?
                FormatDateEn(date, FORMAT_DATE_ENGLISH_LONG) :
                FormatDate(date, FORMAT_DATE_CHINESE_YEAR_MONTH_DAY);
    }

    /**
     * 将 Date 类型转换为年月格式的 Date 类型。
     *
     * @param date 要转换的 Date 对象，如果为 null 则返回 null。
     * @return 只包含年月的 Date 对象，格式为 "yyyy-MM"，如果输入为 null 则返回 null。
     */
    public static Date toYearMonthDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_YEAR_MONTH);
        String yearMonthString = sdf.format(date);
        try {
            return sdf.parse(yearMonthString);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将当前时间加上指定的分钟数，并返回 java.util.Date 类型。
     *
     * @param minutesToAdd 要添加的分钟数 (可以为负数以减去时间)。
     * @return 加上指定分钟数后的 java.util.Date 对象。
     */
    public static Date addMinutesToCurrentDate(long minutesToAdd) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime futureDateTime = currentDateTime.plus(minutesToAdd, ChronoUnit.MINUTES);
        return Date.from(futureDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 判断给定的 Date 对象是否已经过期（忽略时区，直接比较时间点）。
     * 过期的标准是该 Date 对象表示的时间是否早于当前系统默认时区的时间。
     *
     * @param expirationDate 要判断的过期日期。
     * @return 如果过期则返回 true，否则返回 false。如果传入 null，则认为未过期。
     */
    public static boolean isDateExpired(Date expirationDate) {
        if (expirationDate == null) {
            return false; // 或者根据您的业务逻辑，null 可以认为是已过期
        }
        // 获取当前时间的 Date 对象 (使用系统默认时区)
        Date now = new Date();
        // 比较传入的过期日期是否早于当前时间
        return expirationDate.before(now);
    }

    /**
     * 格式化时间
     *
     * @param startTime
     * @param sf
     * @return
     */
    public static String formatTime(Time startTime, String sf) {
        // java.sql.Time 可以直接转换为 LocalTime
        LocalTime localTime = startTime.toLocalTime();
        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(sf);
        // 格式化 LocalTime
        return localTime.format(formatter);
    }

    /**
     * 将字符串日期转换为 LocalDate 对象。
     *
     * @param dateString 要转换的日期字符串。
     * @param format     日期字符串的格式 (e.g., "yyyy-MM-dd", "yyyy/MM/dd")。
     * @return 一个 LocalDate 对象，如果解析失败则返回 null。
     */
    public static Date stringToDate(String dateString, String format) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(dateString);
        } catch (Exception e) {
            System.err.println("Error parsing date: " + dateString + " with format: " + format);
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取指定周序的开始日期(周一)
     *
     * @param weekSeq 周序
     * @return 开始日期
     */
    public static Date getStartDateByWeekSeq(String weekSeq) {
        if (weekSeq == null) {
            return null;
        }

        // 获取当前日期的Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 计算本周的周一日期（Calendar.MONDAY = 2）
        // 将日期调整为本周的周一
        int currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int daysToSubtract = (currentDayOfWeek == Calendar.SUNDAY) ? 6 : currentDayOfWeek - Calendar.MONDAY;
        calendar.add(Calendar.DAY_OF_MONTH, -daysToSubtract);

        // 根据weekSeq添加相应的周数
        switch (weekSeq) {
            case "this_week":
                // 本周不需要额外添加
                break;
            case "next_week":
                // 下周，添加7天
                calendar.add(Calendar.DAY_OF_MONTH, 7);
                break;
            case "the_week_after_next":
                // 下下周，添加14天
                calendar.add(Calendar.DAY_OF_MONTH, 14);
                break;
            case "the_week_after_that":
                // 大大下周，添加21天
                calendar.add(Calendar.DAY_OF_MONTH, 21);
                break;
            default:
                return null;
        }

        return calendar.getTime();
    }

    /**
     * 获取指定周序的结束日期(周五)
     *
     * @param weekSeq 周序
     * @return 结束日期
     */
    public static Date getEndDateByWeekSeq(String weekSeq) {
        Date startDate = getStartDateByWeekSeq(weekSeq);
        if (startDate == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        // 计算周五日期（从周一开始加4天）
        calendar.add(Calendar.DAY_OF_MONTH, 4);

        return calendar.getTime();
    }

    /**
     * 获取当前时间是星期几的名称
     *
     * @return 星期几的名称 (例如: saturday).
     */
    public static String getCurrentDayWeekName() {
        LocalDate now = LocalDate.now();
        DayOfWeek dayOfWeek = now.getDayOfWeek();
        return dayOfWeek.getDisplayName(TextStyle.FULL, Locale.ENGLISH).toLowerCase();
    }

    /**
     * 获取指定日期是星期几的英文名称
     *
     * @param date 指定的日期
     * @return 星期几的英文名称 (例如: monday, tuesday, wednesday, thursday, friday, saturday, sunday)
     * 如果传入的日期为null，则返回null
     */
    public static String getDayWeekName(Date date) {
        if (date == null) {
            return null;
        }

        // 将Date转换为LocalDate
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 获取星期几
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();

        // 返回英文全名的小写形式
        return dayOfWeek.getDisplayName(TextStyle.FULL, Locale.ENGLISH).toLowerCase();
    }

    public static String getCurrentPeriod() {
        ZonedDateTime nowOsaka = ZonedDateTime.now();
        LocalTime currentTime = nowOsaka.toLocalTime();

        for (Map.Entry<String, String> entry : CommonConstant.periodTime.entrySet()) {
            String periodName = entry.getKey();
            String timeRange = entry.getValue();
            String[] times = timeRange.split(Constant.HYPHEN);
            DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(FORMAT_TIME_HOUR_MINUTE);
            if (times.length == 2) {
                LocalTime startTime = LocalTime.parse(times[0], TIME_FORMATTER);
                LocalTime endTime = LocalTime.parse(times[1], TIME_FORMATTER);
                if (!currentTime.isBefore(startTime) && !currentTime.isAfter(endTime)) {
                    return periodName;
                }
            }
        }
        return null;
    }

    /**
     * dateToTime
     *
     * @param date
     * @return
     */
    public static Time dateToTime(Date date) {
        if (date == null) {
            return null;
        }
        // java.sql.Time 可以直接通过传入一个 long 类型的时间戳来创建，
        // 这个时间戳表示自 1970 年 1 月 1 日 00:00:00 GMT 以来的毫秒数。
        // java.util.Date 的 getTime() 方法正是返回这个时间戳。
        return new Time(date.getTime());
    }
}
