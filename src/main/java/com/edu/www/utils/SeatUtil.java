package com.edu.www.utils;

import com.edu.www.constants.Constant;

import java.util.*;
import java.util.stream.Collectors;

public class SeatUtil {
    public static String formatCourses(Set<String> courses) {
        if (courses == null || courses.isEmpty()) {
            return "";
        }

        // 使用 Map 来存储课程的组合信息
        // Key: 学科名称 + "-" + 课程类型 (例如 "Math-AA", "Biology")
        // Value: 该组合对应的所有级别 (例如 {"HL", "SL"})
        Map<String, Set<String>> combinedCourseLevels = new HashMap<>();

        // 预处理输入，将 "Biolody" 修正为 "Biology"，并规范化分隔符
        Set<String> processedCourses = courses.stream()
                .map(s -> s.replace("Biolody", "Biology").replace(Constant.SPACE, Constant.HYPHEN)) // 将空格替换为连字符
                .collect(Collectors.toCollection(LinkedHashSet::new));


        for (String course : processedCourses) {
            String[] parts = course.split(Constant.HYPHEN);
            if (parts.length < 2) {
                continue; // 跳过不符合格式的字符串
            }

            String subject;
            String type = ""; // 默认为空，适用于没有细分类型的课程（如 Biology）
            String level;

            if (parts.length == 3) {
                // 例如 "Math-AA-HL" -> parts = ["Math", "AA", "HL"]
                subject = parts[0];
                type = parts[1];
                level = parts[2];
            } else {
                // 例如 "Biology-SL" -> parts = ["Biology", "SL"]
                subject = parts[0];
                level = parts[1];
            }

            // 构建用于 Map 的 Key
            String mapKey = subject;
            if (!type.isEmpty()) {
                mapKey += Constant.HYPHEN + type;
            }

            // 将级别添加到对应的 Set 中
            combinedCourseLevels.computeIfAbsent(mapKey, k -> new LinkedHashSet<>()).add(level);
        }

        // 构建最终的字符串
        StringBuilder resultBuilder = new StringBuilder();

        // 使用自然排序，按科目名称的字母顺序排列
        // 这样可以动态处理所有科目类型，而不是写死特定科目
        combinedCourseLevels.entrySet().stream()
                .sorted(Map.Entry.comparingByKey()) // 按key的字母顺序排序
                .forEach(entry -> {
                    if (resultBuilder.length() > 0) {
                        resultBuilder.append("、");
                    }
                    String mapKey = entry.getKey();
                    Set<String> levels = entry.getValue();

                    String subjectName;
                    String levelPart;

                    // 将级别按字母顺序排序，然后用&符号连接
                    String sortedLevels = levels.stream().sorted().collect(Collectors.joining("&"));

                    if (mapKey.contains(Constant.HYPHEN)) {
                        // 例如 "Math-AA"
                        String[] keyParts = mapKey.split(Constant.HYPHEN);
                        subjectName = keyParts[0]; // "Math"
                        levelPart = keyParts[1] + "&" + sortedLevels; // "AA&HL" 或 "AI&SL"
                    } else {
                        // 例如 "Biology" 或 "Chemistry"
                        subjectName = mapKey;
                        levelPart = sortedLevels; // "SL&HL" 或 "HL"
                    }

                    resultBuilder.append(subjectName).append("(").append(levelPart).append(")");
                });

        return resultBuilder.toString();
    }
}
