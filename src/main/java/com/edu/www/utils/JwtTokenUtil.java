package com.edu.www.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
public class JwtTokenUtil {
    @Value("${jwt.secret:defaultSecretKey}")
    private String secret;

    @Value("${jwt.expiration:86400}")
    private Long expiration;

    // 添加日志对象
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(JwtTokenUtil.class);

    // 添加一个PostConstruct方法，查看配置是否正确加载
    @javax.annotation.PostConstruct
    public void init() {
    }

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    public String generateToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        return createToken(claims, username);
    }

    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date(System.currentTimeMillis());
        Date expirationDate = new Date(now.getTime() + expiration * 1000);
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expirationDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    public Boolean validateToken(String token, String username) {
        try {
            final String extractedUsername = extractUsername(token);
            boolean expired = isTokenExpired(token);
            return (extractedUsername.equals(username) && !expired);
        } catch (Exception e) {
            logger.error("验证token时出错", e);
            return false;
        }
    }

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    private Claims extractAllClaims(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    private Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * 检查 token 是否接近过期（还剩余 5 分钟以内）
     * @param token JWT token
     * @return 如果 token 接近过期返回 true，否则返回 false
     */
    public Boolean isTokenNearExpiration(String token) {
        try {
            Date expiration = extractExpiration(token);
            // 当前时间加上 5 分钟
            Date threshold = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
            // 如果过期时间在阈值之前，则认为即将过期
            return expiration.before(threshold);
        } catch (Exception e) {
            return true; // 出现异常，可能 token 无效，当作即将过期处理
        }
    }
    
    /**
     * 刷新 token
     * @param token 旧的 token
     * @return 新的 token
     */
    public String refreshToken(String token) {
        try {
            final Claims claims = extractAllClaims(token);
            String username = claims.getSubject();
            Map<String, Object> claimsMap = new HashMap<>(claims);
            return createToken(claimsMap, username);
        } catch (Exception e) {
            throw new RuntimeException("Failed to refresh token", e);
        }
    }
}
