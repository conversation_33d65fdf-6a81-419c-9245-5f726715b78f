package com.edu.www.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 供应商编码生成工具类
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public class SupplierCodeUtil {

    private static final String PREFIX = "SP";
    private static final Random RANDOM = new Random();

    /**
     * 生成供应商编码
     * 格式：SP + 年月日 + 4位随机数
     * 例如：SP20250717001
     *
     * @return 供应商编码
     */
    public static String generateSupplierCode() {
        // 获取当前日期，格式：yyyyMMdd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());
        
        // 生成4位随机数
        int randomNum = RANDOM.nextInt(9999) + 1; // 1-9999
        String randomStr = String.format("%04d", randomNum);
        
        // 组合编码：SP + 日期 + 随机数
        return PREFIX + dateStr + randomStr;
    }
}
