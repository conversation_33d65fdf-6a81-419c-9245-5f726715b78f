package com.edu.www.utils;

import com.edu.www.excepitons.EduException;
import com.edu.www.excepitons.codes.BizErrorCode;
import com.edu.www.excepitons.Exception;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 校验工具 Util
 *
 * <AUTHOR>
 * @date 2024/09/07
 */
public class ValidateUtil {
    private static final ValidateUtil validate = new ValidateUtil();

    public static ValidateUtil validate(boolean flag, String message) {
        if (flag) throw new Exception(message);
        return validate;
    }


    /**
     * 参数校验方法
     *
     * @param expression 判断条件,不符合则报错
     * @param errorCode  错误类型
     * @param extMsg     错误附加信息
     */
    public static void paramValidate(boolean expression, BizErrorCode errorCode, String extMsg) {
        if (expression) {
            throw new EduException(errorCode, extMsg);
        }
    }

    /**
     * 参数校验方法
     *
     * @param expression 判断条件,不符合则报错
     * @param errorCode  错误类型
     */
    public static void paramValidate(boolean expression, BizErrorCode errorCode) {
        paramValidate(expression, errorCode, errorCode.getDesc());
    }

    public static void paramValidate(boolean expression, String message) {
        if (expression) throw new EduException(message, null);
    }

    /**
     * 参数校验方法
     * <p>判断字符串是否为空为null</>
     *
     * @param str       需要判断的字符串
     * @param errorCode 错误类型
     * @param extMsg    错误附加信息
     */
    public static void validateString(String str, BizErrorCode errorCode, String extMsg) {
        if (StringUtils.isBlank(str)) {
            throw new EduException(errorCode, extMsg);
        }
    }

    /**
     * 参数校验方法
     *
     * @param str
     * @param extMsg
     */
    public static void validateString(String str, String extMsg) {
        validateString(str, BizErrorCode.BLANK_IS_ILLEGAL_PARAM, extMsg);
    }

    /**
     * 检查一致性。
     *
     * @param request
     * @param response
     * @param attributeName
     */
    public static void checkConsistent(Object request, Object response, String attributeName) {
        ValidateUtil.paramValidate(!Objects.equals(request, response),
                BizErrorCode.REPEAT_REQ_INCONSISTENT,
                String.format("%s 是不一致的，信息是 %s, 数据库中信息是 %s", attributeName, request, response));
    }
}
