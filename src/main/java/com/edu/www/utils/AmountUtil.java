package com.edu.www.utils;


import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 金额工具
 */
public class AmountUtil {

    /**
     * 分摊算法：将总金额按总人数分摊，确保每人金额差距最小且精确到分
     *
     * @param totalAmount 总金额（元）
     * @param totalPeople 总人数
     * @return 每人分摊金额列表
     */
    public static List<BigDecimal> splitExpense(BigDecimal totalAmount, int totalPeople) {
        ValidateUtil.paramValidate(Objects.isNull(totalAmount) ||
                totalAmount.compareTo(BigDecimal.ZERO) < NumberUtils.INTEGER_ZERO, "总金额不能为空或负数");
        ValidateUtil.paramValidate(totalPeople <= NumberUtils.INTEGER_ZERO, "总人数必须大于0");

        List<BigDecimal> result = new ArrayList<>();

        // 将总金额转换为分（避免浮点数精度问题）
        BigDecimal totalCents = totalAmount.multiply(new BigDecimal("100"));

        // 计算每人平均分摊的分数（向下取整）
        BigDecimal avgCents = totalCents.divide(new BigDecimal(totalPeople), 0, RoundingMode.DOWN);

        // 计算余数（需要额外分配的分数）
        BigDecimal remainder = totalCents.subtract(avgCents.multiply(new BigDecimal(totalPeople)));

        // 分配金额
        for (int i = 0; i < totalPeople; i++) {
            BigDecimal personAmount = avgCents;

            // 如果还有余数，给前面的人多分配1分
            if (remainder.compareTo(BigDecimal.ZERO) > 0) {
                personAmount = personAmount.add(BigDecimal.ONE);
                remainder = remainder.subtract(BigDecimal.ONE);
            }

            // 转换回元（保留两位小数）
            BigDecimal amountInYuan = personAmount.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            result.add(amountInYuan);
        }

        return result;
    }
}
