package com.edu.www.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class StringUtil {
    /**
     * 将字符串的首字母转换为大写。
     *
     * @param str 待处理的字符串
     * @return 首字母大写后的字符串，如果输入为 null 或空，则返回原字符串
     */
    public static String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        // 获取第一个字符并转换为大写
        char firstChar = Character.toUpperCase(str.charAt(0));
        // 获取剩余部分字符串
        String restOfString = str.substring(1);
        // 拼接首字母和剩余部分
        return firstChar + restOfString;
    }

    /**
     * 生成文件ID
     *
     * @return 文件ID
     */
    public static String generateFileId() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return dateStr + "_" + uuid;
    }
}
