package com.edu.www.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.edu.www.enums.EduFileTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

/**
 * GitHub文件上传工具类
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Component
public class GitHubUploadUtil {

    private static final Logger logger = LoggerFactory.getLogger(GitHubUploadUtil.class);

    @Value("${auth.token.edu.file}")
    private String githubToken;

    @Value("${file.upload.cache.dir:./upload-cache}")
    private String localCacheDir;

    /**
     * 初始化缓存目录
     */
    @PostConstruct
    public void initCacheDir() {
        // 智能处理缓存目录路径
        localCacheDir = getSmartCacheDir(localCacheDir);
        logger.info("文件缓存目录初始化: {}", localCacheDir);

        // 确保缓存目录存在
        try {
            Path cacheDir = Paths.get(localCacheDir);
            if (!Files.exists(cacheDir)) {
                Files.createDirectories(cacheDir);
                logger.info("创建缓存目录成功: {}", localCacheDir);
            }
        } catch (Exception e) {
            logger.error("创建缓存目录失败: {}", localCacheDir, e);
        }
    }

    /**
     * 智能获取缓存目录
     */
    private String getSmartCacheDir(String configDir) {
        // 如果配置的是绝对路径，先检查是否可写
        if (configDir.startsWith("/") || configDir.matches("^[A-Za-z]:.*")) {
            try {
                Path testDir = Paths.get(configDir);
                // 尝试创建目录来测试是否可写
                Files.createDirectories(testDir);
                return configDir;
            } catch (Exception e) {
                logger.warn("配置的缓存目录不可写: {}, 错误: {}", configDir, e.getMessage());
            }
        }

        // 尝试几个备选方案
        String[] fallbackDirs = {
                "./upload-cache",                           // 项目根目录
                System.getProperty("user.home") + "/edu-file-cache",  // 用户目录
                System.getProperty("java.io.tmpdir") + "/edu-file-cache"  // 临时目录
        };

        for (String dir : fallbackDirs) {
            try {
                Path testDir = Paths.get(dir);
                Files.createDirectories(testDir);
                logger.info("使用缓存目录: {}", dir);
                return dir;
            } catch (Exception e) {
                logger.warn("缓存目录不可用: {}, 错误: {}", dir, e.getMessage());
            }
        }

        // 如果都失败了，使用相对路径
        logger.warn("所有缓存目录都不可用，使用默认相对路径: upload-cache");
        return "upload-cache";
    }

    // GitHub仓库信息
    private static final String GITHUB_OWNER = "RuneDance";
    private static final String GITHUB_REPO = "edu-file";
    private static final String GITHUB_API_URL = "https://api.github.com/repos/" + GITHUB_OWNER + "/" + GITHUB_REPO + "/contents/";
    private static final String GITHUB_RAW_URL = "https://raw.githubusercontent.com/" + GITHUB_OWNER + "/" + GITHUB_REPO + "/main/";

    /**
     * 上传文件到GitHub
     *
     * @param file 上传的文件
     * @return 文件访问路径或文件ID
     */
    public String uploadFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            logger.error("上传文件为空");
            return null;
        }

        try {
            // 1. 确定文件类型和目标文件夹
            String originalFileName = file.getOriginalFilename();
            EduFileTypeEnum fileType = EduFileTypeEnum.getByFileName(originalFileName);
            String targetFolder = fileType.getFolder();

            // 2. 生成唯一文件名
            String fileId = StringUtil.generateFileId();
            String fileExtension = FileUtil.getFileExtension(originalFileName);
            String fileName = fileId + "." + fileExtension;

            // 3. 构建GitHub路径
            String githubPath = targetFolder + "/" + fileName;

            // 4. 先保存到本地缓存
            String localPath = saveToLocalCache(file, targetFolder, fileName);

            // 5. 上传到GitHub
            boolean uploadSuccess = uploadToGitHub(file, githubPath, fileName);

            if (uploadSuccess) {
                logger.info("文件上传GitHub成功: {}", githubPath);
                return fileId; // 返回文件ID，便于后续管理
            } else {
                logger.warn("文件上传GitHub失败，使用本地缓存: {}", localPath);
                return fileId; // 即使GitHub上传失败，也返回文件ID，可以从本地缓存获取
            }

        } catch (Exception e) {
            logger.error("文件上传异常", e);
            return null;
        }
    }

    /**
     * 上传文件到GitHub仓库
     *
     * @param file       文件
     * @param githubPath GitHub路径
     * @param fileName   文件名
     * @return 是否成功
     */
    private boolean uploadToGitHub(MultipartFile file, String githubPath, String fileName) {
        try {
            // 1. 将文件转换为Base64
            byte[] fileBytes = file.getBytes();
            String base64Content = Base64.getEncoder().encodeToString(fileBytes);

            // 2. 构建请求URL
            String apiUrl = GITHUB_API_URL + githubPath;

            // 3. 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("message", "Upload file: " + fileName);
            requestBody.put("content", base64Content);

            // 4. 发送HTTP请求
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("PUT");
            connection.setRequestProperty("Authorization", "token " + githubToken);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            // 5. 写入请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toJSONString().getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 6. 检查响应
            int responseCode = connection.getResponseCode();
            if (responseCode == 201) {
                logger.info("文件上传GitHub成功: {}", githubPath);
                return true;
            } else {
                logger.error("GitHub上传失败，响应码: {}", responseCode);
                // 读取错误响应
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "utf-8"))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    logger.error("GitHub错误响应: {}", response.toString());
                }
                return false;
            }

        } catch (Exception e) {
            logger.error("上传到GitHub异常", e);
            return false;
        }
    }

    /**
     * 保存文件到本地缓存
     *
     * @param file         文件
     * @param targetFolder 目标文件夹
     * @param fileName     文件名
     * @return 本地文件路径
     */
    private String saveToLocalCache(MultipartFile file, String targetFolder, String fileName) {
        try {
            // 1. 创建本地缓存目录
            Path cacheDir = Paths.get(localCacheDir, targetFolder);
            Files.createDirectories(cacheDir);

            // 2. 保存文件
            Path filePath = cacheDir.resolve(fileName);
            Files.write(filePath, file.getBytes());

            String localPath = filePath.toString();
            logger.info("文件保存到本地缓存: {}", localPath);
            return localPath;

        } catch (Exception e) {
            logger.error("保存到本地缓存异常", e);
            return null;
        }
    }

    /**
     * 获取文件访问URL
     *
     * @param fileId 文件ID
     * @return 文件访问URL
     */
    public String getFileUrl(String fileId) {
        if (StringUtils.isBlank(fileId)) {
            return null;
        }

        try {
            // 1. 从本地缓存查找文件
            String localPath = findFileInLocalCache(fileId);
            if (localPath != null) {
                // 先尝试从GitHub获取
                String githubUrl = getGitHubFileUrl(fileId);
                if (isUrlAccessible(githubUrl)) {
                    return githubUrl;
                } else {
                    // GitHub不可访问，返回本地路径
                    return "/file/" + fileId;
                }
            }

            return null;

        } catch (Exception e) {
            logger.error("获取文件URL异常", e);
            return null;
        }
    }

    /**
     * 从本地缓存查找文件
     *
     * @param fileId 文件ID
     * @return 本地文件路径
     */
    private String findFileInLocalCache(String fileId) {
        try {
            Path cacheDir = Paths.get(localCacheDir);
            if (!Files.exists(cacheDir)) {
                return null;
            }

            // 遍历所有子目录查找文件
            return Files.walk(cacheDir)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().startsWith(fileId + "."))
                    .findFirst()
                    .map(Path::toString)
                    .orElse(null);

        } catch (Exception e) {
            logger.error("查找本地缓存文件异常", e);
            return null;
        }
    }

    /**
     * 获取GitHub文件URL
     *
     * @param fileId 文件ID
     * @return GitHub文件URL
     */
    private String getGitHubFileUrl(String fileId) {
        String localPath = findFileInLocalCache(fileId);
        if (localPath == null) {
            return null;
        }

        // 从本地路径推断GitHub路径
        String relativePath = localPath.replace(localCacheDir + File.separator, "").replace(File.separator, "/");
        return GITHUB_RAW_URL + relativePath;
    }

    /**
     * 检查URL是否可访问
     *
     * @param urlString URL字符串
     * @return 是否可访问
     */
    public static boolean isUrlAccessible(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            int responseCode = connection.getResponseCode();
            return responseCode == 200;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 删除GitHub上的文件
     *
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    public boolean deleteFileFromGitHub(String fileId) {
        try {
            // 1. 根据fileId找到本地文件路径
            String localPath = findFileInLocalCache(fileId);
            if (localPath == null) {
                logger.warn("找不到文件ID对应的本地文件: {}", fileId);
                return false;
            }

            // 2. 从本地路径推断GitHub路径
            String relativePath = localPath.replace(localCacheDir + File.separator, "").replace(File.separator, "/");
            String githubPath = relativePath;

            // 3. 获取文件的SHA值（删除文件需要SHA）
            String sha = getFileShaFromGitHub(githubPath);
            if (sha == null) {
                logger.warn("无法获取GitHub文件SHA: {}", githubPath);
                return false;
            }

            // 4. 删除GitHub文件
            return deleteFileFromGitHubWithSha(githubPath, sha);

        } catch (Exception e) {
            logger.error("删除GitHub文件异常: fileId={}", fileId, e);
            return false;
        }
    }

    /**
     * 从GitHub获取文件的SHA值
     *
     * @param githubPath GitHub文件路径
     * @return 文件SHA值
     */
    private String getFileShaFromGitHub(String githubPath) {
        try {
            // 构建API URL
            String apiUrl = GITHUB_API_URL + githubPath;

            // 发送GET请求获取文件信息
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Authorization", "token " + githubToken);
            connection.setRequestProperty("Accept", "application/vnd.github.v3+json");

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                // 读取响应
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }

                    // 解析JSON获取SHA
                    JSONObject jsonResponse = JSON.parseObject(response.toString());
                    return jsonResponse.getString("sha");
                }
            } else if (responseCode == 404) {
                logger.warn("GitHub文件不存在: {}", githubPath);
                return null;
            } else {
                logger.error("获取GitHub文件SHA失败，响应码: {}", responseCode);
                return null;
            }

        } catch (Exception e) {
            logger.error("获取GitHub文件SHA异常: {}", githubPath, e);
            return null;
        }
    }

    /**
     * 使用SHA删除GitHub文件
     *
     * @param githubPath GitHub文件路径
     * @param sha        文件SHA值
     * @return 是否删除成功
     */
    private boolean deleteFileFromGitHubWithSha(String githubPath, String sha) {
        try {
            // 构建API URL
            String apiUrl = GITHUB_API_URL + githubPath;

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("message", "Delete file: " + githubPath);
            requestBody.put("sha", sha);

            // 发送DELETE请求
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("DELETE");
            connection.setRequestProperty("Authorization", "token " + githubToken);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            // 写入请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toJSONString().getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 检查响应
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                logger.info("GitHub文件删除成功: {}", githubPath);
                return true;
            } else {
                logger.error("GitHub文件删除失败，响应码: {}", responseCode);
                // 读取错误响应
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "utf-8"))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    logger.error("GitHub删除错误响应: {}", response.toString());
                }
                return false;
            }

        } catch (Exception e) {
            logger.error("删除GitHub文件异常: {}", githubPath, e);
            return false;
        }
    }

    /**
     * 从GitHub下载文件并输出到响应
     *
     * @param githubUrl GitHub文件URL
     * @param fileId    文件ID
     * @param response  HTTP响应
     * @return 是否成功
     */
    public static boolean downloadAndOutputFromGitHub(String githubUrl, String fileId, HttpServletResponse response) {
        try {
            URL url = new URL(githubUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 设置响应头
                String contentType = connection.getContentType();
                if (StringUtils.isBlank(contentType)) {
                    contentType = FileUtil.getContentTypeFromUrl(githubUrl);
                }
                response.setContentType(contentType);

                int contentLength = connection.getContentLength();
                if (contentLength > 0) {
                    response.setContentLength(contentLength);
                }

                // 设置为内联显示（预览）
                response.setHeader("Content-Disposition", "inline; filename=\"" + fileId + "\"");

                // 设置缓存控制
                response.setHeader("Cache-Control", "public, max-age=3600");
                response.setDateHeader("Expires", System.currentTimeMillis() + 3600 * 1000);

                // 输出文件内容
                try (InputStream is = connection.getInputStream();
                     OutputStream os = response.getOutputStream()) {

                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = is.read(buffer)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                    os.flush();
                }

                return true;
            } else {
                logger.warn("GitHub文件访问失败: {}, 响应码: {}", githubUrl, responseCode);
                return false;
            }

        } catch (Exception e) {
            logger.error("从GitHub下载文件异常: {}", githubUrl, e);
            return false;
        }
    }

    /**
     * 根据fileId构建可能的GitHub URL
     *
     * @param fileId 文件ID
     * @return 可能的GitHub URL
     */
    public static String buildGitHubUrlFromFileId(String fileId) {
        // 尝试常见的文件类型和路径
        String[] commonExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp", "pdf", "txt"};
        String[] commonFolders = {"image", "file/pdf", "file/txt", "file/word", "file/excel", "video", "music"};

        for (String folder : commonFolders) {
            for (String ext : commonExtensions) {
                String possibleUrl = String.format("https://raw.githubusercontent.com/RuneDance/edu-file/master/%s/%s.%s",
                        folder, fileId, ext);
                if (isUrlAccessible(possibleUrl)) {
                    return possibleUrl;
                }
            }
        }
        return null;
    }
}
