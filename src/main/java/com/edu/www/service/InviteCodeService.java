package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.InviteCodeVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 邀请码管理 Service
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface InviteCodeService {

    /**
     * 根据ID查询邀请码
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询邀请码
     *
     * @param inviteCodeVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, InviteCodeVO inviteCodeVO);

    /**
     * 生成邀请码
     *
     * @return
     */
    ResponseEntity<Object> generate(HttpServletRequest request);

    /**
     * 修改邀请码
     *
     * @param inviteCodeVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, InviteCodeVO inviteCodeVO);

    /**
     * 删除邀请码
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 