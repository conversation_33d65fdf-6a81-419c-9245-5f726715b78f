package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.DutyVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 值日信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface DutyManageService {

    /**
     * 根据ID查询值日信息
     *
     * @param id 值日信息ID
     * @return 值日信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 查询基组信息
     *
     * @param request
     * @return
     */
    ResponseEntity<Object> getBaseGroup(HttpServletRequest request);

    /**
     * 分页查询值日信息
     *
     * @param dutyVO 查询条件
     * @return 值日信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, DutyVO dutyVO);

    /**
     * 新增值日信息
     *
     * @param dutyVO 值日信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, DutyVO dutyVO);

    /**
     * 修改值日信息
     *
     * @param dutyVO 值日信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, DutyVO dutyVO);

    /**
     * 根据ID删除值日信息
     *
     * @param id 值日信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 批量删除值日信息
     *
     * @param request
     * @param ids
     * @return
     */
    ResponseEntity<Object> batchDelete(HttpServletRequest request, List<String> ids);
}
