package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.PurchaseContractVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购合同管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface PurchaseContractService {

    /**
     * 根据ID查询采购合同信息
     *
     * @param id 采购合同信息ID
     * @return 采购合同信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 查询采购合同信息
     *
     * @return
     */
    ResponseEntity<Object> getPurchaseContract();

    /**
     * 分页查询采购合同信息
     *
     * @param purchaseContractVO 查询条件
     * @return 采购合同信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, PurchaseContractVO purchaseContractVO);

    /**
     * 新增采购合同信息
     *
     * @param purchaseContractVO 采购合同信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, PurchaseContractVO purchaseContractVO);

    /**
     * 修改采购合同信息
     *
     * @param purchaseContractVO 采购合同信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, PurchaseContractVO purchaseContractVO);

    /**
     * 删除采购合同信息
     *
     * @param id 采购合同信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
