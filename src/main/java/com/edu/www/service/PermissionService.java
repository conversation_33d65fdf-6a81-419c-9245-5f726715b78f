package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.PermissionVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 权限信息管理服务接口
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public interface PermissionService {

    /**
     * 根据ID查询权限信息
     *
     * @param id 权限ID
     * @return 权限信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询权限信息
     *
     * @param permissionVO 查询条件
     * @return 权限列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, PermissionVO permissionVO);

    /**
     * 分页查询权限信息
     *
     * @param permissionVO 查询条件
     * @return 权限列表
     */
    ResponseEntity<Object> queryByPage(HttpServletRequest request, PermissionVO permissionVO);

    /**
     * 根据菜单ID查询权限列表
     *
     * @param menuId 菜单ID
     * @return 权限列表
     */
    ResponseEntity<Object> queryByMenuId(String menuId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    ResponseEntity<Object> queryByUserId(HttpServletRequest request, String userId);

    /**
     * 根据用户ID查询按钮权限列表
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    ResponseEntity<Object> queryButtonPermissionsByUserId(String userId);

    /**
     * 校验用户是否有指定权限
     *
     * @param userId         用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(String userId, String permissionCode);

    /**
     * 校验用户是否有指定权限列表中的任意一个
     *
     * @param userId          用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否有权限
     */
    boolean hasAnyPermission(String userId, List<String> permissionCodes);

    /**
     * 校验用户是否有指定权限列表中的所有权限
     *
     * @param userId          用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否有权限
     */
    boolean hasAllPermissions(String userId, List<String> permissionCodes);

    /**
     * 新增权限
     *
     * @param permissionVO 权限信息
     * @return 结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, PermissionVO permissionVO);

    /**
     * 更新权限
     *
     * @param permissionVO 权限信息
     * @return 结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, PermissionVO permissionVO);

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 