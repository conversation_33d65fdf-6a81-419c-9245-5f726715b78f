package com.edu.www.service;

import com.edu.www.common.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 值日信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface DutyService {
    /**
     * 查询三个月的值日数据(上月、本月、下月)
     *
     * @param request
     * @return
     */
    ResponseEntity<Object> query(HttpServletRequest request);

    /**
     * 查询三个月的值日数据详情(带ID格式)
     *
     * @param request
     * @return
     */
    ResponseEntity<Object> detail(HttpServletRequest request);

    /**
     * 预览自动填充结果
     *
     * @param request
     * @return
     */
    ResponseEntity<Object> previewAutoFill(HttpServletRequest request);

    /**
     * 自动填充下个月值日数据
     *
     * @param request
     * @return
     */
    ResponseEntity<Object> autoFillNextMonth(HttpServletRequest request);

    /**
     * 导出值日信息到Excel
     *
     * @param request
     * @param response
     * @return
     */
    ResponseEntity<Object> exportDutyInfoToExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 置换表格内容信息
     *
     * @param masterId
     * @param slaveId
     * @return
     */
    ResponseEntity<Object> swap(HttpServletRequest request, String masterId, String slaveId);

    /**
     * 统计值日信息
     *
     * @param request
     * @param startDate
     * @param endDate
     * @return
     */
    ResponseEntity<Object> statistic(HttpServletRequest request, String startDate, String endDate);

    /**
     * 导出值日统计信息到Excel
     *
     * @param request
     * @param response
     * @param startDate
     * @param endDate
     * @return
     */
    ResponseEntity<Object> exportStatisticToExcel(HttpServletRequest request, HttpServletResponse response, String startDate, String endDate);
}
