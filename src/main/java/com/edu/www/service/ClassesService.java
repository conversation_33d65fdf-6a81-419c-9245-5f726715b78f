package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.ClassesVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 班级信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface ClassesService {
    /**
     * 根据ID查询班级信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 根据部门编号查询所有班级信息
     *
     * @param deptCode
     * @return
     */
    ResponseEntity<Object> getAllClasses(String deptCode);

    /**
     * 分页查询班级信息
     *
     * @param classesVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, ClassesVO classesVO);

    /**
     * 新增班级信息
     *
     * @param classesVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, ClassesVO classesVO);

    /**
     * 修改班级信息
     *
     * @param classesVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, ClassesVO classesVO);

    /**
     * 根据ID删除班级信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 