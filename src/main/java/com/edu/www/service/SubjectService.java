package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.SubjectVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 学科信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface SubjectService {
    /**
     * 根据ID查询学科信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询学科信息
     *
     * @param subjectVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, SubjectVO subjectVO);

    /**
     * 新增学科信息
     *
     * @param subjectVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, SubjectVO subjectVO);

    /**
     * 修改学科信息
     *
     * @param subjectVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, SubjectVO subjectVO);

    /**
     * 根据ID删除学科信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 