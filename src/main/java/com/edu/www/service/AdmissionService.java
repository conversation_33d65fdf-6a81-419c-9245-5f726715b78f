package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.AdmissionVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 招生信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface AdmissionService {

    /**
     * 根据ID查询招生信息
     *
     * @param id 招生信息ID
     * @return 招生信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询招生信息
     *
     * @param admissionVO 查询条件
     * @return 招生信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, AdmissionVO admissionVO);

    /**
     * 新增招生信息
     *
     * @param admissionVO 招生信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, AdmissionVO admissionVO);

    /**
     * 修改招生信息
     *
     * @param admissionVO 招生信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, AdmissionVO admissionVO);

    /**
     * 根据ID删除招生信息
     *
     * @param id 招生信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 