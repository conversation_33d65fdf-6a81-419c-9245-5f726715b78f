package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.TmpScheduleVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 临时课程表信息管理
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public interface TmpScheduleService {

    /**
     * 根据ID查询临时课程表信息
     *
     * @param id 临时课程表ID
     * @return 临时课程表信息
     */
    ResponseEntity<Object> get(String id);

    /**
     * 查询临时课程表列表信息
     *
     * @param tmpScheduleVO
     * @return
     */
    ResponseEntity<Object> query(TmpScheduleVO tmpScheduleVO);

    /**
     * 新增临时课程表信息
     *
     * @param tmpScheduleVO 临时课程表信息
     * @return 操作结果
     */
    ResponseEntity<Object> insert(TmpScheduleVO tmpScheduleVO);

    /**
     * 修改临时课程表信息
     *
     * @param tmpScheduleVO 临时课程表信息
     * @return 操作结果
     */
    ResponseEntity<Object> update(TmpScheduleVO tmpScheduleVO);

    /**
     * 批量修改临时课程表信息
     *
     * @param tmpScheduleVOList 临时课程表信息列表
     * @return 操作结果
     */
    ResponseEntity<Object> batchUpdate(List<TmpScheduleVO> tmpScheduleVOList);

    /**
     * 置换临时课程表表格内容信息
     *
     * @param masterId
     * @param slaveId
     * @return
     */
    ResponseEntity<Object> swap(String masterId, String slaveId);

    /**
     * 根据ID删除临时课程表信息
     *
     * @param id 临时课程表ID
     * @return 操作结果
     */
    ResponseEntity<Object> delete(String id);
} 