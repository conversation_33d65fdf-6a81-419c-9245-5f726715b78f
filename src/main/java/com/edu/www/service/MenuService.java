package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.MenuVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 菜单信息管理服务接口
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public interface MenuService {
    
    /**
     * 根据ID查询菜单信息
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);
    
    /**
     * 分页查询菜单信息
     *
     * @param menuVO 查询条件
     * @return 菜单列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, MenuVO menuVO);
    
    /**
     * 查询菜单树结构
     *
     * @return 菜单树
     */
    ResponseEntity<Object> queryMenuTree();
    
    /**
     * 根据用户ID查询菜单树结构
     *
     * @param userId 用户ID
     * @return 菜单树
     */
    ResponseEntity<Object> queryMenuTreeByUserId(String userId);
    
    /**
     * 新增菜单
     *
     * @param menuVO 菜单信息
     * @return 结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, MenuVO menuVO);
    
    /**
     * 更新菜单
     *
     * @param menuVO 菜单信息
     * @return 结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, MenuVO menuVO);
    
    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 