package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.ExamVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 考试信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface ExamService {

    /**
     * 根据ID查询考试信息
     *
     * @param id 考试信息ID
     * @return 考试信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询考试信息
     *
     * @param examVO 查询条件
     * @return 考试信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, ExamVO examVO);

    /**
     * 新增考试信息
     *
     * @param examVO 考试信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, ExamVO examVO);

    /**
     * 修改考试信息
     *
     * @param examVO 考试信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, ExamVO examVO);

    /**
     * 根据ID删除考试信息
     *
     * @param id 考试信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 生成打印标签贴
     *
     * @param request
     * @param ids
     * @return
     */
    ResponseEntity<Object> examTag(HttpServletRequest request, List<String> ids);

    /**
     * 监考时长汇总
     *
     * @param request
     * @param examVO
     * @return
     */
    ResponseEntity<Object> duration(HttpServletRequest request, ExamVO examVO);

    /**
     * 监考时长详情
     *
     * @param request
     * @param invigilatorId
     * @return
     */
    ResponseEntity<Object> durationDetail(HttpServletRequest request, String invigilatorId);
}
