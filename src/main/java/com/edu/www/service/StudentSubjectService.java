package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.StudentSubjectVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 学生学科管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface StudentSubjectService {
    /**
     * 根据ID查询学生学科信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, StudentSubjectVO studentSubjectVO);

    /**
     * 新增学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, StudentSubjectVO studentSubjectVO);

    /**
     * 修改学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, StudentSubjectVO studentSubjectVO);

    /**
     * 根据ID删除学生学科信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 