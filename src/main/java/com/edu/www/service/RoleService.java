package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.RoleVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * 角色信息管理服务接口
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public interface RoleService {

    /**
     * 根据ID查询角色信息
     *
     * @param id 角色ID
     * @return 角色信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 获取所有角色信息
     *
     * @return
     */
    ResponseEntity<Object> getRoleList();

    /**
     * 分页查询角色信息
     *
     * @param roleVO 查询条件
     * @return 角色列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, RoleVO roleVO);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    ResponseEntity<Object> queryByUserId(HttpServletRequest request, String userId);

    /**
     * 新增角色
     *
     * @param roleVO 角色信息
     * @return 结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, RoleVO roleVO);

    /**
     * 更新角色
     *
     * @param roleVO 角色信息
     * @return 结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, RoleVO roleVO);

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 为角色分配权限
     *
     * @param roleId        角色ID
     * @param permissionIds 权限ID列表
     * @return 结果
     */
    ResponseEntity<Object> assignPermissions(HttpServletRequest request, String roleId, Set<String> permissionIds);

    /**
     * 查询角色的权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    ResponseEntity<Object> queryRolePermissions(HttpServletRequest request, String roleId);
} 