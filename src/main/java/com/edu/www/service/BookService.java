package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.BookVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 书籍信息管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface BookService {

    /**
     * 根据ID查询书籍信息
     *
     * @param id 书籍信息ID
     * @return 书籍信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询书籍信息
     *
     * @param bookVO 查询条件
     * @return 书籍信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, BookVO bookVO);

    /**
     * 新增书籍信息
     *
     * @param bookVO 书籍信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, BookVO bookVO);

    /**
     * 修改书籍信息
     *
     * @param bookVO 书籍信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, BookVO bookVO);

    /**
     * 删除书籍信息
     *
     * @param id 书籍信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
