package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.StudentVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 学生信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface StudentService {
    /**
     * 根据ID查询学生信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询学生信息
     *
     * @param studentVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, StudentVO studentVO);

    /**
     * 查询所有学生信息
     *
     * @return 所有学生信息列表
     */
    ResponseEntity<Object> queryAll();

    /**
     * 新增学生信息
     *
     * @param studentVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, StudentVO studentVO);

    /**
     * 修改学生信息
     *
     * @param studentVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, StudentVO studentVO);

    /**
     * 根据ID删除学生信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 生成在读证明文档
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> generateReadingCertificate(HttpServletRequest request, String id);

    /**
     * 生成多学生在读证明
     *
     * @param ids
     * @return
     */
    ResponseEntity<Object> generateGroupReadingCertificate(HttpServletRequest request, List<String> ids);

    /**
     * 随机分组
     *
     * @param groupNum
     * @return
     */
    ResponseEntity<Object> randomGroup(Integer groupNum);

    /**
     * 同步学生信息（支持ManageBac API参数）
     *
     * @param request          HTTP请求
     * @param studentIds       学生ID列表（多个，如：["AA1001", "BB2002"]）
     * @param yearGroupId      年级组ID（单个）
     * @return 同步结果
     */
    ResponseEntity<Object> sync(HttpServletRequest request, List<String> studentIds, Long yearGroupId);
}
