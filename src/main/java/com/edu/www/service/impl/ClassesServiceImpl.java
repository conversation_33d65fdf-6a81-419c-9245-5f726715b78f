package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.ClassesDTO;
import com.edu.www.dto.TeacherDTO;
import com.edu.www.enums.*;
import com.edu.www.mapper.ClassesMapper;
import com.edu.www.service.ClassesService;
import com.edu.www.service.CommonService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.ClassesVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 班级信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class ClassesServiceImpl implements ClassesService {
    private static final Logger logger = LoggerFactory.getLogger(ClassesService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private ClassesMapper classesMapper;

    /**
     * 根据ID查询班级信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "班级ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        ClassesVO classesVO = classesMapper.getById(id);
        // 翻译枚举值为可读文本
        if (classesVO != null) {
            if (StringUtils.isNotBlank(classesVO.getDepartmentCode())) {
                EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(classesVO.getDepartmentCode());
                if (deptEnum != null) {
                    classesVO.setDepartmentCode(deptEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(classesVO.getGradeCode())) {
                EduGradeCodeEnum gradeEnum = EduGradeCodeEnum.getByKey(classesVO.getGradeCode());
                if (gradeEnum != null) {
                    classesVO.setGradeName(gradeEnum.getDesc());
                }
            }
        }

        responseEntity.setData(classesVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询班级信息成功");
        return responseEntity;
    }

    /**
     * 根据部门编号查询所有班级信息
     *
     * @return
     */
    @Override
    public ResponseEntity<Object> getAllClasses(String deptCode) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        ClassesDTO classesDTO = new ClassesDTO();
        classesDTO.setDepartmentCode(deptCode);
        responseEntity.setData(classesMapper.query(classesDTO));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("根据部门编号查询所有班级信息成功");
        return responseEntity;
    }

    /**
     * 分页查询班级信息
     *
     * @param classesVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, ClassesVO classesVO) {
        logger.info("分页查询班级信息入参:{}", JSON.toJSONString(classesVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }
        PageHelper.startPage(classesVO.getPageStart(), classesVO.getPageSize());
        ClassesDTO classesDTO = new ClassesDTO();
        BeanUtils.copyProperties(classesVO, classesDTO);
        List<ClassesVO> classesVOList = classesMapper.query(classesDTO);
        // 翻译枚举值为可读文本
        if (classesVOList != null && !classesVOList.isEmpty()) {
            for (ClassesVO vo : classesVOList) {
                if (StringUtils.isNotBlank(vo.getCode())) {
                    EduClassCodeEnum classCodeEnum = EduClassCodeEnum.getByKey(vo.getCode());
                    if (classCodeEnum != null) {
                        vo.setName(classCodeEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getDepartmentCode())) {
                    EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(vo.getDepartmentCode());
                    if (deptEnum != null) {
                        vo.setDepartment(deptEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getGradeCode())) {
                    EduGradeCodeEnum gradeEnum = EduGradeCodeEnum.getByKey(vo.getGradeCode());
                    if (gradeEnum != null) {
                        vo.setGradeName(gradeEnum.getDesc());
                    }
                }
            }
        }

        PageInfo<ClassesVO> pageInfo = new PageInfo(classesVOList);

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询班级信息成功");
        return responseEntity;
    }

    /**
     * 新增班级信息
     *
     * @param classesVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, ClassesVO classesVO) {
        logger.info("新增班级信息入参:{}", JSON.toJSONString(classesVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(classesVO.getDescription()) &&
                classesVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }
        ClassesDTO classesDTO = new ClassesDTO();
        BeanUtils.copyProperties(classesVO, classesDTO);
        if (StringUtils.isNotBlank(classesVO.getCode())) {
            EduClassCodeEnum classCodeEnum = EduClassCodeEnum.getByKey(classesVO.getCode());
            if (classCodeEnum != null) {
                classesVO.setName(classCodeEnum.getDesc());
            }
        }
        String userName = RequestMsgUtil.getSessionUserName();
        classesDTO.setCreatedBy(userName);
        classesDTO.setUpdatedBy(userName);
        classesMapper.insert(classesDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增班级信息成功");
        return responseEntity;
    }

    /**
     * 修改班级信息
     *
     * @param classesVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, ClassesVO classesVO) {
        logger.info("修改班级信息入参:{}", JSON.toJSONString(classesVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(classesVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(classesVO.getDescription()) &&
                classesVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }
        ClassesDTO classesDTO = new ClassesDTO();
        BeanUtils.copyProperties(classesVO, classesDTO);
        String userName = RequestMsgUtil.getSessionUserName();
        classesDTO.setUpdatedBy(userName);
        classesMapper.update(classesDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改班级信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除班级信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("班级信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "班级ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }
        classesMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除班级信息成功");
        return responseEntity;
    }
} 