package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.PurchaseOrderDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.mapper.PurchaseOrderMapper;
import com.edu.www.service.PurchaseOrderService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.UniqueCodeUtil;
import com.edu.www.vo.PurchaseOrderVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 采购订单管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class PurchaseOrderServiceImpl implements PurchaseOrderService {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderService.class);

    @Autowired
    private PurchaseOrderMapper purchaseOrderMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询采购订单信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购订单ID不能为空");
                return responseEntity;
            }

            // 查询采购订单信息
            PurchaseOrderVO purchaseOrderVO = purchaseOrderMapper.get(id);
            if (purchaseOrderVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购订单信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(purchaseOrderVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询采购订单信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 根据部门编码查询采购订单信息
     *
     * @param departmentCode
     * @return
     */
    @Override
    public ResponseEntity<Object> getByDepartmentCode(String departmentCode) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        PurchaseOrderDTO purchaseOrderDTO = new PurchaseOrderDTO();
        if (StringUtils.isNotBlank(departmentCode)) {
            purchaseOrderDTO.setDepartmentCode(departmentCode);
        }
        List<PurchaseOrderVO> purchaseOrderList = purchaseOrderMapper.query(purchaseOrderDTO);
        responseEntity.setData(purchaseOrderList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("根据部门编码查询采购订单信息成功");
        return responseEntity;
    }

    /**
     * 生成采购订单号
     *
     * @return
     */
    @Override
    public ResponseEntity<Object> generatePurchaseOrderNo() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 生成供应商编码，确保唯一性
            String purchaseOrderNo = UniqueCodeUtil.generatePurchaseOrderNo();
            Integer count = purchaseOrderMapper.getByCodeCount(purchaseOrderNo);

            // 如果编码已存在，重新生成（最多尝试10次）
            int attempts = 0;
            while (count != null && count > 0 && attempts < 10) {
                purchaseOrderNo = UniqueCodeUtil.generatePurchaseOrderNo();
                count = purchaseOrderMapper.getByCodeCount(purchaseOrderNo);
                attempts++;
            }

            if (count != null && count > 0) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("生成编码失败，请重试");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(purchaseOrderNo);
            responseEntity.setMsg("生成采购订单号成功");

        } catch (Exception e) {
            logger.error("生成采购订单号异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("生成编码失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, PurchaseOrderVO purchaseOrderVO) {
        logger.info("分页查询采购订单信息入参:{}", JSON.toJSONString(purchaseOrderVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 设置分页参数
            PageHelper.startPage(purchaseOrderVO.getPageStart(), purchaseOrderVO.getPageSize());

            // 转换为DTO
            PurchaseOrderDTO purchaseOrderDTO = new PurchaseOrderDTO();
            BeanUtils.copyProperties(purchaseOrderVO, purchaseOrderDTO);

            // 查询数据
            List<PurchaseOrderVO> purchaseOrderList = purchaseOrderMapper.query(purchaseOrderDTO);
            PageInfo<PurchaseOrderVO> pageInfo = new PageInfo<>(purchaseOrderList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询采购订单信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, PurchaseOrderVO purchaseOrderVO) {
        logger.info("新增采购订单信息入参:{}", JSON.toJSONString(purchaseOrderVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePurchaseOrder(purchaseOrderVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 转换为DTO
            PurchaseOrderDTO purchaseOrderDTO = new PurchaseOrderDTO();
            BeanUtils.copyProperties(purchaseOrderVO, purchaseOrderDTO);

            EduDeptCodeEnum deptCodeEnum = EduDeptCodeEnum.getByKey(purchaseOrderDTO.getDepartmentCode());
            if (deptCodeEnum != null) {
                purchaseOrderDTO.setDepartment(deptCodeEnum.getDesc());
            }

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            purchaseOrderDTO.setCreatedBy(currentUser);
            purchaseOrderDTO.setUpdatedBy(currentUser);

            // 新增采购订单信息
            purchaseOrderMapper.insert(purchaseOrderDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增采购订单信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, PurchaseOrderVO purchaseOrderVO) {
        logger.info("修改采购订单信息入参:{}", JSON.toJSONString(purchaseOrderVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePurchaseOrder(purchaseOrderVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 检查采购订单是否存在
            PurchaseOrderVO existPurchaseOrder = purchaseOrderMapper.get(purchaseOrderVO.getId());
            if (existPurchaseOrder == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购订单信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            PurchaseOrderDTO purchaseOrderDTO = new PurchaseOrderDTO();
            BeanUtils.copyProperties(purchaseOrderVO, purchaseOrderDTO);

            EduDeptCodeEnum deptCodeEnum = EduDeptCodeEnum.getByKey(purchaseOrderDTO.getDepartmentCode());
            if (deptCodeEnum != null) {
                purchaseOrderDTO.setDepartment(deptCodeEnum.getDesc());
            }

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            purchaseOrderDTO.setUpdatedBy(currentUser);

            // 修改采购订单信息
            purchaseOrderMapper.update(purchaseOrderDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改采购订单信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除采购订单信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购订单ID不能为空");
                return responseEntity;
            }

            // 检查采购订单是否存在
            PurchaseOrderVO existPurchaseOrder = purchaseOrderMapper.get(id);
            if (existPurchaseOrder == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购订单信息不存在");
                return responseEntity;
            }

            // 删除采购订单信息
            purchaseOrderMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除采购订单信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证采购订单信息
     */
    private String validatePurchaseOrder(PurchaseOrderVO purchaseOrderVO, boolean isUpdate) {
        if (purchaseOrderVO == null) {
            return "采购订单信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(purchaseOrderVO.getId())) {
            return "采购订单ID不能为空";
        }

        if (StringUtils.isBlank(purchaseOrderVO.getDepartmentCode())) {
            return "部门编码不能为空";
        }

        if (StringUtils.isBlank(purchaseOrderVO.getYear())) {
            return "年度不能为空";
        }

        if (StringUtils.isBlank(purchaseOrderVO.getPurchaseNo())) {
            return "采购单号不能为空";
        }

        if (StringUtils.isBlank(purchaseOrderVO.getRequesterName())) {
            return "申请人姓名不能为空";
        }

        return null;
    }
}
