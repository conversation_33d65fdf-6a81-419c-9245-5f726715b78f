package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.ClassroomDTO;
import com.edu.www.enums.EduBuildingEnum;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduFloorEnum;
import com.edu.www.mapper.ClassroomMapper;
import com.edu.www.service.ClassroomService;
import com.edu.www.service.CommonService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.ClassroomVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 教室信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class ClassroomServiceImpl implements ClassroomService {
    private static final Logger logger = LoggerFactory.getLogger(ClassroomService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private ClassroomMapper classroomMapper;

    /**
     * 根据ID查询教室信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询教室信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "教室信息ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }
        ClassroomVO classroomVO = classroomMapper.get(id);
        // 翻译建筑枚举
        if (classroomVO != null && StringUtils.isNotBlank(classroomVO.getBuildingCode())) {
            EduBuildingEnum buildingEnum = EduBuildingEnum.getByKey(classroomVO.getBuildingCode());
            if (buildingEnum != null) {
                classroomVO.setBuildingName(buildingEnum.getDesc());
            }
        }

        responseEntity.setData(classroomVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询教室信息成功");
        return responseEntity;
    }

    /**
     * 分页查询教室信息
     *
     * @param classroomVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, ClassroomVO classroomVO) {
        logger.info("分页查询教室信息入参:{}", JSON.toJSONString(classroomVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(classroomVO.getPageStart(), classroomVO.getPageSize());

        ClassroomDTO classroomDTO = new ClassroomDTO();
        BeanUtils.copyProperties(classroomVO, classroomDTO);

        List<ClassroomVO> classroomVOList = classroomMapper.query(classroomDTO);

        // 翻译建筑枚举
        if (classroomVOList != null && !classroomVOList.isEmpty()) {
            for (ClassroomVO vo : classroomVOList) {
                if (StringUtils.isNotBlank(vo.getBuildingCode())) {
                    EduBuildingEnum buildingEnum = EduBuildingEnum.getByKey(vo.getBuildingCode());
                    if (buildingEnum != null) {
                        vo.setBuildingName(buildingEnum.getDesc());
                    }
                }
            }
        }

        PageInfo<ClassroomVO> pageInfo = new PageInfo<>(classroomVOList);
        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询教室信息成功");
        return responseEntity;
    }

    /**
     * 新增教室信息
     *
     * @param classroomVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, ClassroomVO classroomVO) {
        logger.info("新增教室信息入参:{}", JSON.toJSONString(classroomVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(classroomVO.getDescription()) &&
                classroomVO.getDescription().length() > 255, "备注长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        ClassroomDTO classroomDTO = new ClassroomDTO();
        BeanUtils.copyProperties(classroomVO, classroomDTO);

        // 如果提供了楼号但没有楼名，自动根据枚举获取楼名
        if (StringUtils.isNotBlank(classroomDTO.getBuildingCode()) &&
                StringUtils.isBlank(classroomDTO.getBuildingName())) {
            EduBuildingEnum buildingEnum = EduBuildingEnum.getByKey(classroomDTO.getBuildingCode());
            if (buildingEnum != null) {
                classroomDTO.setBuildingName(buildingEnum.getDesc());
            }
        }

        if (StringUtils.isNotBlank(classroomDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(classroomDTO.getDepartmentCode());
            if (deptEnum != null) {
                classroomDTO.setDepartment(deptEnum.getDesc());

            }
        }
        if (StringUtils.isBlank(classroomDTO.getFloor())) {
            classroomDTO.setFloor(String.valueOf(classroomDTO.getCode().charAt(1)));
        }
        String userName = RequestMsgUtil.getSessionUserName();
        classroomDTO.setCreatedBy(userName);
        classroomDTO.setUpdatedBy(userName);

        classroomMapper.insert(classroomDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增教室信息成功");
        return responseEntity;
    }

    /**
     * 修改教室信息
     *
     * @param classroomVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, ClassroomVO classroomVO) {
        logger.info("修改教室信息入参:{}", JSON.toJSONString(classroomVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(classroomVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(classroomVO.getDescription()) &&
                classroomVO.getDescription().length() > 255, "备注长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        ClassroomDTO classroomDTO = new ClassroomDTO();
        BeanUtils.copyProperties(classroomVO, classroomDTO);

        // 如果提供了楼号但没有楼名，自动根据枚举获取楼名
        if (StringUtils.isNotBlank(classroomDTO.getBuildingCode()) &&
                StringUtils.isBlank(classroomDTO.getBuildingName())) {
            EduBuildingEnum buildingEnum = EduBuildingEnum.getByKey(classroomDTO.getBuildingCode());
            if (buildingEnum != null) {
                classroomDTO.setBuildingName(buildingEnum.getDesc());
            }
        }

        if (StringUtils.isNotBlank(classroomDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(classroomDTO.getDepartmentCode());
            if (deptEnum != null) {
                classroomDTO.setDepartment(deptEnum.getDesc());

            }
        }

        if (StringUtils.isBlank(classroomDTO.getFloor())) {
            classroomDTO.setFloor(String.valueOf(classroomDTO.getCode().charAt(1)));
        }
        String userName = RequestMsgUtil.getSessionUserName();
        classroomDTO.setUpdatedBy(userName);
        classroomDTO.setUpdatedAt(new Date());

        classroomMapper.update(classroomDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改教室信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除教室信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("教室信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "教室信息ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }
        classroomMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除教室信息成功");
        return responseEntity;
    }

    /**
     * 根据部门编号查询教室信息
     *
     * @param deptCode 部门编号(SY：双语部、IC：融合部、DP：高中部)
     * @return 教室信息列表
     */
    @Override
    public ResponseEntity<Object> getClassroomByDeptCode(String deptCode) {
        logger.info("根据部门编号查询教室信息，deptCode：{}", deptCode);

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        ClassroomDTO classroomDTO = new ClassroomDTO();
        classroomDTO.setDepartmentCode(deptCode);

        List<ClassroomVO> classroomVOList = classroomMapper.query(classroomDTO);

        // 翻译建筑枚举和部门枚举
        if (classroomVOList != null && !classroomVOList.isEmpty()) {
            for (ClassroomVO vo : classroomVOList) {
                // 翻译建筑枚举
                if (StringUtils.isNotBlank(vo.getBuildingCode())) {
                    EduBuildingEnum buildingEnum = EduBuildingEnum.getByKey(vo.getBuildingCode());
                    if (buildingEnum != null) {
                        vo.setBuildingName(buildingEnum.getDesc());
                    }
                }

                // 翻译楼层枚举
                if (StringUtils.isNotBlank(vo.getFloor())) {
                    EduFloorEnum floorEnum = EduFloorEnum.getByKey(vo.getFloor());
                    if (floorEnum != null) {
                        // 这里可以根据需要设置楼层描述，或者保持原值
                        vo.setFloor(floorEnum.getDesc());
                    }
                }

                // 翻译部门枚举
                if (StringUtils.isNotBlank(vo.getDepartmentCode())) {
                    EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(vo.getDepartmentCode());
                    if (deptEnum != null) {
                        vo.setDepartment(deptEnum.getDesc());
                    }
                }
            }
        }

        responseEntity.setData(classroomVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("根据部门编号查询教室信息成功");
        return responseEntity;
    }
}