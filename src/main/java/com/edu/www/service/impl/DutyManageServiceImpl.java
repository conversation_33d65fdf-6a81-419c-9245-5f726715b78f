package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.dto.DutyDTO;
import com.edu.www.enums.EduDPDutyTypeEnum;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduWeekdayEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.DutyMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.DutyManageService;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.DutyVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 值日信息管理 Service实现
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class DutyManageServiceImpl implements DutyManageService {

    private static final Logger logger = LoggerFactory.getLogger(DutyManageServiceImpl.class);

    @Autowired
    private DutyMapper dutyMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询值日信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询值日信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "值日信息ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        responseEntity.setData(dutyMapper.get(id));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询值日信息成功");
        return responseEntity;
    }

    /**
     * 查询基组信息
     *
     * @param request
     * @return
     */
    @Override
    public ResponseEntity<Object> getBaseGroup(HttpServletRequest request) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        DutyDTO dutyDTO = new DutyDTO();
        dutyDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        dutyDTO.setIsBaseGroup(EduYesOrNoEnum.YES.getKey());
        List<DutyVO> dutyVOList = dutyMapper.query(dutyDTO);

        if (CollectionUtils.isEmpty(dutyVOList)) {
            responseEntity.setData(new HashMap<>());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("查询基组信息成功");
            return responseEntity;
        }

        List<String> list = Arrays.asList(EduDPDutyTypeEnum.YEAR_10_AB.getKey(),
                EduDPDutyTypeEnum.YEAR_11_AB.getKey(), EduDPDutyTypeEnum.YEAR_12_SPRING.getKey());
        // 按基组编码分组
        Map<String, List<DutyVO>> groupMap = dutyVOList.stream()
                .filter(duty -> list.contains(duty.getDutyType()))
                .collect(Collectors.groupingBy(DutyVO::getBaseGroupCode));

        // 构建返回格式
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        for (Map.Entry<String, List<DutyVO>> entry : groupMap.entrySet()) {
            String baseGroupCode = entry.getKey();
            String weekKey = Constant.BASE_GROUP_PREFIX + baseGroupCode;
            Map<String, String> weekData = new LinkedHashMap<>();

            for (DutyVO duty : entry.getValue()) {
                // 构建key: 值日类型描述_星期_示例日期
                String key = buildKey(duty);
                weekData.put(key, duty.getTeacherName());
            }

            result.put(weekKey, weekData);
        }

        responseEntity.setData(result);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询基组信息成功");
        return responseEntity;
    }

    /**
     * 构建基组数据的key
     */
    private String buildKey(DutyVO duty) {
        // 值日类型描述
        String dutyTypeDesc = "";
        EduDPDutyTypeEnum dutyTypeEnum = EduDPDutyTypeEnum.getByKey(duty.getDutyType());
        if (dutyTypeEnum != null) {
            dutyTypeDesc = dutyTypeEnum.getDesc() + Constant.HYPHEN + dutyTypeEnum.getTimeDesc();
        }

        // 星期描述
        String weekdayDesc = "";
        EduWeekdayEnum weekdayEnum = EduWeekdayEnum.getByKey(duty.getWeekday());
        if (weekdayEnum != null) {
            weekdayDesc = String.valueOf(weekdayEnum.getDesc().charAt(1));
        }

        return dutyTypeDesc + Constant.UNDERSCORE + weekdayDesc;
    }

    /**
     * 分页查询值日信息
     *
     * @param dutyVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, DutyVO dutyVO) {
        logger.info("分页查询值日信息入参:{}", JSON.toJSONString(dutyVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(dutyVO.getPageStart(), dutyVO.getPageSize());

        DutyDTO dutyDTO = new DutyDTO();
        BeanUtils.copyProperties(dutyVO, dutyDTO);

        List<DutyVO> dutyVOList = dutyMapper.query(dutyDTO);

        PageInfo<DutyVO> pageInfo = new PageInfo<>(dutyVOList);
        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询值日信息成功");
        return responseEntity;
    }

    /**
     * 新增值日信息
     *
     * @param dutyVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, DutyVO dutyVO) {
        logger.info("新增值日信息入参:{}", JSON.toJSONString(dutyVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(dutyVO.getDescription()) &&
                dutyVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        String currentUser = RequestMsgUtil.getSessionUserName();
        DutyDTO dutyDTO = new DutyDTO();
        BeanUtils.copyProperties(dutyVO, dutyDTO);

        if (StringUtils.isBlank(dutyDTO.getWeekday()) && Objects.nonNull(dutyDTO.getDutyDate())) {
            Map<String, String> weekMap = CommonConstant.weekMap;
            dutyDTO.setWeekday(weekMap.get(DateUtil.getDayWeekName(dutyDTO.getDutyDate())));
        }

        Map<String, String> map = commonService.getTeacherName();
        dutyDTO.setTeacherName(map.get(dutyDTO.getTeacherId()));
        dutyDTO.setCreatedBy(currentUser);
        dutyDTO.setUpdatedBy(currentUser);

        dutyMapper.insert(dutyDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增值日信息成功");
        return responseEntity;
    }

    /**
     * 修改值日信息
     *
     * @param dutyVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, DutyVO dutyVO) {
        logger.info("修改值日信息入参:{}", JSON.toJSONString(dutyVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(dutyVO.getId()), "值日信息ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(dutyVO.getDescription()) &&
                dutyVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        String currentUser = RequestMsgUtil.getSessionUserName();
        DutyDTO dutyDTO = new DutyDTO();
        BeanUtils.copyProperties(dutyVO, dutyDTO);

        if (StringUtils.isBlank(dutyDTO.getWeekday()) && Objects.nonNull(dutyDTO.getDutyDate())) {
            Map<String, String> weekMap = CommonConstant.weekMap;
            dutyDTO.setWeekday(weekMap.get(DateUtil.getDayWeekName(dutyDTO.getDutyDate())));
        }

        Map<String, String> map = commonService.getTeacherName();
        dutyDTO.setTeacherName(map.get(dutyDTO.getTeacherId()));
        dutyDTO.setUpdatedBy(currentUser);

        dutyMapper.update(dutyDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改值日信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除值日信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除值日信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "值日信息ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        dutyMapper.delete(id);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除值日信息成功");
        return responseEntity;
    }

    /**
     * 批量删除值日信息
     *
     * @param request HTTP请求
     * @param ids     值日信息ID列表，多个ID用逗号分隔
     * @return 处理结果
     */
    @Override
    public ResponseEntity<Object> batchDelete(HttpServletRequest request, List<String> ids) {
        logger.info("批量删除值日信息，ids：{}", ids);
        ValidateUtil.paramValidate(CollectionUtils.isEmpty(ids), "值日信息ID列表不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        try {
            // 使用批量删除方法
            int deletedCount = dutyMapper.batchDelete(ids);
            responseEntity.setMsg(String.format("批量删除完成，成功删除%d条记录", deletedCount));
            responseEntity.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            logger.error("批量删除值日信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("批量删除失败：" + e.getMessage());
        }
        return responseEntity;
    }
}
