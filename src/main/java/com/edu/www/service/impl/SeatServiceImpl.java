package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.dto.ExamDTO;
import com.edu.www.dto.SeatDTO;
import com.edu.www.dto.StudentSubjectDTO;
import com.edu.www.mapper.ExamMapper;
import com.edu.www.mapper.StudentSubjectMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.ExamService;
import com.edu.www.service.SeatService;
import com.edu.www.utils.*;
import com.edu.www.vo.ExamVO;
import com.edu.www.vo.SeatVO;
import com.edu.www.vo.StudentSubjectVO;
import com.edu.www.vo.StudentVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeatServiceImpl implements SeatService {
    private static final Logger logger = LoggerFactory.getLogger(SeatService.class);

    @Autowired
    private ExamMapper examMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CommonService commonService;

    @Autowired
    private StudentSubjectMapper studentSubjectMapper;

    /**
     * 根据选择的科目信息查询学生信息
     *
     * @param request
     * @param seatVO
     * @return
     */
    @Override
    public ResponseEntity<Object> query(HttpServletRequest request, SeatVO seatVO) {
        logger.info("根据科目选择条件查询学生信息入参:{}", JSON.toJSONString(seatVO));

        // 验证入参
        ValidateUtil.paramValidate(Objects.isNull(seatVO) ||
                CollectionUtils.isEmpty(seatVO.getSubjectSelection()), "科目选择条件不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 权限验证
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        try {
            // 调用Mapper方法查询学生信息
            List<StudentVO> studentList = studentSubjectMapper.getStudentBySubjectSelection(seatVO);

            // 构建学生分组Map
            Map<String, List<SeatDTO.StudentInfo>> groupedStudents = buildStudentGroupsMap(studentList, seatVO);


            // "2024-2025 1st Semester Mid-Term Exam Seating Chart"
            String title = "";
            // "Year11 Physics(SL&HL)、Biology(SL&HL)"
            String subTitle = "";
            // "2025-5-15 Monday"
            String date = "";
            String firstMapKey = MapUtil.getFirstKey(groupedStudents);
            if (StringUtils.isNotBlank(firstMapKey)) {
                String[] parts = firstMapKey.split(Constant.SPACE);
                ExamDTO examDTO = new ExamDTO();
                examDTO.setDepartmentCode(seatVO.getDepartmentCode());
                // todo
//                if (parts.length > 0) {
//                    examDTO.setSubjectCode(parts[0]);
//                }
//                if (parts.length > 1) {
//                    examDTO.setSubjectType(parts[1]);
//                }
//                if (parts.length > 2) {
//                    examDTO.setSubjectLevel(parts[2]);
//                }
                List<ExamVO> examVOList = examMapper.query(examDTO);
                if (!CollectionUtils.isEmpty(examVOList)) {
                    ExamVO examVO = examVOList.get(NumberUtils.INTEGER_ZERO);
                    String semester = examVO.getSemester().equals("S1") ? "1st" : "2nd";
                    title = examVO.getYear() + Constant.SPACE + semester + " Semester " + CommonConstant.examTypeMap.get(examVO.getType()) + " Exam Seating Chart";
                    // subTitle的格式为："Year11 Physics(SL&HL)、Biology(SL&HL)"
                    subTitle = "Year" + examVO.getGradeCode() + Constant.SPACE + SeatUtil.formatCourses(MapUtil.getAllKeyAsSet(groupedStudents));
                    Map<String, String> map = CommonConstant.weekdayMap;
                    date = DateUtil.FormatDate(examVO.getExamDate(), DateUtil.FORMAT_DATE_SHORT) + Constant.SPACE + StringUtil.capitalizeFirstLetter(map.get(examVO.getWeekday()));
                }

            }


            Map<String, Object> map = new HashMap<>();
            map.put("title", title);
            map.put("subTitle", subTitle);
            map.put("date", date);
            map.put("subjectSize", groupedStudents.size());
            map.put("subject", groupedStudents);

            responseEntity.setData(map);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("查询学生信息成功");

            logger.info("查询到符合条件的学生数量: {}, 分组数量: {}",
                    studentList != null ? studentList.size() : 0,
                    groupedStudents.size());

        } catch (Exception e) {
            logger.error("查询学生信息失败", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询学生信息失败: " + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 构建学生分组Map
     *
     * @param studentList 学生列表
     * @param seatVO      查询条件
     * @return 分组结果Map，key为分组名称，value为学生列表
     */
    private Map<String, List<SeatDTO.StudentInfo>> buildStudentGroupsMap(List<StudentVO> studentList, SeatVO seatVO) {
        // 根据学生的科目信息进行分组
        Map<String, List<StudentVO>> subjectGroups = groupStudentsBySubject(studentList, seatVO);

        logger.info("学生分组结果: 共{}组", subjectGroups.size());
        subjectGroups.forEach((key, students) ->
                logger.info("组别[{}]: {}名学生", key, students.size()));

        // 构建最终的Map结构
        Map<String, List<SeatDTO.StudentInfo>> result = new LinkedHashMap<>();

        for (Map.Entry<String, List<StudentVO>> entry : subjectGroups.entrySet()) {
            String groupName = buildGroupDisplayName(entry.getKey());

            // 构建学生信息列表
            List<SeatDTO.StudentInfo> students = entry.getValue().stream()
                    .map(student -> {
                        SeatDTO.StudentInfo studentInfo = new SeatDTO.StudentInfo();
                        studentInfo.setNameZh(student.getNameZh());
                        studentInfo.setNameEn(student.getNameEn());
                        return studentInfo;
                    })
                    .collect(Collectors.toList());

            result.put(groupName, students);
        }

        return result;
    }

    /**
     * 根据科目信息对学生进行分组
     *
     * @param studentList 学生列表
     * @param seatVO      查询条件
     * @return 分组后的学生Map，key为分组标识，value为学生列表
     */
    private Map<String, List<StudentVO>> groupStudentsBySubject(List<StudentVO> studentList, SeatVO seatVO) {
        Map<String, List<StudentVO>> groups = new LinkedHashMap<>();

        for (StudentVO student : studentList) {
            try {
                // 查询学生的科目信息
                StudentSubjectDTO studentSubjectDTO = new StudentSubjectDTO();
                studentSubjectDTO.setStudentId(student.getId());
                List<StudentSubjectVO> studentSubjects = studentSubjectMapper.query(studentSubjectDTO);

                // 找到与查询条件匹配的科目
                StudentSubjectVO matchedSubject = findMatchedSubject(studentSubjects, seatVO);

                String groupKey;
                if (matchedSubject != null) {
                    // 构建分组key：科目编号 + 科目类型 + 科目层级
                    groupKey = buildGroupKey(matchedSubject.getSubjectCode(),
                            matchedSubject.getSubjectType(),
                            matchedSubject.getSubjectLevel());
                } else {
                    // 如果没有匹配的科目，放入默认组
                    groupKey = "其他";
                }

                groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(student);

            } catch (Exception e) {
                logger.warn("获取学生{}的科目信息失败: {}", student.getId(), e.getMessage());
                groups.computeIfAbsent("其他", k -> new ArrayList<>()).add(student);
            }
        }

        return groups;
    }

    /**
     * 构建分组key
     *
     * @param subjectCode  科目编号
     * @param subjectType  科目类型
     * @param subjectLevel 科目层级
     * @return 分组key
     */
    private String buildGroupKey(String subjectCode, String subjectType, String subjectLevel) {
        StringBuilder key = new StringBuilder(subjectCode);

        if (StringUtils.isNotBlank(subjectType)) {
            key.append("-").append(subjectType);
        }

        if (StringUtils.isNotBlank(subjectLevel)) {
            key.append("-").append(subjectLevel);
        }

        return key.toString();
    }

    /**
     * 构建分组显示名称
     *
     * @param groupKey 分组key
     * @return 显示名称
     */
    private String buildGroupDisplayName(String groupKey) {
        if ("其他".equals(groupKey)) {
            return "其他";
        }

        String[] parts = groupKey.split("-");
        StringBuilder name = new StringBuilder();

        if (parts.length >= 1) {
            name.append(parts[0]); // 保持英文科目名
        }

        if (parts.length >= 2) {
            name.append(" ").append(parts[1]);
        }

        if (parts.length >= 3) {
            name.append(" ").append(parts[2]);
        }

        return name.toString();
    }

    /**
     * 找到与查询条件匹配的学生科目信息
     *
     * @param studentSubjects 学生科目列表
     * @param seatVO          查询条件
     * @return 匹配的科目信息
     */
    private StudentSubjectVO findMatchedSubject(List<StudentSubjectVO> studentSubjects, SeatVO seatVO) {
        if (CollectionUtils.isEmpty(seatVO.getSubjectSelection())) {
            return null;
        }

        for (SeatVO.SubjectSelectionVO selection : seatVO.getSubjectSelection()) {
            for (StudentSubjectVO studentSubject : studentSubjects) {
                if (selection.getSubjectCode().equals(studentSubject.getSubjectCode())) {
                    // 检查是否有匹配的详细信息
                    if (!CollectionUtils.isEmpty(selection.getSubjectDetail())) {
                        for (SeatVO.SubjectDetailVO detail : selection.getSubjectDetail()) {
                            if (isSubjectDetailMatched(studentSubject, detail)) {
                                return studentSubject;
                            }
                        }
                    } else {
                        // 如果没有详细信息要求，直接匹配科目编号
                        return studentSubject;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检查学生科目是否与详细条件匹配
     *
     * @param studentSubject 学生科目信息
     * @param detail         详细条件
     * @return 是否匹配
     */
    private boolean isSubjectDetailMatched(StudentSubjectVO studentSubject, SeatVO.SubjectDetailVO detail) {
        // 检查科目类型
        if (StringUtils.isNotBlank(detail.getSubjectType()) &&
                !detail.getSubjectType().equals(studentSubject.getSubjectType())) {
            return false;
        }

        // 检查科目层级
        if (StringUtils.isNotBlank(detail.getSubjectLevel()) &&
                !detail.getSubjectLevel().equals(studentSubject.getSubjectLevel())) {
            return false;
        }

        return true;
    }
}