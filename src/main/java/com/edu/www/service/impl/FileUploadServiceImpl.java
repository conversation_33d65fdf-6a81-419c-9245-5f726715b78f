package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.enums.EduFileTypeEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.FileMapper;
import com.edu.www.service.FileService;
import com.edu.www.service.FileUploadService;
import com.edu.www.utils.FileUtil;
import com.edu.www.utils.GitHubUploadUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.FileVO;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 文件上传服务实现类
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private GitHubUploadUtil gitHubUploadUtil;

    @Autowired
    private FileService fileService;

    @Value("${file.upload.cache.dir:./upload-cache}")
    private String localCacheDir;

    /**
     * 获取文件访问URL
     *
     * @param fileId 文件ID
     * @return
     */
    @Override
    public ResponseEntity<Object> getFileUrl(String fileId) {
        logger.info("获取文件URL: {}", fileId);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(fileId), "文件ID不能为空");

            // 获取文件URL
            String fileUrl = gitHubUploadUtil.getFileUrl(fileId);
            if (StringUtils.isBlank(fileUrl)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("文件不存在");
                return responseEntity;
            }

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("fileId", fileId);
            resultData.put("fileUrl", fileUrl);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(resultData);
            responseEntity.setMsg("获取文件URL成功");

        } catch (Exception e) {
            logger.error("获取文件URL异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("获取文件URL失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 下载文件
     *
     * @param fileId   文件ID
     * @param response HTTP响应
     */
    @Override
    public void download(String fileId, HttpServletResponse response) {
        logger.info("获取本地文件入参: {}", fileId);

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(fileId), "文件ID不能为空");

            if (StringUtils.isBlank(fileId)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            // 查找本地文件
            String localFilePath = findFileInLocalCache(fileId);
            if (localFilePath == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            File file = new File(localFilePath);
            if (!file.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头
            String fileName = file.getName();
            String contentType = FileUtil.getContentType(fileName);
            response.setContentType(contentType);
            response.setContentLength((int) file.length());
            response.setHeader("Content-Disposition", "inline; filename=\"" + fileName + "\"");

            // 输出文件内容
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            logger.info("本地文件输出成功: {}", localFilePath);

        } catch (Exception e) {
            logger.error("获取本地文件异常", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 预览文件
     * 优先从本地存储查找图片，如果找不到则从GitHub获取
     *
     * @param fileId   文件ID
     * @param response HTTP响应
     */
    @Override
    public void preview(String fileId, HttpServletResponse response) {
        logger.info("预览文件入参: {}", fileId);

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(fileId), "文件ID不能为空");

            // 1. 优先尝试从本地存储获取文件
            String localFilePath = findFileInLocalCache(fileId);
            if (localFilePath != null) {
                File localFile = new File(localFilePath);
                if (localFile.exists() && localFile.isFile()) {
                    logger.info("从本地存储预览文件: {}", localFilePath);
                    FileUtil.outputFileToResponse(localFile, response);
                    return;
                }
            }

            // 2. 本地文件不存在，尝试从GitHub获取
            logger.info("本地文件不存在，尝试从GitHub获取: {}", fileId);
            String githubUrl = getGitHubFileUrl(fileId);
            if (StringUtils.isNotBlank(githubUrl)) {
                if (GitHubUploadUtil.downloadAndOutputFromGitHub(githubUrl, fileId, response)) {
                    logger.info("从GitHub预览文件成功: {}", githubUrl);
                    return;
                }
            }

            // 3. 都找不到，返回404
            logger.warn("文件不存在: {}", fileId);
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            response.getWriter().write("File not found: " + fileId);

        } catch (Exception e) {
            logger.error("预览文件异常: fileId={}", fileId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<Object> uploadFile(MultipartFile file) {
        logger.info("开始上传文件: {}", file.getOriginalFilename());
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 1. 参数验证
            ValidateUtil.paramValidate(Objects.isNull(file) || file.isEmpty(), "上传文件不能为空");
            String originalFileName = file.getOriginalFilename();
            ValidateUtil.paramValidate(StringUtils.isBlank(originalFileName), "文件名不能为空");

            // 2. 文件大小验证（限制50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            ValidateUtil.paramValidate(file.getSize() > maxSize, "文件大小不能超过50MB");

            // 3. 文件类型验证
            EduFileTypeEnum fileType = EduFileTypeEnum.getByFileName(originalFileName);
            if (fileType == EduFileTypeEnum.FILE_OTHER) {
                logger.warn("不支持的文件类型: {}", originalFileName);
            }

            // 4. 上传文件
            String fileId = gitHubUploadUtil.uploadFile(file);
            if (StringUtils.isBlank(fileId)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("文件上传失败");
                return responseEntity;
            }

            // 5. 保存文件信息到数据库
            try {
                saveFileInfoToDatabase(fileId, originalFileName, file.getSize(), fileType);
                logger.info("文件信息已保存到数据库: fileId={}", fileId);
            } catch (Exception e) {
                logger.error("保存文件信息到数据库失败: fileId={}", fileId, e);
                // 不影响上传成功的返回，只记录日志
            }
            responseEntity.setData(fileId);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("文件上传成功");

            logger.info("文件上传成功: fileId={}, fileName={}", fileId, originalFileName);

        } catch (Exception e) {
            logger.error("文件上传异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("文件上传失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 批量上传文件
     *
     * @param files
     * @return
     */
    @Override
    public ResponseEntity<Object> batchUploadFile(MultipartFile[] files) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        try {
            // 参数校验
            ValidateUtil.paramValidate(ArrayUtils.isEmpty(files), "上传文件不能为空");

            List<Object> results = new ArrayList<>();

            for (MultipartFile file : files) {
                ResponseEntity<Object> result = uploadFile(file);
                results.add(result.getData());
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(results);
            responseEntity.setMsg("批量上传完成");

        } catch (Exception e) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("批量上传失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> deleteFile(String fileId) {
        logger.info("删除文件: {}", fileId);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(fileId), "文件ID不能为空");

            // 1. 删除GitHub文件
            gitHubUploadUtil.deleteFileFromGitHub(fileId);

            // 2. 删除本地缓存文件
            deleteLocalFile(fileId);

            // 3. 删除数据库记录
            fileMapper.deleteByFileId(fileId);

        } catch (Exception e) {
            logger.error("删除文件异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除文件失败：" + e.getMessage());
        }

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除文件成功");
        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> batchDeleteFiles(List<String> fileIds) {
        logger.info("批量删除文件入参: {}", JSON.toJSONString(fileIds));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(CollectionUtils.isEmpty(fileIds), "文件ID不能为空");

            List<Object> results = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;

            for (String fileId : fileIds) {
                try {
                    ResponseEntity<Object> result = deleteFile(fileId);
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("fileId", fileId);
                    resultMap.put("success", result.getSuccess());
                    resultMap.put("message", result.getMsg());
                    results.add(resultMap);

                    if (result.getSuccess()) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    logger.error("删除文件失败: {}", fileId, e);
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("fileId", fileId);
                    resultMap.put("success", false);
                    resultMap.put("message", "删除失败：" + e.getMessage());
                    results.add(resultMap);
                    failCount++;
                }
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(results);
            responseEntity.setMsg(String.format("批量删除完成：成功%d个，失败%d个", successCount, failCount));

        } catch (Exception e) {
            logger.error("批量删除文件异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("批量删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 从本地缓存查找文件
     *
     * @param fileId 文件ID
     * @return 本地文件路径
     */
    private String findFileInLocalCache(String fileId) {
        try {
            Path cacheDir = Paths.get(localCacheDir);
            if (!Files.exists(cacheDir)) {
                return null;
            }

            // 遍历所有子目录查找文件
            return Files.walk(cacheDir)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().startsWith(fileId + "."))
                    .findFirst()
                    .map(Path::toString)
                    .orElse(null);

        } catch (Exception e) {
            logger.error("查找本地缓存文件异常", e);
            return null;
        }
    }

    /**
     * 删除本地文件
     *
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    private boolean deleteLocalFile(String fileId) {
        try {
            String localFilePath = findFileInLocalCache(fileId);
            if (localFilePath == null) {
                return false;
            }

            File file = new File(localFilePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                logger.info("删除本地文件: {}, 结果: {}", localFilePath, deleted);
                return deleted;
            }
            return false;

        } catch (Exception e) {
            logger.error("删除本地文件异常", e);
            return false;
        }
    }

    /**
     * 保存文件信息到数据库
     *
     * @param fileId   文件ID
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param fileType 文件类型
     */
    private void saveFileInfoToDatabase(String fileId, String fileName, long fileSize, EduFileTypeEnum fileType) {
        try {
            FileVO fileVO = new FileVO();
            fileVO.setFileId(fileId);
            fileVO.setFileName(fileName);
            fileVO.setFileType(fileType.getDescription());
            fileVO.setFileSize(fileSize);
            fileVO.setStorageDir(fileType.getFolder());

            // 构建预览URL
            String previewUrl = gitHubUploadUtil.getFileUrl(fileId);
            if (StringUtils.isBlank(previewUrl)) {
                previewUrl = "/file/" + fileId;
            }
            fileVO.setPreviewUrl(previewUrl);

            // 设置扩展名
            String extension = FileUtil.getFileExtension(fileName);
            fileVO.setExtension(extension);

            // 设置是否可预览
            fileVO.setIsPreview(FileUtil.isPreviewableFile(extension) ? "1" : "0");

            // 构建完整路径
            String fullPath = String.format("./upload-cache/%s/%s", fileType.getFolder(), fileId + "." + extension);
            fileVO.setFullPath(fullPath);

            // 设置状态为启用
            fileVO.setStatus(EduYesOrNoEnum.YES.getKey());

            // 设置描述
            fileVO.setDescription("通过文件上传接口自动创建");

            // 调用FileService保存
            ResponseEntity<Object> result = fileService.insert(null, fileVO);
            if (!result.getSuccess()) {
                logger.error("保存文件信息到数据库失败: fileId={}, error={}", fileId, result.getMsg());
            }
        } catch (Exception e) {
            logger.error("保存文件信息到数据库异常: fileId={}", fileId, e);
            throw e;
        }
    }

    /**
     * 获取GitHub文件URL
     *
     * @param fileId 文件ID
     * @return GitHub文件URL
     */
    private String getGitHubFileUrl(String fileId) {
        try {
            // 从本地缓存路径推断GitHub路径
            String localPath = findFileInLocalCache(fileId);
            if (localPath == null) {
                // 如果本地没有，尝试根据fileId构建可能的GitHub路径
                return GitHubUploadUtil.buildGitHubUrlFromFileId(fileId);
            }

            // 从本地路径推断GitHub路径
            String relativePath = localPath.replace(localCacheDir + File.separator, "").replace(File.separator, "/");
            return "https://raw.githubusercontent.com/RuneDance/edu-file/master/" + relativePath;
        } catch (Exception e) {
            logger.error("获取GitHub文件URL异常: fileId={}", fileId, e);
            return null;
        }
    }


}
