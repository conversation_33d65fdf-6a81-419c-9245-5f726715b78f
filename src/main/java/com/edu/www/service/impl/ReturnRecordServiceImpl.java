package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.ReturnRecordDTO;
import com.edu.www.mapper.ReturnRecordMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.ReturnRecordService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.ReturnRecordVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 归还记录管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class ReturnRecordServiceImpl implements ReturnRecordService {
    private static final Logger logger = LoggerFactory.getLogger(ReturnRecordService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private ReturnRecordMapper returnRecordMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询归还记录信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "归还记录ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无查询权限");
                return responseEntity;
            }

            // 查询归还记录信息
            ReturnRecordVO returnRecordVO = returnRecordMapper.get(id);
            if (Objects.isNull(returnRecordVO)) {
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("归还记录信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(returnRecordVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询归还记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, ReturnRecordVO returnRecordVO) {
        logger.info("分页查询归还记录信息入参:{}", JSON.toJSONString(returnRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setData(new PageInfo());
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("无分页查询权限");
                return responseEntity;
            }

            // 设置分页参数
            PageHelper.startPage(returnRecordVO.getPageStart(), returnRecordVO.getPageSize());

            // 转换为DTO
            ReturnRecordDTO returnRecordDTO = new ReturnRecordDTO();
            BeanUtils.copyProperties(returnRecordVO, returnRecordDTO);

            // 查询数据
            List<ReturnRecordVO> returnRecordList = returnRecordMapper.query(returnRecordDTO);
            PageInfo<ReturnRecordVO> pageInfo = new PageInfo<>(returnRecordList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询归还记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, ReturnRecordVO returnRecordVO) {
        logger.info("新增归还记录信息入参:{}", JSON.toJSONString(returnRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateReturnRecord(returnRecordVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无新增权限");
                return responseEntity;
            }

            // 转换为DTO
            ReturnRecordDTO returnRecordDTO = new ReturnRecordDTO();
            BeanUtils.copyProperties(returnRecordVO, returnRecordDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            returnRecordDTO.setCreatedBy(currentUser);
            returnRecordDTO.setUpdatedBy(currentUser);

            // 新增归还记录信息
            returnRecordMapper.insert(returnRecordDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增归还记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, ReturnRecordVO returnRecordVO) {
        logger.info("修改归还记录信息入参:{}", JSON.toJSONString(returnRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateReturnRecord(returnRecordVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无修改权限");
                return responseEntity;
            }

            // 检查归还记录是否存在
            ReturnRecordVO existReturnRecord = returnRecordMapper.get(returnRecordVO.getId());
            if (existReturnRecord == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("归还记录信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            ReturnRecordDTO returnRecordDTO = new ReturnRecordDTO();
            BeanUtils.copyProperties(returnRecordVO, returnRecordDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            returnRecordDTO.setUpdatedBy(currentUser);

            // 修改归还记录信息
            returnRecordMapper.update(returnRecordDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改归还记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除归还记录信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "归还记录ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无删除权限");
                return responseEntity;
            }

            // 检查归还记录是否存在
            ReturnRecordVO existReturnRecord = returnRecordMapper.get(id);
            if (existReturnRecord == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("归还记录信息不存在");
                return responseEntity;
            }

            // 删除归还记录信息
            returnRecordMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除归还记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证归还记录信息
     */
    private String validateReturnRecord(ReturnRecordVO returnRecordVO, boolean isUpdate) {
        if (returnRecordVO == null) {
            return "归还记录信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(returnRecordVO.getId())) {
            return "归还记录ID不能为空";
        }

        if (StringUtils.isBlank(returnRecordVO.getBorrowId())) {
            return "领用ID不能为空";
        }

        if (returnRecordVO.getReturnQuantity() == null || returnRecordVO.getReturnQuantity() <= 0) {
            return "归还数量必须大于0";
        }

        if (returnRecordVO.getReturnDate() == null) {
            return "归还日期不能为空";
        }

        if (StringUtils.isBlank(returnRecordVO.getReturnCondition())) {
            return "归还状况不能为空";
        }

        return null;
    }
}
