package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.SecurityConstant;
import com.edu.www.convert.ToolConverter;
import com.edu.www.dto.ScheduleDTO;
import com.edu.www.enums.EduSubSubjectEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.ScheduleMapper;
import com.edu.www.mapper.TeacherMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.ToolService;
import com.edu.www.utils.CommonUtil;
import com.edu.www.utils.RedisUtil;
import com.edu.www.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Comparator;

/**
 * 调课
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class ToolServiceImpl implements ToolService {
    private static final Logger logger = LoggerFactory.getLogger(ToolService.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private TeacherMapper teacherMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 调课
     *
     * @param rescheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> reschedule(RescheduleVO rescheduleVO) {
        // 清除已有的Redis缓存数据
        String candidateKeyPattern = "teacher:candidate:" + rescheduleVO.getDepartmentCode() + ":" +
                rescheduleVO.getYear() + ":" + rescheduleVO.getSemester() + ":*";
        String swappableKeyPattern = "teacher:swappable:" + rescheduleVO.getDepartmentCode() + ":" +
                rescheduleVO.getYear() + ":" + rescheduleVO.getSemester() + ":*";

        // 获取并删除匹配的所有键
        Set<String> candidateKeys = redisUtil.keys(candidateKeyPattern);
        Set<String> swappableKeys = redisUtil.keys(swappableKeyPattern);

        if (!CollectionUtils.isEmpty(candidateKeys)) {
            for (String key : candidateKeys) {
                redisUtil.delete(key);
            }
        }

        if (!CollectionUtils.isEmpty(swappableKeys)) {
            for (String key : swappableKeys) {
                redisUtil.delete(key);
            }
        }

        logger.info("已清除候选教师缓存数据: {}个键", candidateKeys.size());
        logger.info("已清除可调换教师缓存数据: {}个键", swappableKeys.size());

        Map<String, String> map = commonService.getTeacherName();

        ScheduleDTO scheduleDTO = new ScheduleDTO();
        scheduleDTO.setDepartmentCode(rescheduleVO.getDepartmentCode());
        scheduleDTO.setYear(rescheduleVO.getYear());
        scheduleDTO.setSemester(rescheduleVO.getSemester());
        scheduleDTO.setGradeCode(rescheduleVO.getGradeCode());
        // A、B班级
        scheduleDTO.setClassCode(rescheduleVO.getClassCode());
        // 子学科
        scheduleDTO.setSubjectName(rescheduleVO.getSubjectName());
        scheduleDTO.setWeekday(rescheduleVO.getWeekday());
        scheduleDTO.setPeriod(rescheduleVO.getPeriod());

        // 需要换课老师组
        List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);
        scheduleVOList.forEach(e -> {
            System.out.println(CommonUtil.getGradeName(e.getGradeCode()) + " 第" + e.getPeriod() + "课 "
                    + CommonUtil.getWeekdayName(e.getWeekday()) + " 老师：" + map.get(e.getTeacherId()));
        });

        ScheduleDTO scheduleDTOPrim = new ScheduleDTO();
        scheduleDTOPrim.setDepartmentCode(rescheduleVO.getDepartmentCode());
        scheduleDTOPrim.setYear(rescheduleVO.getYear());
        scheduleDTOPrim.setSemester(rescheduleVO.getSemester());
        scheduleDTOPrim.setGradeCode(rescheduleVO.getGradeCode());
        scheduleDTOPrim.setClassCode(rescheduleVO.getClassCode());

        List<ScheduleVO> scheduleVOLists = scheduleMapper.query(scheduleDTOPrim);
        // 去掉 需要换课老师组的老师们
        scheduleVOLists.removeAll(scheduleVOList);

        // 根据 weekday 进行分组
        Map<String, List<ScheduleVO>> groupedByWeekday = scheduleVOLists.stream()
                .collect(Collectors.groupingBy(ScheduleVO::getWeekday));

        // 定义目标 weekday 范围
        List<String> weekdays = CommonConstant.weekdays;

        // 定义目标 period 范围
        List<String> periods = CommonConstant.periods;

        // 被换课老师组
        for (String weekday : weekdays) {
            for (String period : periods) {
                // 获取该星期的数据
                List<ScheduleVO> schedulesOnWeekday = groupedByWeekday.getOrDefault(weekday, new ArrayList<>());

                // 筛选出该节次的数据
                List<ScheduleVO> schedulesInPeriod = schedulesOnWeekday.stream()
                        .filter(schedule -> period.equals(schedule.getPeriod())).collect(Collectors.toList());
                // 候选教师key
                String candidateTeacherKey = "teacher:candidate:" + rescheduleVO.getDepartmentCode() + ":" +
                        rescheduleVO.getYear() + ":" + rescheduleVO.getSemester() + ":" +
                        weekday + ":" + period;

                if (!schedulesInPeriod.isEmpty()) {
                    // 存储可能的换课老师信息到Redis
                    redisUtil.set(candidateTeacherKey, JSON.toJSONString(schedulesInPeriod), 2 * 60);
                }
            }
        }

        // 获取需要换课的老师信息
        String requestWeekday = rescheduleVO.getWeekday();
        String requestPeriod = rescheduleVO.getPeriod();

        // 获取需要换课老师的ID列表
        List<String> requesterTeacherIds = scheduleVOList.stream().map(ScheduleVO::getTeacherId).collect(Collectors.toList());

        // 存储所有可以换课的老师组
        List<List<ScheduleVO>> swappableTeacherGroups = new ArrayList<>();

        System.out.println("\n以下是能与 " + CommonUtil.getWeekdayName(requestWeekday) + " 第" +
                requestPeriod + "节课 需要换课老师组能换课的组：");

        // 遍历所有可能的时间段
        for (String weekday : weekdays) {
            for (String period : periods) {
                // 跳过与请求相同的时间段
                if (weekday.equals(requestWeekday) && period.equals(requestPeriod)) {
                    continue;
                }

                // 从Redis中获取该时间段的候选老师组
                String candidateTeacherKey = "teacher:candidate:" + rescheduleVO.getDepartmentCode() + ":" +
                        rescheduleVO.getYear() + ":" + rescheduleVO.getSemester() + ":" + weekday + ":" + period;

                Object candidateObj = redisUtil.get(candidateTeacherKey);
                if (Objects.isNull(candidateObj)) {
                    continue;
                }

                List<ScheduleVO> candidateTeachers = JSON.parseArray(candidateObj.toString(), ScheduleVO.class);
                if (CollectionUtils.isEmpty(candidateTeachers)) {
                    continue;
                }

                // 获取候选老师ID列表
                List<String> candidateTeacherIds = candidateTeachers.stream()
                        .map(ScheduleVO::getTeacherId).collect(Collectors.toList());

                // 检查双方是否可以互换课程
                boolean canSwap = true;

                // 检查需要换课的老师在候选时间段是否已有课
                for (String teacherId : requesterTeacherIds) {
                    if (commonService.isScheduled(rescheduleVO, weekday, period, teacherId)) {
                        canSwap = false;
                        break;
                    }
                }

                // 检查候选老师在需要换课的时间段是否已有课
                if (canSwap) {
                    for (String teacherId : candidateTeacherIds) {
                        if (commonService.isScheduled(rescheduleVO, requestWeekday, requestPeriod, teacherId)) {
                            canSwap = false;
                            break;
                        }
                    }
                }

                // 如果可以换课，保存并打印候选老师组
                if (canSwap) {
                    swappableTeacherGroups.add(candidateTeachers);

                    // 打印格式要求的信息
                    System.out.println("\n--- 部门：" + candidateTeachers.get(0).getDepartment() +
                            " " + CommonUtil.getWeekdayName(weekday) + " 第 " + period + " 节课 ---");

                    for (ScheduleVO teacher : candidateTeachers) {
                        System.out.println("姓名：" + map.get(teacher.getTeacherId()) +
                                ", 学科：" + EduSubSubjectEnum.getDescByKey(teacher.getSubjectName()) +
                                ", 年级：" + CommonUtil.getGradeName(teacher.getGradeCode()) +
                                ", 班级：" + teacher.getClassCode());
                    }

                    // 将可换课组合存入Redis
                    String swapResultKey = "teacher:swappable:" + rescheduleVO.getDepartmentCode() + ":" +
                            rescheduleVO.getYear() + ":" + rescheduleVO.getSemester() + ":" +
                            requestWeekday + ":" + requestPeriod + ":" + weekday + ":" + period;

                    // 构建要存储的数据结构，包含双方信息
                    Map<String, Object> swapData = new HashMap<>();
                    swapData.put("requesterSchedules", scheduleVOList);
                    swapData.put("candidateSchedules", candidateTeachers);

                    redisUtil.set(swapResultKey, JSON.toJSONString(swapData), 2 * 60);
                }
            }
        }

        // 构建响应对象
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        responseEntity.setSuccess(true);
        responseEntity.setMsg("查询调课可能性成功");
        responseEntity.setData(swappableTeacherGroups);

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> query(HttpServletRequest request, ScheduleToolVO scheduleToolVO) {
        logger.info("查询课表信息，参数：{}", JSON.toJSONString(scheduleToolVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        // 查询当前整个部门当前学期和当前季度所有课表的信息
        ScheduleToolVO scheduleTool = new ScheduleToolVO();
        scheduleTool.setYear(scheduleToolVO.getYear());
        scheduleTool.setSemester(scheduleToolVO.getSemester());
        scheduleTool.setDepartmentCode(scheduleToolVO.getDepartmentCode());
        List<ScheduleVO> scheduleVOAllList = scheduleMapper.queryList(scheduleTool);
        
        // 复制查询条件用于筛选
        ScheduleToolVO filterScheduleTool = new ScheduleToolVO();
        filterScheduleTool.setYear(scheduleToolVO.getYear());
        filterScheduleTool.setSemester(scheduleToolVO.getSemester());
        filterScheduleTool.setDepartmentCode(scheduleToolVO.getDepartmentCode());
        filterScheduleTool.setGradeCodes(scheduleToolVO.getGradeCodes());
        filterScheduleTool.setGradeCmpSym(scheduleToolVO.getGradeCmpSym());
        filterScheduleTool.setTeacherIds(scheduleToolVO.getTeacherIds());
        filterScheduleTool.setTeacherCmpSym(scheduleToolVO.getTeacherCmpSym());
        filterScheduleTool.setWeekdays(scheduleToolVO.getWeekdays());
        filterScheduleTool.setWeekdayCmpSym(scheduleToolVO.getWeekdayCmpSym());
        filterScheduleTool.setPeriods(scheduleToolVO.getPeriods());
        filterScheduleTool.setPeriodCmpSym(scheduleToolVO.getPeriodCmpSym());
        filterScheduleTool.setStartTime(scheduleToolVO.getStartTime());
        filterScheduleTool.setEndTime(scheduleToolVO.getEndTime());
        
        // 查询当前整个部门当前学期和当前季度 所有课表的信息
        List<ScheduleVO> scheduleVOList = scheduleMapper.queryList(filterScheduleTool);

        List<TeacherScheduleVO> sortedList;
        
        // 如果有课
        if (EduYesOrNoEnum.YES.getKey().equals(scheduleToolVO.getHasClass())) {
            // 先按teacherId排序，再按gradeCode排序，最后按period排序
            sortedList = getTeacherScheduleVOList(scheduleVOList).stream()
                    .sorted(Comparator.comparing(TeacherScheduleVO::getTeacherId)
                            .thenComparing(TeacherScheduleVO::getGradeCode)
                            .thenComparing(TeacherScheduleVO::getWeekday)
                            .thenComparing(TeacherScheduleVO::getPeriod)).collect(Collectors.toList());
        } else {
            // 如果无课
            // 对于无课查询，需要一个不包含筛选条件的完整查询集合
            // 使用Stream过滤，实现差集操作，而不是依赖removeAll
            List<ScheduleVO> unusedSchedules = scheduleVOAllList.stream()
                .filter(schedule -> !scheduleVOList.contains(schedule))
                .collect(Collectors.toList());
                
            // 先按teacherId排序，再按gradeCode排序，最后按period排序
            sortedList = getTeacherScheduleVOList(unusedSchedules).stream()
                    .sorted(Comparator.comparing(TeacherScheduleVO::getTeacherId)
                            .thenComparing(TeacherScheduleVO::getGradeCode)
                            .thenComparing(TeacherScheduleVO::getWeekday)
                            .thenComparing(TeacherScheduleVO::getPeriod)).collect(Collectors.toList());
        }
        
        responseEntity.setSuccess(true);
        responseEntity.setMsg("查询教师课表信息成功");
        responseEntity.setData(sortedList);
        return responseEntity;
    }

    /**
     * 获取教师课表信息列表
     *
     * @param scheduleVOList
     * @return
     */
    private List<TeacherScheduleVO> getTeacherScheduleVOList(List<ScheduleVO> scheduleVOList) {
        Map<String, TeacherVO> map = commonService.getTeacherInfo();
        return ToolConverter.scheduleVOList2TeacherScheduleVOList(map, scheduleVOList);
    }

    private List<String> getDistinctTeacherId(List<ScheduleVO> scheduleVOList) {
        if (CollectionUtils.isEmpty(scheduleVOList)) {
            return new ArrayList<>();
        }
        // 提取 teacherId 字段并去重
        List<String> teacherIdList = scheduleVOList.stream()
                .map(ScheduleVO::getTeacherId).distinct().collect(Collectors.toList());
        return teacherIdList;
    }

}
