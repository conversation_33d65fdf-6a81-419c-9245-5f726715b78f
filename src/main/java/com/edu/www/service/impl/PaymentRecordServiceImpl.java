package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.PaymentRecordDTO;
import com.edu.www.mapper.PaymentRecordMapper;
import com.edu.www.service.PaymentRecordService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.vo.PaymentRecordVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 支付记录管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class PaymentRecordServiceImpl implements PaymentRecordService {
    private static final Logger logger = LoggerFactory.getLogger(PaymentRecordService.class);

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询支付记录信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("支付记录ID不能为空");
                return responseEntity;
            }

            // 查询支付记录信息
            PaymentRecordVO paymentRecordVO = paymentRecordMapper.get(id);
            if (paymentRecordVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("支付记录信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(paymentRecordVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询支付记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, PaymentRecordVO paymentRecordVO) {
        logger.info("分页查询支付记录信息入参:{}", JSON.toJSONString(paymentRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 设置分页参数
            PageHelper.startPage(paymentRecordVO.getPageStart(), paymentRecordVO.getPageSize());

            // 转换为DTO
            PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
            BeanUtils.copyProperties(paymentRecordVO, paymentRecordDTO);

            // 查询数据
            List<PaymentRecordVO> paymentRecordList = paymentRecordMapper.query(paymentRecordDTO);
            PageInfo<PaymentRecordVO> pageInfo = new PageInfo<>(paymentRecordList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询支付记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, PaymentRecordVO paymentRecordVO) {
        logger.info("新增支付记录信息入参:{}", JSON.toJSONString(paymentRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePaymentRecord(paymentRecordVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 转换为DTO
            PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
            BeanUtils.copyProperties(paymentRecordVO, paymentRecordDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            paymentRecordDTO.setCreatedBy(currentUser);
            paymentRecordDTO.setUpdatedBy(currentUser);

            // 新增支付记录信息
            paymentRecordMapper.insert(paymentRecordDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增支付记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, PaymentRecordVO paymentRecordVO) {
        logger.info("修改支付记录信息入参:{}", JSON.toJSONString(paymentRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePaymentRecord(paymentRecordVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 检查支付记录是否存在
            PaymentRecordVO existPaymentRecord = paymentRecordMapper.get(paymentRecordVO.getId());
            if (existPaymentRecord == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("支付记录信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
            BeanUtils.copyProperties(paymentRecordVO, paymentRecordDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            paymentRecordDTO.setUpdatedBy(currentUser);

            // 修改支付记录信息
            paymentRecordMapper.update(paymentRecordDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改支付记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除支付记录信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("支付记录ID不能为空");
                return responseEntity;
            }

            // 检查支付记录是否存在
            PaymentRecordVO existPaymentRecord = paymentRecordMapper.get(id);
            if (existPaymentRecord == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("支付记录信息不存在");
                return responseEntity;
            }

            // 删除支付记录信息
            paymentRecordMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除支付记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证支付记录信息
     */
    private String validatePaymentRecord(PaymentRecordVO paymentRecordVO, boolean isUpdate) {
        if (paymentRecordVO == null) {
            return "支付记录信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(paymentRecordVO.getId())) {
            return "支付记录ID不能为空";
        }

        if (StringUtils.isBlank(paymentRecordVO.getContractId())) {
            return "合同ID不能为空";
        }

        if (StringUtils.isBlank(paymentRecordVO.getPaymentType())) {
            return "支付类型不能为空";
        }

        if (paymentRecordVO.getPaymentAmount() == null || paymentRecordVO.getPaymentAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return "支付金额必须大于0";
        }

        return null;
    }
}
