package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.PurchaseContractDTO;
import com.edu.www.mapper.PurchaseContractMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.PurchaseContractService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.PurchaseContractVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 采购合同管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class PurchaseContractServiceImpl implements PurchaseContractService {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseContractService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private PurchaseContractMapper purchaseContractMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询采购合同信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "采购合同ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无查询权限");
                return responseEntity;
            }

            // 查询采购合同信息
            PurchaseContractVO purchaseContractVO = purchaseContractMapper.get(id);
            if (purchaseContractVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购合同信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(purchaseContractVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询采购合同信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 查询采购合同信息
     *
     * @return
     */
    @Override
    public ResponseEntity<Object> getPurchaseContract() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        PurchaseContractDTO purchaseContractDTO = new PurchaseContractDTO();
        List<PurchaseContractVO> purchaseContractList = purchaseContractMapper.query(purchaseContractDTO);
        responseEntity.setData(purchaseContractList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询采购合同信息成功");
        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, PurchaseContractVO purchaseContractVO) {
        logger.info("分页查询采购合同信息入参:{}", JSON.toJSONString(purchaseContractVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 设置分页参数
            PageHelper.startPage(purchaseContractVO.getPageStart(), purchaseContractVO.getPageSize());

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setData(new PageInfo());
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("无分页查询权限");
                return responseEntity;
            }

            // 转换为DTO
            PurchaseContractDTO purchaseContractDTO = new PurchaseContractDTO();
            BeanUtils.copyProperties(purchaseContractVO, purchaseContractDTO);

            // 查询数据
            List<PurchaseContractVO> purchaseContractList = purchaseContractMapper.query(purchaseContractDTO);
            PageInfo<PurchaseContractVO> pageInfo = new PageInfo<>(purchaseContractList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询采购合同信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, PurchaseContractVO purchaseContractVO) {
        logger.info("新增采购合同信息入参:{}", JSON.toJSONString(purchaseContractVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePurchaseContract(purchaseContractVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无新增权限");
                return responseEntity;
            }

            // 转换为DTO
            PurchaseContractDTO purchaseContractDTO = new PurchaseContractDTO();
            BeanUtils.copyProperties(purchaseContractVO, purchaseContractDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            purchaseContractDTO.setCreatedBy(currentUser);
            purchaseContractDTO.setUpdatedBy(currentUser);

            // 新增采购合同信息
            purchaseContractMapper.insert(purchaseContractDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增采购合同信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, PurchaseContractVO purchaseContractVO) {
        logger.info("修改采购合同信息入参:{}", JSON.toJSONString(purchaseContractVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePurchaseContract(purchaseContractVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无修改权限");
                return responseEntity;
            }

            // 检查采购合同是否存在
            PurchaseContractVO existPurchaseContract = purchaseContractMapper.get(purchaseContractVO.getId());
            if (existPurchaseContract == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购合同信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            PurchaseContractDTO purchaseContractDTO = new PurchaseContractDTO();
            BeanUtils.copyProperties(purchaseContractVO, purchaseContractDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            purchaseContractDTO.setUpdatedBy(currentUser);

            // 修改采购合同信息
            purchaseContractMapper.update(purchaseContractDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改采购合同信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除采购合同信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "采购合同ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无删除权限");
                return responseEntity;
            }

            // 检查采购合同是否存在
            PurchaseContractVO existPurchaseContract = purchaseContractMapper.get(id);
            if (existPurchaseContract == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购合同信息不存在");
                return responseEntity;
            }

            // 删除采购合同信息
            purchaseContractMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除采购合同信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证采购合同信息
     */
    private String validatePurchaseContract(PurchaseContractVO purchaseContractVO, boolean isUpdate) {
        if (purchaseContractVO == null) {
            return "采购合同信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(purchaseContractVO.getId())) {
            return "采购合同ID不能为空";
        }

        if (StringUtils.isBlank(purchaseContractVO.getContractNo())) {
            return "合同编号不能为空";
        }

        if (StringUtils.isBlank(purchaseContractVO.getContractTitle())) {
            return "合同标题不能为空";
        }

        if (purchaseContractVO.getContractAmount() == null || purchaseContractVO.getContractAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return "合同金额必须大于0";
        }

        return null;
    }
}
