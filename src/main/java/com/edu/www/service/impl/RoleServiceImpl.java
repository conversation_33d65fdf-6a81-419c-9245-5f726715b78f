package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.RoleDTO;
import com.edu.www.dto.RolePermissionDTO;
import com.edu.www.dto.TeacherDTO;
import com.edu.www.enums.EduTeacherTypeEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.MenuMapper;
import com.edu.www.mapper.RoleMapper;
import com.edu.www.mapper.RolePermissionMapper;
import com.edu.www.mapper.PermissionMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.RoleService;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.vo.RoleVO;
import com.edu.www.vo.PermissionVO;
import com.edu.www.vo.TeacherVO;
import com.edu.www.vo.MenuVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色信息管理服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Service
public class RoleServiceImpl implements RoleService {
    private static final Logger logger = LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    /**
     * 根据ID查询角色信息
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "角色ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        RoleVO roleVO = roleMapper.getById(id);
        responseEntity.setData(roleVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询角色信息成功");
        return responseEntity;
    }

    /**
     * 获取所有角色信息
     *
     * @return
     */
    @Override
    public ResponseEntity<Object> getRoleList() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        RoleDTO roleDTO = new RoleDTO();
        List<RoleVO> roleVOList = roleMapper.query(roleDTO);
        if (CollectionUtils.isEmpty(roleVOList)) {
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("未查询到所有角色信息");
            return responseEntity;
        }

        Map<String, Map<String, String>> roleMap = roleVOList.stream()
                .filter(r -> EduYesOrNoEnum.YES.getKey().equals(r.getStatus()))
                .collect(Collectors.toMap(
                        RoleVO::getCode,
                        r -> {
                            Map<String, String> map = new HashMap<>();
                            map.put(r.getId(), r.getName());
                            return map;
                        }
                ));

        responseEntity.setData(roleMap);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取所有角色信息成功");
        return responseEntity;
    }

    /**
     * 分页查询角色信息
     *
     * @param roleVO 查询条件
     * @return 角色列表
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, RoleVO roleVO) {
        logger.info("分页查询角色信息入参:{}", JSON.toJSONString(roleVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(roleVO.getPageStart(), roleVO.getPageSize());
        RoleDTO roleDTO = new RoleDTO();
        BeanUtils.copyProperties(roleVO, roleDTO);

        List<RoleVO> roleVOList = roleMapper.query(roleDTO);
        PageInfo<RoleVO> roleVOPageInfo = new PageInfo<>(roleVOList);

        responseEntity.setData(roleVOPageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询角色信息成功");
        return responseEntity;
    }

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public ResponseEntity<Object> queryByUserId(HttpServletRequest request, String userId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        List<RoleVO> roleVOList = roleMapper.queryByUserId(userId);
        responseEntity.setData(roleVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询用户角色列表成功");
        return responseEntity;
    }

    /**
     * 新增角色
     *
     * @param roleVO 角色信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, RoleVO roleVO) {
        logger.info("新增角色信息入参:{}", JSON.toJSONString(roleVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(roleVO.getName()), "角色名称不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(roleVO.getCode()), "角色编码不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        // 检查角色编码是否已存在
        if (roleMapper.existsByCode(roleVO.getCode())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("角色编码已存在");
            return responseEntity;
        }

        String userName = RequestMsgUtil.getSessionUserName();

        RoleDTO roleDTO = new RoleDTO();
        BeanUtils.copyProperties(roleVO, roleDTO);

        roleDTO.setCreatedBy(userName);
        roleDTO.setUpdatedBy(userName);
        roleMapper.insert(roleDTO);

        // 如果有关联的权限ID，则批量添加角色权限关联
        List<String> permissionIds = roleVO.getPermissionIds();
        if (!CollectionUtils.isEmpty(permissionIds)) {
            List<RolePermissionDTO> rolePermissionDTOList = new ArrayList<>();
            permissionIds.forEach(id -> {
                RolePermissionDTO dto = new RolePermissionDTO();
                dto.setRoleId(roleDTO.getId());
                dto.setPermissionId(id);
                dto.setUpdatedBy(userName);
                dto.setCreatedBy(userName);
                rolePermissionDTOList.add(dto);
            });

            rolePermissionMapper.batchInsert(rolePermissionDTOList);
        }

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增角色成功");
        return responseEntity;
    }

    /**
     * 更新角色
     *
     * @param roleVO 角色信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, RoleVO roleVO) {
        logger.info("更新角色信息入参:{}", JSON.toJSONString(roleVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(roleVO.getId()), "角色ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        // 检查角色编码是否已存在（排除自身）
        if (StringUtils.isNotBlank(roleVO.getCode())) {
            RoleDTO queryDTO = new RoleDTO();
            queryDTO.setCode(roleVO.getCode());
            List<RoleVO> existRoles = roleMapper.query(queryDTO);
            if (existRoles != null && !existRoles.isEmpty() && !existRoles.get(0).getId().equals(roleVO.getId())) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("角色编码已存在");
                return responseEntity;
            }
        }

        RoleDTO roleDTO = new RoleDTO();
        BeanUtils.copyProperties(roleVO, roleDTO);

        roleDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        roleMapper.update(roleDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("更新角色成功");
        return responseEntity;
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "角色ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        // 删除角色权限关联
        rolePermissionMapper.deleteByRoleId(id);

        // 删除角色
        roleMapper.delete(id);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除角色成功");
        return responseEntity;
    }

    /**
     * 根据菜单id找出所有的父级ID
     * @param menuId
     * @return
     */
    private Set<String> getParentId(String menuId){
        Set<String> parentIds = new HashSet<>();
        MenuVO menu = menuMapper.getById(menuId);
        while (Objects.nonNull(menu) && StringUtils.isNotBlank(menu.getParentId())) {
            parentIds.add(menu.getParentId());
            menu = menuMapper.getById(menu.getParentId());
        }
        return parentIds;
    }

    /**
     * 为角色分配权限
     *
     * @param roleId        角色ID
     * @param permissionIds 权限ID列表
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> assignPermissions(HttpServletRequest request, String roleId, Set<String> permissionIds) {
        logger.info("为角色分配权限入参, roleId:{}, permissionIds:{}", roleId, JSON.toJSONString(permissionIds));
        ValidateUtil.paramValidate(StringUtils.isBlank(roleId), "角色ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 先删除原有的角色权限关联
        rolePermissionMapper.deleteByRoleId(roleId);

        List<RolePermissionDTO> rolePermissionDTOList = new ArrayList<>();
        String userName = RequestMsgUtil.getSessionUserName();
        // 页面传过来的 permissionIds 都是 页面菜单的 权限ID
        if (!CollectionUtils.isEmpty(permissionIds)) {
            Set<String> allParentIds = new HashSet<>();
            for (String id : permissionIds) {
                PermissionVO permissionVO = permissionMapper.getById(id);
                Set<String> sets = getParentId(permissionVO.getMenuId());
                allParentIds.addAll(sets);
            }
            permissionIds.addAll(allParentIds);
        }

        if (!CollectionUtils.isEmpty(permissionIds)) {
            permissionIds.forEach(id -> {
                RolePermissionDTO dto = new RolePermissionDTO();
                dto.setRoleId(roleId);
                dto.setPermissionId(id);
                dto.setUpdatedBy(userName);
                dto.setCreatedBy(userName);
                rolePermissionDTOList.add(dto);
            });
            // 批量添加角色权限关联
            rolePermissionMapper.batchInsert(rolePermissionDTOList);
        }

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("角色权限分配成功");
        return responseEntity;
    }

    /**
     * 查询角色的权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public ResponseEntity<Object> queryRolePermissions(HttpServletRequest request, String roleId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(roleId), "角色ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        List<PermissionVO> permissionVOList = permissionMapper.queryByRoleId(roleId);

        responseEntity.setData(permissionVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询角色权限列表成功");
        return responseEntity;
    }
} 