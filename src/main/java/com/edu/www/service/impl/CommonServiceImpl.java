package com.edu.www.service.impl;


import com.alibaba.fastjson.JSON;
import com.edu.www.client.managebac.MBYearGroupsClient;
import com.edu.www.constants.Constant;
import com.edu.www.dto.ScheduleDTO;
import com.edu.www.dto.SubjectDTO;
import com.edu.www.dto.TeacherDTO;
import com.edu.www.mapper.*;
import com.edu.www.po.MBYearGroupPO;
import com.edu.www.po.MBYearGroupResponsePO;
import com.edu.www.service.CommonService;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公共服务
 *
 * <AUTHOR>
 * @date 2020/11/17
 */
@Service
public class CommonServiceImpl implements CommonService {
    private static final Logger logger = LoggerFactory.getLogger(CommonService.class);

    @Value("${auth.token.mb}")
    private String authToken;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private TeacherMapper teacherMapper;

    @Autowired
    private SubjectMapper subjectMapper;

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private FileMapper fileMapper;

    /**
     * 获取未来6年的学年时间段列表
     *
     * @return
     */
    @Override
    public List<String> getAcademicYearList() {
        List<String> yearList = new ArrayList<>();

        // 获取当前年份
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR) - 1;

        // 生成6个连续的学年时间段
        for (int i = 0; i < 6; i++) {
            int startYear = currentYear + i;
            int endYear = startYear + 1;
            String academicYear = startYear + Constant.HYPHEN + endYear;
            yearList.add(academicYear);
        }
        return yearList;
    }

    /**
     * 是否有课(true：有课，false：无课)
     *
     * @param rescheduleVO
     * @param weekday
     * @param period
     * @param teacherId
     * @return
     */
    @Override
    public boolean isScheduled(RescheduleVO rescheduleVO, String weekday, String period, String teacherId) {
        ScheduleDTO dto = new ScheduleDTO();
        dto.setDepartmentCode(rescheduleVO.getDepartmentCode());
        dto.setYear(rescheduleVO.getYear());
        dto.setSemester(rescheduleVO.getSemester());
        dto.setWeekday(weekday);
        dto.setPeriod(period);
        dto.setTeacherId(teacherId);
        List<ScheduleVO> scheduleVOList = scheduleMapper.query(dto);
        if (CollectionUtils.isEmpty(scheduleVOList)) {
            return false;
        }
        return true;
    }

    /**
     * 获取教师信息和ID
     *
     * @return
     */
    @Override
    public Map<String, TeacherVO> getTeacherInfo() {
        TeacherDTO teacherDTO = new TeacherDTO();
        List<TeacherVO> teacherVOList = teacherMapper.query(teacherDTO);
        return teacherVOList.stream()
                .collect(Collectors.toMap(TeacherVO::getId, Function.identity()));
    }

    /**
     * 获取教师名和ID
     *
     * @return
     */
    @Override
    public Map<String, String> getTeacherName() {
        List<TeacherVO> teacherVOList = teacherMapper.query(new TeacherDTO());
        return teacherVOList.stream().collect(Collectors.toMap(TeacherVO::getId, TeacherVO::getNameZh));
    }

    /**
     * 获取子学科名称和ID
     *
     * @return
     */
    @Override
    public Map<String, String> getSubSubjectName() {
        SubjectDTO subjectDTOO = new SubjectDTO();
        List<SubjectVO> subjectVOList = subjectMapper.query(subjectDTOO);


        return subjectVOList.stream()
                .collect(Collectors.toMap(SubjectVO::getSubjectTeacherId, SubjectVO::getSubSubjectName));
    }

    /**
     * 判断是否为超级管理员
     *
     * @param userId
     * @return
     */
    @Override
    public boolean isSuperAdmin(String userId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");

        List<UserRoleVO> userRoleVOList = userRoleMapper.getByUserId(userId);
        if (CollectionUtils.isEmpty(userRoleVOList)) {
            return false;
        }

        boolean isSuperAdmin = false;
        if (!CollectionUtils.isEmpty(userRoleVOList)) {
            isSuperAdmin = userRoleVOList.stream()
                    .map(UserRoleVO::getRoleId)
                    .map(roleMapper::getById)
                    .filter(Objects::nonNull)
                    .anyMatch(roleVO -> Constant.SUPER_ADMIN.equals(roleVO.getCode()));
        }
        return isSuperAdmin;
    }

    /**
     * 校验是否有按钮权限
     *
     * @param request
     * @return
     */
    @Override
    public boolean hasButtonPermission(HttpServletRequest request) {
        Object obj = request.getAttribute(Constant.USER_ID);
        ValidateUtil.paramValidate(Objects.isNull(obj) || StringUtils.isBlank((String) obj), "未获取到用户ID");

        String userId = (String) obj;
        // 判断是否为超级管理员
        if (this.isSuperAdmin(userId)) {
            return true;
        }

        List<PermissionVO> permissionVOList = permissionMapper.queryButtonPermissionsByUserId(userId);
        if (CollectionUtils.isEmpty(permissionVOList)) {
            return false;
        }

        String path = request.getRequestURI();
        String method = request.getMethod();
        if (StringUtils.isBlank(path) || StringUtils.isBlank(method)) {
            return false;
        }

        // 使用anyMatch提升效率和可读性
        return permissionVOList.stream()
                .anyMatch(e -> method.equalsIgnoreCase(e.getMethod()) && path.equals(e.getUrl()));
    }

    @Override
    public List<MBYearGroupPO> getYearGroups() {
        logger.info("开始获取年级组信息");

        // 参数验证
        ValidateUtil.paramValidate(StringUtils.isBlank(authToken), "API接口Token为空");

        List<MBYearGroupPO> allYearGroups = new ArrayList<>();

        try {
            MBYearGroupsClient apiClient = new MBYearGroupsClient();
            int currentPage = 1;
            boolean hasMorePages = true;

            // 循环获取所有页面的年级组数据
            while (hasMorePages) {
                logger.debug("正在获取第{}页年级组数据...", currentPage);

                // 构建查询参数
                Map<String, String> queryParams = new HashMap<>();
                queryParams.put("page", String.valueOf(currentPage));
                queryParams.put("per_page", "100");

                // 调用API获取数据
                Optional<String> yearGroupsResponse = apiClient.getYearGroupsWithParams(authToken, queryParams);

                if (!yearGroupsResponse.isPresent()) {
                    logger.error("第{}页年级组数据获取失败", currentPage);
                    break;
                }

                // 解析响应
                String responseJson = yearGroupsResponse.get();
                logger.debug("第{}页API响应长度: {}", currentPage, responseJson.length());

                MBYearGroupResponsePO responsePO = JSON.parseObject(responseJson, MBYearGroupResponsePO.class);

                if (responsePO == null) {
                    logger.warn("第{}页响应解析失败", currentPage);
                    break;
                }

                if (CollectionUtils.isEmpty(responsePO.getYearGroups())) {
                    logger.info("第{}页没有年级组数据，结束获取", currentPage);
                    break;
                }

                // 添加到结果集合
                allYearGroups.addAll(responsePO.getYearGroups());

                logger.info("第{}页获取到{}个年级组", currentPage, responsePO.getYearGroups().size());

                // 检查是否还有更多页面
                if (responsePO.getMeta() != null) {
                    hasMorePages = currentPage < responsePO.getMeta().getTotalPages();
                    logger.debug("分页信息 - 当前页: {}, 总页数: {}, 总记录数: {}",
                            currentPage, responsePO.getMeta().getTotalPages(), responsePO.getMeta().getTotalCount());
                } else {
                    logger.warn("第{}页响应中缺少分页元数据，结束获取", currentPage);
                    hasMorePages = false;
                }

                currentPage++;

                // 添加延迟避免API限流
                Thread.sleep(100);
            }

            logger.info("年级组信息获取完成，共获取{}个年级组", allYearGroups.size());

            // 按年级组ID排序
            allYearGroups.sort((a, b) -> {
                if (a.getId() == null && b.getId() == null) return 0;
                if (a.getId() == null) return 1;
                if (b.getId() == null) return -1;
                return a.getId().compareTo(b.getId());
            });

        } catch (Exception e) {
            logger.error("获取年级组信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取年级组信息失败: " + e.getMessage(), e);
        }

        return allYearGroups;
    }

    /**
     * 根据工号获取教师信息
     *
     * @param empNo
     * @return
     */
    @Override
    public TeacherVO getTeacherByEmpNo(String empNo) {
        ValidateUtil.paramValidate(StringUtils.isBlank(empNo), "工号不能为空");
        TeacherDTO teacherDTO = new TeacherDTO();
        teacherDTO.setEmpNo(empNo);
        List<TeacherVO> teacherVOList = teacherMapper.query(teacherDTO);
        if (CollectionUtils.isEmpty(teacherVOList)) {
            return new TeacherVO();
        }
        return teacherVOList.get(NumberUtils.INTEGER_ZERO);
    }

    /**
     * 根据多个文件ID查询文件信息
     *
     * @param fileIds
     * @return
     */
    @Override
    public Map<String, FileVO> getFileInfo(List<String> fileIds) {
        // 参数验证
        ValidateUtil.paramValidate(CollectionUtils.isEmpty(fileIds), "文件ID不能为空");
        List<FileVO> fileVOList = fileMapper.getByFileIds(fileIds);
        return fileVOList.stream()
                .collect(Collectors.toMap(FileVO::getFileId, Function.identity()));
    }
}
