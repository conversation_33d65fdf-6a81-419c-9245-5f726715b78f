package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.InboundRecordDTO;
import com.edu.www.mapper.InboundRecordMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.InboundRecordService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.InboundRecordVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 入库记录管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class InboundRecordServiceImpl implements InboundRecordService {
    private static final Logger logger = LoggerFactory.getLogger(InboundRecordService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private InboundRecordMapper inboundRecordMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询入库记录信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "入库记录ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无查询权限");
                return responseEntity;
            }

            // 查询入库记录信息
            InboundRecordVO inboundRecordVO = inboundRecordMapper.get(id);
            if (inboundRecordVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("入库记录信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(inboundRecordVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询入库记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, InboundRecordVO inboundRecordVO) {
        logger.info("分页查询入库记录信息入参:{}", JSON.toJSONString(inboundRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setData(new PageInfo());
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("无分页查询权限");
                return responseEntity;
            }

            // 设置分页参数
            PageHelper.startPage(inboundRecordVO.getPageStart(), inboundRecordVO.getPageSize());

            // 转换为DTO
            InboundRecordDTO inboundRecordDTO = new InboundRecordDTO();
            BeanUtils.copyProperties(inboundRecordVO, inboundRecordDTO);

            // 查询数据
            List<InboundRecordVO> inboundRecordList = inboundRecordMapper.query(inboundRecordDTO);
            PageInfo<InboundRecordVO> pageInfo = new PageInfo<>(inboundRecordList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询入库记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, InboundRecordVO inboundRecordVO) {
        logger.info("新增入库记录信息入参:{}", JSON.toJSONString(inboundRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateInboundRecord(inboundRecordVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无新增权限");
                return responseEntity;
            }

            // 转换为DTO
            InboundRecordDTO inboundRecordDTO = new InboundRecordDTO();
            BeanUtils.copyProperties(inboundRecordVO, inboundRecordDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            inboundRecordDTO.setCreatedBy(currentUser);
            inboundRecordDTO.setUpdatedBy(currentUser);

            // 新增入库记录信息
            inboundRecordMapper.insert(inboundRecordDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增入库记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, InboundRecordVO inboundRecordVO) {
        logger.info("修改入库记录信息入参:{}", JSON.toJSONString(inboundRecordVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateInboundRecord(inboundRecordVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无修改权限");
                return responseEntity;
            }

            // 检查入库记录是否存在
            InboundRecordVO existInboundRecord = inboundRecordMapper.get(inboundRecordVO.getId());
            if (existInboundRecord == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("入库记录信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            InboundRecordDTO inboundRecordDTO = new InboundRecordDTO();
            BeanUtils.copyProperties(inboundRecordVO, inboundRecordDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            inboundRecordDTO.setUpdatedBy(currentUser);

            // 修改入库记录信息
            inboundRecordMapper.update(inboundRecordDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改入库记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除入库记录信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "入库记录ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无删除权限");
                return responseEntity;
            }

            // 检查入库记录是否存在
            InboundRecordVO existInboundRecord = inboundRecordMapper.get(id);
            if (existInboundRecord == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("入库记录信息不存在");
                return responseEntity;
            }

            // 删除入库记录信息
            inboundRecordMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除入库记录信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证入库记录信息
     */
    private String validateInboundRecord(InboundRecordVO inboundRecordVO, boolean isUpdate) {
        if (inboundRecordVO == null) {
            return "入库记录信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(inboundRecordVO.getId())) {
            return "入库记录ID不能为空";
        }

        if (StringUtils.isBlank(inboundRecordVO.getBookId())) {
            return "书籍ID不能为空";
        }

        if (inboundRecordVO.getInboundQuantity() == null || inboundRecordVO.getInboundQuantity() <= 0) {
            return "入库数量必须大于0";
        }

        if (inboundRecordVO.getInboundDate() == null) {
            return "入库日期不能为空";
        }

        return null;
    }
}
