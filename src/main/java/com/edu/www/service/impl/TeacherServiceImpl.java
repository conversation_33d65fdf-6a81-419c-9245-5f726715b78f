package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.TeacherDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduGenderEnum;
import com.edu.www.enums.EduTeacherTypeEnum;
import com.edu.www.enums.EduWorkStatusEnum;
import com.edu.www.mapper.TeacherMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.TeacherService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.TeacherVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教师信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class TeacherServiceImpl implements TeacherService {
    private static final Logger logger = LoggerFactory.getLogger(TeacherService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private TeacherMapper teacherMapper;
    private static final String EXCLUDED_NAME_AILEEN = "刘玲Aileen";
    private static final String EXCLUDED_NAME_LING = "刘玲Ling";

    /**
     * 根据ID查询教师信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "教师ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        TeacherVO teacherVO = teacherMapper.getById(id);
        // 翻译枚举值为可读文本
        if (teacherVO != null) {
            if (StringUtils.isNotBlank(teacherVO.getGender())) {
                EduGenderEnum genderEnum = EduGenderEnum.getByKey(teacherVO.getGender());
                if (genderEnum != null) {
                    teacherVO.setGender(genderEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(teacherVO.getDepartmentCode())) {
                EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(teacherVO.getDepartmentCode());
                if (deptEnum != null) {
                    teacherVO.setDepartment(deptEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(teacherVO.getStatus())) {
                EduWorkStatusEnum statusEnum = EduWorkStatusEnum.getByKey(teacherVO.getStatus());
                if (statusEnum != null) {
                    teacherVO.setStatus(statusEnum.getDesc());
                }
            }
        }

        responseEntity.setData(teacherVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询教师信息成功");
        return responseEntity;
    }

    /**
     * 分页查询教师信息
     *
     * @param teacherVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, TeacherVO teacherVO) {
        logger.info("分页查询教师信息入参:{}", JSON.toJSONString(teacherVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(teacherVO.getPageStart(), teacherVO.getPageSize());
        TeacherDTO teacherDTO = new TeacherDTO();
        BeanUtils.copyProperties(teacherVO, teacherDTO);
        List<TeacherVO> teacherVOList = teacherMapper.query(teacherDTO);
        // 翻译枚举值为可读文本
        if (teacherVOList != null && !teacherVOList.isEmpty()) {
            for (TeacherVO vo : teacherVOList) {
                if (StringUtils.isNotBlank(vo.getGender())) {
                    EduGenderEnum genderEnum = EduGenderEnum.getByKey(vo.getGender());
                    if (genderEnum != null) {
                        vo.setGender(genderEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getDepartmentCode())) {
                    EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(vo.getDepartmentCode());
                    if (deptEnum != null) {
                        vo.setDepartment(deptEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getStatus())) {
                    EduWorkStatusEnum statusEnum = EduWorkStatusEnum.getByKey(vo.getStatus());
                    if (statusEnum != null) {
                        vo.setStatus(statusEnum.getDesc());
                    }
                }
            }
        }

        PageInfo<TeacherVO> pageInfo = new PageInfo(teacherVOList);

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询教师信息成功");
        return responseEntity;
    }

    /**
     * 新增教师信息
     *
     * @param teacherVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, TeacherVO teacherVO) {
        logger.info("新增教师信息入参:{}", JSON.toJSONString(teacherVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(teacherVO.getDescription()) &&
                teacherVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        TeacherDTO teacherDTO = new TeacherDTO();
        BeanUtils.copyProperties(teacherVO, teacherDTO);
        String userName = RequestMsgUtil.getSessionUserName();
        teacherDTO.setCreatedBy(userName);
        teacherDTO.setUpdatedBy(userName);
        teacherMapper.insert(teacherDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增教师信息成功");
        return responseEntity;
    }

    /**
     * 修改教师信息
     *
     * @param teacherVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, TeacherVO teacherVO) {
        logger.info("修改教师信息入参:{}", JSON.toJSONString(teacherVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(teacherVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(teacherVO.getDescription()) &&
                teacherVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        TeacherDTO teacherDTO = new TeacherDTO();
        BeanUtils.copyProperties(teacherVO, teacherDTO);
        String userName = RequestMsgUtil.getSessionUserName();
        teacherDTO.setUpdatedBy(userName);
        teacherMapper.update(teacherDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改教师信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除教师信息
     *
     * @param request
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("教师信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "教师ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        teacherMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除教师信息成功");
        return responseEntity;
    }


    /**
     * 查询所有教师信息
     *
     * @return
     */
    @Override
    public ResponseEntity<Object> getAllTeacher() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        List<TeacherVO> teacherVOList = getTeacherByDepartmentCode("");
        responseEntity.setData(teacherVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询所有教师信息成功");
        return responseEntity;
    }

    /**
     * 根据部门编码查询教师信息
     *
     * @param departmentCode
     * @return
     */
    @Override
    public ResponseEntity<Object> getByDepartmentCode(String departmentCode) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        List<TeacherVO> teacherVOList = getTeacherByDepartmentCode(departmentCode);
        responseEntity.setData(teacherVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("根据部门编码查询教师信息成功");
        return responseEntity;
    }

    /**
     * 根据部门编号获取老师信息
     *
     * @param departmentCode
     * @return
     */
    private List<TeacherVO> getTeacherByDepartmentCode(String departmentCode) {
        TeacherDTO teacherDTO = new TeacherDTO();
        if (StringUtils.isNotBlank(departmentCode)) {
            teacherDTO.setDepartmentCode(departmentCode);
        }
        List<TeacherVO> teacherVOList = teacherMapper.query(teacherDTO).stream()
                .peek(e -> {
                    if (EduTeacherTypeEnum.YES.getKey().equals(e.getType())) {
                        String nameZh = e.getNameZh();
                        String nameEn = e.getNameEn();
                        if (StringUtils.isNotBlank(nameEn) &&
                                !EXCLUDED_NAME_AILEEN.equals(nameZh) &&
                                !EXCLUDED_NAME_LING.equals(nameZh)) {
                            e.setNameZh(nameZh + "(" + nameEn + ")");
                        }
                    }
                })
                .collect(Collectors.toList());
        return teacherVOList;
    }

} 