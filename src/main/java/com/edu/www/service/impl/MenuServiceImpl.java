package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.Constant;
import com.edu.www.dto.MenuDTO;
import com.edu.www.enums.EduSwitchEnum;
import com.edu.www.mapper.MenuMapper;
import com.edu.www.mapper.RoleMapper;
import com.edu.www.mapper.UserRoleMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.MenuService;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.vo.MenuVO;
import com.edu.www.vo.RoleVO;
import com.edu.www.vo.UserRoleVO;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 菜单信息管理服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Service
public class MenuServiceImpl implements MenuService {
    private static final Logger logger = LoggerFactory.getLogger(MenuServiceImpl.class);

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询菜单信息
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "菜单ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        MenuVO menuVO = menuMapper.getById(id);
        responseEntity.setData(menuVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询菜单信息成功");
        return responseEntity;
    }

    /**
     * 分页查询菜单信息
     *
     * @param menuVO 查询条件
     * @return 菜单列表
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, MenuVO menuVO) {
        logger.info("分页查询菜单信息入参:{}", JSON.toJSONString(menuVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        MenuDTO menuDTO = new MenuDTO();
        BeanUtils.copyProperties(menuVO, menuDTO);

        // 查询父级菜单数量
        int totalParentCount = menuMapper.queryParentAll(menuDTO);

        // 查询所有菜单
        List<MenuVO> allMenus = menuMapper.query(menuDTO);

        // 过滤出父级菜单
        List<MenuVO> parentMenus = allMenus.stream()
                .filter(menu -> StringUtils.isBlank(menu.getParentId()))
                .collect(Collectors.toList());

        // 手动分页
        int pageStart = menuVO.getPageStart();
        int pageSize = menuVO.getPageSize();
        int startIndex = (pageStart - 1) * pageSize;

        // 获取当前页的父级菜单
        List<MenuVO> currentPageParentMenus = new ArrayList<>();
        if (startIndex < parentMenus.size()) {
            int endIndex = Math.min(startIndex + pageSize, parentMenus.size());
            currentPageParentMenus = parentMenus.subList(startIndex, endIndex);
        }

        // 构建当前页父节点的菜单树
        List<MenuVO> menuTreeList = new ArrayList<>();
        for (MenuVO parentMenu : currentPageParentMenus) {
            // 为每个父节点构建子树
            MenuVO menuTree = new MenuVO();
            BeanUtils.copyProperties(parentMenu, menuTree);
            buildChildren(menuTree, allMenus);
            menuTreeList.add(menuTree);
        }

        // 创建分页信息对象
        PageInfo<MenuVO> menuVOPageInfo = new PageInfo<>();
        menuVOPageInfo.setList(menuTreeList);
        menuVOPageInfo.setTotal(totalParentCount);
        menuVOPageInfo.setPageNum(pageStart);
        menuVOPageInfo.setPageSize(pageSize);
        menuVOPageInfo.setPages((totalParentCount + pageSize - 1) / pageSize); // 计算总页数
        menuVOPageInfo.setSize(menuTreeList.size());

        responseEntity.setData(menuVOPageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询菜单信息成功");
        return responseEntity;
    }

    /**
     * 查询菜单树结构
     *
     * @return 菜单树
     */
    @Override
    public ResponseEntity<Object> queryMenuTree() {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 查询所有菜单（包含权限信息）
        List<MenuVO> allMenus = menuMapper.queryAllWithPermissions();

        // 构建菜单树
        List<MenuVO> menuTree = buildMenuTree(allMenus);

        responseEntity.setData(menuTree);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询菜单树成功");
        return responseEntity;
    }

    /**
     * 根据用户ID查询菜单树结构
     *
     * @param userId 用户ID
     * @return 菜单树
     */
    @Override
    public ResponseEntity<Object> queryMenuTreeByUserId(String userId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 判断是否为超级管理员
        boolean isSuperAdmin = commonService.isSuperAdmin(userId);
        List<MenuVO> userMenus = isSuperAdmin
                ? menuMapper.queryAllWithPermissions()
                : menuMapper.queryByUserId(userId);
        List<MenuVO> menuTree = buildMenuTree(userMenus);

        responseEntity.setData(menuTree);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询用户菜单树成功");
        return responseEntity;
    }

    /**
     * 新增菜单
     *
     * @param menuVO 菜单信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, MenuVO menuVO) {
        logger.info("新增菜单信息入参:{}", JSON.toJSONString(menuVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(menuVO.getName()), "菜单名称不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        // 检查同级菜单名称是否已存在
        MenuDTO queryDTO = new MenuDTO();
        queryDTO.setName(menuVO.getName());
        queryDTO.setParentId(menuVO.getParentId());
        List<MenuVO> existMenus = menuMapper.query(queryDTO);
        if (existMenus != null && !existMenus.isEmpty()) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("同级菜单名称已存在");
            return responseEntity;
        }

        MenuDTO menuDTO = new MenuDTO();
        BeanUtils.copyProperties(menuVO, menuDTO);

        menuDTO.setCreatedBy(RequestMsgUtil.getSessionUserName());
        menuDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());

        // 如果状态为空，默认为启用
        if (StringUtils.isBlank(menuDTO.getStatus())) {
            menuDTO.setStatus(EduSwitchEnum.ENABLE.getKey());
        }

        // 如果isHidden为空，默认为显示
        if (StringUtils.isBlank(menuDTO.getIsHidden())) {
            menuDTO.setIsHidden(EduSwitchEnum.DISABLE.getKey());
        }

        menuMapper.insert(menuDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增菜单成功");
        return responseEntity;
    }

    /**
     * 更新菜单
     *
     * @param menuVO 菜单信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, MenuVO menuVO) {
        logger.info("更新菜单信息入参:{}", JSON.toJSONString(menuVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(menuVO.getId()), "菜单ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        // 检查同级菜单名称是否已存在（排除自身）
        if (StringUtils.isNotBlank(menuVO.getName()) && StringUtils.isNotBlank(menuVO.getParentId())) {
            MenuDTO queryDTO = new MenuDTO();
            queryDTO.setName(menuVO.getName());
            queryDTO.setParentId(menuVO.getParentId());
            List<MenuVO> existMenus = menuMapper.query(queryDTO);
            if (existMenus != null && !existMenus.isEmpty() && !existMenus.get(0).getId().equals(menuVO.getId())) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("同级菜单名称已存在");
                return responseEntity;
            }
        }

        MenuDTO menuDTO = new MenuDTO();
        BeanUtils.copyProperties(menuVO, menuDTO);

        if (Objects.nonNull(menuDTO.getDirectorySort()) && menuDTO.getDirectorySort() == 0) {
            menuDTO.setDirectorySort(null);
        }

        if (Objects.nonNull(menuDTO.getPageSort()) && menuDTO.getPageSort() == 0) {
            menuDTO.setPageSort(null);
        }
        menuDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());

        menuMapper.update(menuDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("更新菜单成功");
        return responseEntity;
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "菜单ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        // 检查是否存在子菜单
        MenuDTO queryDTO = new MenuDTO();
        queryDTO.setParentId(id);
        List<MenuVO> children = menuMapper.query(queryDTO);
        if (children != null && !children.isEmpty()) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("该菜单下存在子菜单，无法删除");
            return responseEntity;
        }

        // 删除菜单
        menuMapper.delete(id);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除菜单成功");
        return responseEntity;
    }

    /**
     * 构建菜单树
     *
     * @param menuList 菜单列表
     * @return 菜单树
     */
    private List<MenuVO> buildMenuTree(List<MenuVO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return new ArrayList<>();
        }

        // 根节点列表
        List<MenuVO> rootMenus = menuList.stream()
                .filter(menu -> StringUtils.isBlank(menu.getParentId()))
                .collect(Collectors.toList());

        // 为根节点填充子节点
        rootMenus.forEach(rootMenu -> {
            buildChildren(rootMenu, menuList);
        });

        return rootMenus;
    }

    /**
     * 构建子菜单
     *
     * @param parent   父菜单
     * @param menuList 菜单列表
     */
    private void buildChildren(MenuVO parent, List<MenuVO> menuList) {
        List<MenuVO> children = menuList.stream()
                .filter(menu -> parent.getId().equals(menu.getParentId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parent.setChildren(children);
            // 递归构建子菜单的子菜单
            children.forEach(child -> buildChildren(child, menuList));
        }
    }
} 