package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.SupplierDTO;
import com.edu.www.mapper.SupplierMapper;
import com.edu.www.service.SupplierService;
import com.edu.www.service.CommonService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.SupplierCodeUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.SupplierVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商信息管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class SupplierServiceImpl implements SupplierService {
    private static final Logger logger = LoggerFactory.getLogger(SupplierService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private SupplierMapper supplierMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询供应商信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("供应商ID不能为空");
                return responseEntity;
            }

            // 查询供应商信息
            SupplierVO supplierVO = supplierMapper.get(id);
            if (supplierVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("供应商信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(supplierVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询供应商信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, SupplierVO supplierVO) {
        logger.info("分页查询供应商信息入参:{}", JSON.toJSONString(supplierVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 设置分页参数
            PageHelper.startPage(supplierVO.getPageStart(), supplierVO.getPageSize());

            // 转换为DTO
            SupplierDTO supplierDTO = new SupplierDTO();
            BeanUtils.copyProperties(supplierVO, supplierDTO);

            // 查询数据
            List<SupplierVO> supplierList = supplierMapper.query(supplierDTO);
            PageInfo<SupplierVO> pageInfo = new PageInfo<>(supplierList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询供应商信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, SupplierVO supplierVO) {
        logger.info("新增供应商信息入参:{}", JSON.toJSONString(supplierVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateSupplier(supplierVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 转换为DTO
            SupplierDTO supplierDTO = new SupplierDTO();
            BeanUtils.copyProperties(supplierVO, supplierDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            supplierDTO.setCreatedBy(currentUser);
            supplierDTO.setUpdatedBy(currentUser);

            int count = supplierMapper.getByCodeCount(supplierDTO.getSupplierCode());
            if (count > 0) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("该供应商编码已存在，请勿重复添加");
                return responseEntity;
            }

            // 新增供应商信息
            supplierMapper.insert(supplierDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增供应商信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, SupplierVO supplierVO) {
        logger.info("修改供应商信息入参:{}", JSON.toJSONString(supplierVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateSupplier(supplierVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 检查供应商是否存在
            SupplierVO existSupplier = supplierMapper.get(supplierVO.getId());
            if (existSupplier == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("供应商信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            SupplierDTO supplierDTO = new SupplierDTO();
            BeanUtils.copyProperties(supplierVO, supplierDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            supplierDTO.setUpdatedBy(currentUser);

            // 修改供应商信息
            supplierMapper.update(supplierDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改供应商信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除供应商信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("供应商ID不能为空");
                return responseEntity;
            }

            // 检查供应商是否存在
            SupplierVO existSupplier = supplierMapper.get(id);
            if (existSupplier == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("供应商信息不存在");
                return responseEntity;
            }

            // 删除供应商信息
            supplierMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除供应商信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证供应商信息
     */
    private String validateSupplier(SupplierVO supplierVO, boolean isUpdate) {
        if (supplierVO == null) {
            return "供应商信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(supplierVO.getId())) {
            return "供应商ID不能为空";
        }

        if (StringUtils.isBlank(supplierVO.getSupplierName())) {
            return "供应商名称不能为空";
        }

        if (StringUtils.isBlank(supplierVO.getSupplierCode())) {
            return "供应商编码不能为空";
        }

        if (StringUtils.isBlank(supplierVO.getSupplierType())) {
            return "供应商类型不能为空";
        }

        return null;
    }

    @Override
    public ResponseEntity<Object> generateCode(HttpServletRequest request) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 生成供应商编码，确保唯一性
            String supplierCode = SupplierCodeUtil.generateSupplierCode();
            Integer count = supplierMapper.getByCodeCount(supplierCode);

            // 如果编码已存在，重新生成（最多尝试10次）
            int attempts = 0;
            while (count != null && count > 0 && attempts < 10) {
                supplierCode = SupplierCodeUtil.generateSupplierCode();
                count = supplierMapper.getByCodeCount(supplierCode);
                attempts++;
            }

            if (count != null && count > 0) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("生成编码失败，请重试");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(supplierCode);
            responseEntity.setMsg("生成供应商编码成功");

        } catch (Exception e) {
            logger.error("生成供应商编码异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("生成编码失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> getAll() {
        logger.info("查询所有供应商列表");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 查询所有供应商
            List<SupplierVO> supplierList = supplierMapper.getAll();

            // 转换为Map格式 (key=id, value=supplierName)
            Map<String, String> supplierMap = new HashMap<>();
            if (supplierList != null && !supplierList.isEmpty()) {
                for (SupplierVO supplier : supplierList) {
                    supplierMap.put(supplier.getId(), supplier.getSupplierName());
                }
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(supplierMap);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("查询所有供应商列表异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }
}
