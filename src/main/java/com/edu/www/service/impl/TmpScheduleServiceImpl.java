package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.convert.ScheduleConverter;
import com.edu.www.dto.ScheduleDTO;
import com.edu.www.dto.TmpScheduleDTO;
import com.edu.www.enums.*;
import com.edu.www.enums.EduScheduleSubjectEnum;
import com.edu.www.mapper.ScheduleMapper;
import com.edu.www.mapper.TmpScheduleMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.TmpScheduleService;
import com.edu.www.utils.CommonUtil;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.validates.ParamValidate;
import com.edu.www.vo.ScheduleVO;
import com.edu.www.vo.TmpScheduleVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 临时课程表信息管理
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Service
public class TmpScheduleServiceImpl implements TmpScheduleService {
    private static final Logger logger = LoggerFactory.getLogger(TmpScheduleService.class);

    @Autowired
    private TmpScheduleMapper tmpScheduleMapper;

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询临时课程表信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "临时课程表ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        TmpScheduleVO tmpScheduleVO = tmpScheduleMapper.getById(id);
        // 翻译枚举值为可读文本
        if (tmpScheduleVO != null) {
            if (StringUtils.isNotBlank(tmpScheduleVO.getDepartmentCode())) {
                EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(tmpScheduleVO.getDepartmentCode());
                if (deptEnum != null) {
                    tmpScheduleVO.setDepartment(deptEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(tmpScheduleVO.getGradeCode())) {
                EduGradeCodeEnum gradeEnum = EduGradeCodeEnum.getByKey(tmpScheduleVO.getGradeCode());
                if (gradeEnum != null) {
                    tmpScheduleVO.setGradeCode(gradeEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(tmpScheduleVO.getClassCode())) {
                EduClassCodeEnum classEnum = EduClassCodeEnum.getByKey(tmpScheduleVO.getClassCode());
                if (classEnum != null) {
                    tmpScheduleVO.setClassCode(classEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(tmpScheduleVO.getSemester())) {
                EduSemesterEnum semesterEnum = EduSemesterEnum.getByKey(tmpScheduleVO.getSemester());
                if (semesterEnum != null) {
                    tmpScheduleVO.setSemester(semesterEnum.getDesc());
                }
            }
        }

        responseEntity.setData(tmpScheduleVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询临时课程表信息成功");
        return responseEntity;
    }

    /**
     * 查询临时课程表列表信息
     *
     * @param tmpScheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> query(TmpScheduleVO tmpScheduleVO) {
        tmpScheduleVO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        tmpScheduleVO.setSemester(CommonUtil.getCurrentSemester());

        logger.info("查询临时课程表列表信息入参:{}", JSON.toJSONString(tmpScheduleVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        TmpScheduleDTO tmpScheduleDTO = new TmpScheduleDTO();
        BeanUtils.copyProperties(tmpScheduleVO, tmpScheduleDTO);
        List<TmpScheduleVO> tmpScheduleVOList = tmpScheduleMapper.query(tmpScheduleDTO);

        Map<String, List<TmpScheduleVO>> groupedData = new HashMap<>();

        if (!CollectionUtils.isEmpty(tmpScheduleVOList)) {
            for (TmpScheduleVO vo : tmpScheduleVOList) {
                EduGradeCodeEnum gradeCodeEnum = EduGradeCodeEnum.getByKey(vo.getGradeCode());
                if (gradeCodeEnum != null) {
                    vo.setGradeName(gradeCodeEnum.getDesc());
                }
            }
            // 检查是否所有记录都有classCode或者所有记录都没有classCode
            boolean allHasClassCode = tmpScheduleVOList.stream()
                    .allMatch(vo -> StringUtils.isNotBlank(vo.getClassCode()));

            if (allHasClassCode) {
                // 如果所有记录都有classCode，按照startDate_endDate_classCode分组
                logger.debug("所有记录都有classCode，按startDate_endDate_classCode分组");
                
                // 创建一个新的集合来存储处理后的记录列表
                List<TmpScheduleVO> processedList = new ArrayList<>();
                
                // 处理每个记录，将包含多个班级代码的记录复制到每个班级分组中
                for (TmpScheduleVO vo : tmpScheduleVOList) {
                    if (vo.getStartDate() != null && vo.getEndDate() != null) {
                        if (vo.getClassCode() != null && vo.getClassCode().contains(Constant.SEPARATOR)) {
                            // 如果classCode包含分隔符（如A,B），则拆分成多个记录
                            String[] classCodes = vo.getClassCode().split(Constant.SEPARATOR);
                            for (String classCode : classCodes) {
                                TmpScheduleVO clonedVo = new TmpScheduleVO();
                                BeanUtils.copyProperties(vo, clonedVo);
                                clonedVo.setClassCode(classCode);
                                processedList.add(clonedVo);
                            }
                        } else {
                            // 不包含分隔符的直接添加
                            processedList.add(vo);
                        }
                    }
                }
                
                // 使用处理后的列表进行分组
                groupedData = processedList.stream()
                        .collect(Collectors.groupingBy(
                                vo -> vo.getStartDate().getTime() + Constant.UNDERSCORE +
                                        vo.getEndDate().getTime() + Constant.UNDERSCORE +
                                        vo.getClassCode()
                        ));
            } else {
                // 如果所有记录都没有classCode或者不一致，按照原来的方式分组
                logger.debug("记录的classCode不一致或全为空，按startDate_endDate分组");
                groupedData = tmpScheduleVOList.stream()
                        .filter(vo -> vo.getStartDate() != null && vo.getEndDate() != null)
                        .collect(Collectors.groupingBy(
                                vo -> vo.getStartDate().getTime() + Constant.UNDERSCORE + vo.getEndDate().getTime()
                        ));
            }

            logger.debug("分组后的数据结构: 共{}个分组, 包含{}条记录",
                    groupedData.size(), tmpScheduleVOList.size());

            // 对分组数据按照startDate进行排序（升序）
            Map<String, List<TmpScheduleVO>> sortedData = groupedData.entrySet().stream()
                    .sorted(Map.Entry.<String, List<TmpScheduleVO>>comparingByKey(
                            // 从key中提取startTime并比较
                            (key1, key2) -> {
                                long startTime1 = Long.parseLong(key1.split(Constant.UNDERSCORE)[0]);
                                long startTime2 = Long.parseLong(key2.split(Constant.UNDERSCORE)[0]);
                                return Long.compare(startTime1, startTime2);
                            }))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new  // 使用LinkedHashMap保持排序
                    ));

            groupedData = sortedData;
        }

        responseEntity.setData(groupedData);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询临时课程表列表信息成功");
        return responseEntity;
    }

    /**
     * 新增临时课程表信息
     *
     * @param tmpScheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(TmpScheduleVO tmpScheduleVO) {
        logger.info("新增临时课程表信息入参:{}", JSON.toJSONString(tmpScheduleVO));
        // 参数验证
        ParamValidate.validateInsertParams(tmpScheduleVO);

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 查询课程表数据
            List<ScheduleVO> scheduleVOList = queryScheduleData(tmpScheduleVO);

            if (CollectionUtils.isEmpty(scheduleVOList)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未找到有效的课程数据");
                return responseEntity;
            }

            // 按照星期+时段分组课程
            Map<String, List<ScheduleVO>> weekPeriodGrouped = groupSchedulesByWeekPeriod(scheduleVOList);

            if (weekPeriodGrouped.isEmpty()) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未找到有效的课程数据");
                return responseEntity;
            }

            // 提前计算开始日期和结束日期
            Date startDate = DateUtil.getStartDateByWeekSeq(tmpScheduleVO.getWeekSeq());
            Date endDate = DateUtil.getEndDateByWeekSeq(tmpScheduleVO.getWeekSeq());

            TmpScheduleDTO queryDTO = new TmpScheduleDTO();
            queryDTO.setYear(tmpScheduleVO.getYear());
            queryDTO.setSemester(tmpScheduleVO.getSemester());
            queryDTO.setDepartmentCode(tmpScheduleVO.getDepartmentCode());
            queryDTO.setGradeCode(tmpScheduleVO.getGradeCode());
            queryDTO.setClassCode(tmpScheduleVO.getClassCode());
            queryDTO.setStartDate(startDate);
            queryDTO.setEndDate(endDate);

            int count = tmpScheduleMapper.count(queryDTO);
            if (count > 0) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("该时间段的临时课程表已存在，请勿重复添加");
                return responseEntity;
            }

            String userName = RequestMsgUtil.getSessionUserName();

            // 使用Stream处理组合数据并创建DTO
            List<TmpScheduleDTO> tmpScheduleDTOList = weekPeriodGrouped.entrySet().stream()
                    .filter(entry -> !entry.getValue().isEmpty())
                    .map(entry -> ScheduleConverter.createTmpScheduleDTO(
                            entry.getKey(),
                            entry.getValue(),
                            tmpScheduleVO,
                            startDate,
                            endDate,
                            userName
                    ))
                    .collect(Collectors.toList());

            if (!tmpScheduleDTOList.isEmpty()) {
                // 批量插入数据
                tmpScheduleMapper.batchInsert(tmpScheduleDTOList);
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("新增临时课程表信息成功");
            } else {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未找到有效的课程数据");
            }
        } catch (Exception e) {
            logger.error("新增临时课程表信息失败", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增临时课程表信息失败: " + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 修改临时课程表信息
     *
     * @param tmpScheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(TmpScheduleVO tmpScheduleVO) {
        logger.info("修改临时课程表信息入参:{}", JSON.toJSONString(tmpScheduleVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(tmpScheduleVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(tmpScheduleVO.getDescription()) &&
                tmpScheduleVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        TmpScheduleDTO tmpScheduleDTO = new TmpScheduleDTO();
        BeanUtils.copyProperties(tmpScheduleVO, tmpScheduleDTO);
        tmpScheduleDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        tmpScheduleMapper.update(tmpScheduleDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改临时课程表信息成功");
        return responseEntity;
    }

    /**
     * 批量修改临时课程表信息
     *
     * @param tmpScheduleVOList 临时课程表信息列表
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> batchUpdate(List<TmpScheduleVO> tmpScheduleVOList) {
        logger.info("批量修改临时课程表信息入参:{}", JSON.toJSONString(tmpScheduleVOList));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 参数验证
        if (CollectionUtils.isEmpty(tmpScheduleVOList)) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("临时课程表信息列表不能为空");
            return responseEntity;
        }

        try {
            String userName = RequestMsgUtil.getSessionUserName();

            // 遍历列表，验证每个对象的ID是否为空，并转换为DTO进行更新
            for (TmpScheduleVO tmpScheduleVO : tmpScheduleVOList) {
                // 验证ID是否为空
                if (StringUtils.isBlank(tmpScheduleVO.getId())) {
                    responseEntity.setSuccess(Boolean.FALSE);
                    responseEntity.setMsg("临时课程表ID不能为空");
                    return responseEntity;
                }

                // 验证描述长度
                if (StringUtils.isNotBlank(tmpScheduleVO.getDescription()) &&
                        tmpScheduleVO.getDescription().length() > 255) {
                    responseEntity.setSuccess(Boolean.FALSE);
                    responseEntity.setMsg("描述长度不能超过255个字符");
                    return responseEntity;
                }

                // 转换为DTO并设置更新者
                TmpScheduleDTO tmpScheduleDTO = new TmpScheduleDTO();
                BeanUtils.copyProperties(tmpScheduleVO, tmpScheduleDTO);
                tmpScheduleDTO.setUpdatedBy(userName);

                // 更新操作
                int result = tmpScheduleMapper.update(tmpScheduleDTO);
                if (result <= 0) {
                    logger.error("修改临时课程表信息失败, ID: {}", tmpScheduleVO.getId());
                    responseEntity.setSuccess(Boolean.FALSE);
                    responseEntity.setMsg("修改临时课程表信息失败，ID: " + tmpScheduleVO.getId());
                    return responseEntity;
                }
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("批量修改临时课程表信息成功");
        } catch (Exception e) {
            logger.error("批量修改临时课程表信息失败", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("批量修改临时课程表信息失败: " + e.getMessage());
            // 事务会自动回滚
        }

        return responseEntity;
    }

    /**
     * 查询课程表数据
     *
     * @param tmpScheduleVO 临时课程表VO对象
     * @return 课程表VO列表
     */
    private List<ScheduleVO> queryScheduleData(TmpScheduleVO tmpScheduleVO) {
        ScheduleDTO scheduleDTO = new ScheduleDTO();
        BeanUtils.copyProperties(tmpScheduleVO, scheduleDTO);
        List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);

        if (CollectionUtils.isEmpty(scheduleVOList)) {
            return Collections.emptyList();
        }

        // 获取教师名称并为课程设置教师名称
        Map<String, String> teacherNameMap = commonService.getTeacherName();
        scheduleVOList.forEach(vo -> {
            if (vo.getTeacherId() != null) {
                vo.setTeacherName(teacherNameMap.getOrDefault(vo.getTeacherId(), ""));
            }
            // 翻译科目名称
            if (StringUtils.isNotBlank(vo.getSubjectName())) {
                EduScheduleSubjectEnum subjectEnum = EduScheduleSubjectEnum.getByKey(vo.getSubjectName());
                if (subjectEnum != null) {
                    vo.setSubjectName(subjectEnum.getDesc());
                }
            }
        });

        return scheduleVOList;
    }

    /**
     * 按照星期+时段分组课程
     *
     * @param scheduleVOList 课程表VO列表
     * @return 按照星期和时段分组的课程Map
     */
    private Map<String, List<ScheduleVO>> groupSchedulesByWeekPeriod(List<ScheduleVO> scheduleVOList) {
        Map<String, List<ScheduleVO>> weekPeriodGrouped = new HashMap<>();

        scheduleVOList.stream()
                .filter(vo -> vo.getWeekday() != null && vo.getPeriod() != null &&
                        vo.getSubjectName() != null && CommonConstant.weekdayMap.containsKey(vo.getWeekday()))
                .forEach(vo -> {
                    String day = CommonConstant.weekdayMap.get(vo.getWeekday());
                    String periodKey = Constant.PERIOD + Constant.SPACE + vo.getPeriod();
                    String positionKey = day + Constant.HYPHEN + periodKey;

                    weekPeriodGrouped.computeIfAbsent(positionKey, k -> new ArrayList<>()).add(vo);
                });

        return weekPeriodGrouped;
    }

    /**
     * 置换临时课程表表格内容信息
     *
     * @param masterId 交换的课程表ID
     * @param slaveId  被交换的课程表ID
     * @return 响应结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> swap(String masterId, String slaveId) {
        logger.info("置换临时课程表表格内容信息入参:交换的ID={},被交换的ID={}", masterId, slaveId);

        // 参数验证
        ValidateUtil.paramValidate(StringUtils.isBlank(masterId), "交换的ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(slaveId), "被交换的ID不能为空");
        ValidateUtil.paramValidate(masterId.equals(slaveId), "交换的ID不能与被交换的ID相同");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 获取两个课程表信息
            TmpScheduleVO masterVO = tmpScheduleMapper.getById(masterId);
            TmpScheduleVO slaveVO = tmpScheduleMapper.getById(slaveId);

            // 验证记录是否存在
            if (masterVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("交换的课程表不存在，ID: " + masterId);
                return responseEntity;
            }

            if (slaveVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("被交换的课程表不存在，ID: " + slaveId);
                return responseEntity;
            }

            String userName = RequestMsgUtil.getSessionUserName();
            // 准备交换数据
            String masterContent = masterVO.getContent();
            String slaveContent = slaveVO.getContent();

            // 更新从课程表
            TmpScheduleDTO slaveDTO = new TmpScheduleDTO();
            slaveDTO.setId(slaveId);
            slaveDTO.setContent(masterContent);
            slaveDTO.setUpdatedBy(userName);

            // 更新主课程表
            TmpScheduleDTO masterDTO = new TmpScheduleDTO();
            masterDTO.setId(masterId);
            masterDTO.setContent(slaveContent);
            masterDTO.setUpdatedBy(userName);

            // 执行更新操作
            int slaveResult = tmpScheduleMapper.update(slaveDTO);
            int masterResult = tmpScheduleMapper.update(masterDTO);

            // 验证更新结果
            if (slaveResult <= 0 || masterResult <= 0) {
                logger.error("置换临时课程表失败: 主ID={}, 从ID={}, 主结果={}, 从结果={}",
                        masterId, slaveId, masterResult, slaveResult);
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("置换临时课程表失败");
                return responseEntity;
            }

            logger.info("成功置换临时课程表: 主ID={}, 从ID={}", masterId, slaveId);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("置换临时课程表表格内容信息成功");
        } catch (Exception e) {
            logger.error("置换临时课程表发生异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("置换临时课程表失败: " + e.getMessage());
            // 事务会自动回滚
        }

        return responseEntity;
    }

    /**
     * 根据ID删除临时课程表信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(String id) {
        logger.info("临时课程表信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "临时课程表ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        tmpScheduleMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除临时课程表信息成功");
        return responseEntity;
    }
} 