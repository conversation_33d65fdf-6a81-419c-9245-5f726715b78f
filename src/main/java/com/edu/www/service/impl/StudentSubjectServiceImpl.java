package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.StudentSubjectDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduSubSubjectEnum;
import com.edu.www.enums.EduSubjectTypeEnum;
import com.edu.www.mapper.StudentSubjectMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.StudentSubjectService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.StudentSubjectVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 学生学科管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class StudentSubjectServiceImpl implements StudentSubjectService {
    private static final Logger logger = LoggerFactory.getLogger(StudentSubjectService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private StudentSubjectMapper studentSubjectMapper;

    /**
     * 根据ID查询学生学科信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询学生学科信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "学生学科ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        responseEntity.setData(studentSubjectMapper.get(id));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询学生学科信息成功");
        return responseEntity;
    }

    /**
     * 分页查询学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, StudentSubjectVO studentSubjectVO) {
        logger.info("分页查询学生学科信息入参:{}", JSON.toJSONString(studentSubjectVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(studentSubjectVO.getPageStart(), studentSubjectVO.getPageSize());

        StudentSubjectDTO studentSubjectDTO = new StudentSubjectDTO();
        BeanUtils.copyProperties(studentSubjectVO, studentSubjectDTO);

        List<StudentSubjectVO> studentSubjectDTOList = studentSubjectMapper.query(studentSubjectDTO);
        PageInfo<StudentSubjectVO> pageInfo = new PageInfo<>(studentSubjectDTOList);
        pageInfo.setTotal(new PageInfo<>(studentSubjectDTOList).getTotal());

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询学生学科信息成功");
        return responseEntity;
    }

    /**
     * 新增学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, StudentSubjectVO studentSubjectVO) {
        logger.info("新增学生学科信息入参:{}", JSON.toJSONString(studentSubjectVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(studentSubjectVO.getDescription()) &&
                studentSubjectVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        StudentSubjectDTO studentSubjectDTO = new StudentSubjectDTO();
        BeanUtils.copyProperties(studentSubjectVO, studentSubjectDTO);
        if (StringUtils.isNotBlank(studentSubjectDTO.getSubjectCode())) {
            EduSubSubjectEnum subSubjectEnum = EduSubSubjectEnum.getByKey(studentSubjectDTO.getSubjectCode());
            if (subSubjectEnum != null) {
                studentSubjectDTO.setSubject(subSubjectEnum.getDesc());
            }
        }
        
        // 处理学科分类
        if (StringUtils.isNotBlank(studentSubjectDTO.getSubjectType())) {
            EduSubjectTypeEnum subjectTypeEnum = EduSubjectTypeEnum.getByKey(studentSubjectDTO.getSubjectType());
            if (subjectTypeEnum != null) {
                // 如果需要，可以在这里做额外处理
            }
        }
        
        String userName = RequestMsgUtil.getSessionUserName();
        studentSubjectDTO.setCreatedBy(userName);
        studentSubjectDTO.setUpdatedBy(userName);
        studentSubjectMapper.insert(studentSubjectDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增学生学科信息成功");
        return responseEntity;
    }

    /**
     * 修改学生学科信息
     *
     * @param studentSubjectVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, StudentSubjectVO studentSubjectVO) {
        logger.info("修改学生学科信息入参:{}", JSON.toJSONString(studentSubjectVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(studentSubjectVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(studentSubjectVO.getDescription()) &&
                studentSubjectVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }
        StudentSubjectDTO studentSubjectDTO = new StudentSubjectDTO();
        BeanUtils.copyProperties(studentSubjectVO, studentSubjectDTO);
        if (StringUtils.isNotBlank(studentSubjectDTO.getSubjectCode())) {
            EduSubSubjectEnum subSubjectEnum = EduSubSubjectEnum.getByKey(studentSubjectDTO.getSubjectCode());
            if (subSubjectEnum != null) {
                studentSubjectDTO.setSubject(subSubjectEnum.getDesc());
            }
        }
        
        // 处理学科分类
        if (StringUtils.isNotBlank(studentSubjectDTO.getSubjectType())) {
            EduSubjectTypeEnum subjectTypeEnum = EduSubjectTypeEnum.getByKey(studentSubjectDTO.getSubjectType());
            if (subjectTypeEnum != null) {
                // 如果需要，可以在这里做额外处理
            }
        }
        
        String userName = RequestMsgUtil.getSessionUserName();
        studentSubjectDTO.setUpdatedBy(userName);
        studentSubjectDTO.setUpdatedAt(new Date());

        studentSubjectMapper.update(studentSubjectDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改学生学科信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除学生学科信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("学生学科信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "学生学科ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }
        studentSubjectMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除学生学科信息成功");
        return responseEntity;
    }
} 