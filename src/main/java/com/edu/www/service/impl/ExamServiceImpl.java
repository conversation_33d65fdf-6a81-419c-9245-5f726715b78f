package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.convert.ExamConverter;
import com.edu.www.dto.ExamDTO;
import com.edu.www.dto.StudentSubjectDTO;
import com.edu.www.enums.*;
import com.edu.www.po.CompositionInfoPO;
import com.edu.www.mapper.ExamMapper;
import com.edu.www.mapper.StudentSubjectMapper;
import com.edu.www.po.SubjectInfoPO;
import com.edu.www.service.CommonService;
import com.edu.www.service.ExamService;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 考试信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class ExamServiceImpl implements ExamService {
    private static final Logger logger = LoggerFactory.getLogger(ExamService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private ExamMapper examMapper;

    @Autowired
    private StudentSubjectMapper studentSubjectMapper;

    /**
     * 根据ID查询考试信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询考试信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "考试信息ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        ExamVO examVO = examMapper.get(id);
        if (examVO != null) {
            // 将逗号分隔的巡考人字符串转换为List
            ExamConverter.convertInspectorToList(examVO);
        }

        responseEntity.setData(examVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询考试信息成功");
        return responseEntity;
    }

    /**
     * 分页查询考试信息
     *
     * @param examVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, ExamVO examVO) {
        logger.info("分页查询考试信息入参:{}", JSON.toJSONString(examVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(examVO.getPageStart(), examVO.getPageSize());

        ExamDTO examDTO = new ExamDTO();
        BeanUtils.copyProperties(examVO, examDTO);

        // 如果传入多个巡考人，将List转换为逗号分隔的字符串用于查询
        ExamConverter.convertInspectorsToString(examDTO);

        List<ExamVO> examVOList = examMapper.query(examDTO);

        // 处理查询结果，将逗号分隔的巡考人字符串转换为List
        if (examVOList != null && !examVOList.isEmpty()) {
            for (ExamVO vo : examVOList) {
                // 将逗号分隔的巡考人字符串转换为List
                ExamConverter.convertInspectorToList(vo);
            }
        }

        PageInfo<ExamVO> pageInfo = new PageInfo<>(examVOList);
        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询考试信息成功");
        return responseEntity;
    }

    /**
     * 新增考试信息
     *
     * @param examVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, ExamVO examVO) {
        logger.info("新增考试信息入参:{}", JSON.toJSONString(examVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(examVO.getTitle()) &&
                examVO.getTitle().length() > 255, "考试标题长度不能超过255个字符");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(examVO.getDescription()) &&
                examVO.getDescription().length() > 255, "备注长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        ExamDTO examDTO = new ExamDTO();
        BeanUtils.copyProperties(examVO, examDTO);

        // 将巡考人List转换为逗号分隔的字符串用于存储
        ExamConverter.convertInspectorsToString(examDTO);

        // 处理 考生人数
        Integer examCandidate = examDTO.getExamCandidate();
        if (Objects.isNull(examCandidate) || NumberUtils.INTEGER_ZERO >= examCandidate) {
            examDTO.setExamCandidate(getExamCandidate(examDTO));
        }

        // 处理 总时长
        Integer totalDuration = examDTO.getTotalDuration();
        if (Objects.isNull(totalDuration) || NumberUtils.INTEGER_ZERO >= totalDuration) {
            examDTO.setTotalDuration(ExamConverter.calTotalDuration(examDTO));
        }

        Map<String, String> map = CommonConstant.weekMap;
        examDTO.setWeekday(map.get(DateUtil.getDayWeekName(examDTO.getExamDate())));
        String userName = RequestMsgUtil.getSessionUserName();
        examDTO.setCreatedBy(userName);
        examDTO.setUpdatedBy(userName);

        examMapper.insert(examDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增考试信息成功");
        return responseEntity;
    }

    /**
     * 修改考试信息
     *
     * @param examVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, ExamVO examVO) {
        logger.info("修改考试信息入参:{}", JSON.toJSONString(examVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(examVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(examVO.getTitle()) &&
                examVO.getTitle().length() > 255, "考试标题长度不能超过255个字符");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(examVO.getDescription()) &&
                examVO.getDescription().length() > 255, "备注长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        ExamDTO examDTO = new ExamDTO();
        BeanUtils.copyProperties(examVO, examDTO);

        // 将巡考人List转换为逗号分隔的字符串用于存储
        ExamConverter.convertInspectorsToString(examDTO);

        // 更新 重新计算 考生人数
        examDTO.setExamCandidate(getExamCandidate(examDTO));

        // 更新 重新计算总时长
        examDTO.setTotalDuration(ExamConverter.calTotalDuration(examDTO));

        Map<String, String> map = CommonConstant.weekMap;
        examDTO.setWeekday(map.get(DateUtil.getDayWeekName(examDTO.getExamDate())));
        String userName = RequestMsgUtil.getSessionUserName();
        examDTO.setUpdatedBy(userName);

        examMapper.update(examDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改考试信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除考试信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("考试信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "考试信息ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        examMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除考试信息成功");
        return responseEntity;
    }

    /**
     * 生成打印标签贴
     *
     * @param request
     * @param ids
     * @return
     */
    @Override
    public ResponseEntity<Object> examTag(HttpServletRequest request, List<String> ids) {
        logger.info("生成打印标签贴信息入参:{}", JSON.toJSONString(ids));
        ValidateUtil.paramValidate(CollectionUtils.isEmpty(ids), "考试信息ID不能为空");
        ValidateUtil.paramValidate(ids.size() > 5, "最多只能选择5条考试信息");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无打印权限");
            return responseEntity;
        }

        List<ExamTagVO> examTagVOList = new ArrayList<>();

        List<ExamVO> examVOList = examMapper.getByIds(ids);
        if (CollectionUtils.isEmpty(examVOList)) {
            responseEntity.setData(examTagVOList);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("生成打印标签贴信息成功");
            return responseEntity;
        }
        // 类型转换
        examTagVOList = ExamConverter.examVOList2ExamTagVOList(examVOList);

        responseEntity.setData(examTagVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("生成打印标签贴信息成功");
        return responseEntity;
    }

    /**
     * 监考时长汇总
     *
     * @param request
     * @param examVO
     * @return
     */
    @Override
    public ResponseEntity<Object> duration(HttpServletRequest request, ExamVO examVO) {
        logger.info("监考时长汇总查询入参:{}", JSON.toJSONString(examVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(examVO.getYear()), "学年不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(examVO.getSemester()), "学期不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        // 获取教师信息映射
        Map<String, TeacherVO> teacherMap = commonService.getTeacherInfo();

        // 查询考试数据
        ExamDTO examDTO = new ExamDTO();
        BeanUtils.copyProperties(examVO, examDTO);
        List<ExamVO> examVOList = examMapper.query(examDTO);

        if (CollectionUtils.isEmpty(examVOList)) {
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("查询监考时长汇总成功");
            responseEntity.setData(new PageInfo<>(new ArrayList<>()));
            return responseEntity;
        }

        // 统计每位监考人在各种考试类型下的监考时长
        Map<String, ExamDurationVO> durationMap = ExamConverter.calDuration(examVOList, teacherMap, examVO);

        // 转换为列表并分页
        List<ExamDurationVO> durationList = new ArrayList<>(durationMap.values());

        // 按监考总时长降序排列
        durationList.sort((a, b) -> {
            Integer totalA = a.getTotalDuration() != null ? a.getTotalDuration() : 0;
            Integer totalB = b.getTotalDuration() != null ? b.getTotalDuration() : 0;
            return totalB.compareTo(totalA);
        });

        // 分页处理
        PageHelper.startPage(examVO.getPageStart(), examVO.getPageSize());
        PageInfo<ExamDurationVO> pageInfo = new PageInfo<>(durationList);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询监考时长汇总成功");
        responseEntity.setData(pageInfo);

        logger.info("监考时长汇总查询完成，共{}条记录", durationList.size());
        return responseEntity;
    }

    /**
     * 监考时长详情
     *
     * @param request
     * @param invigilatorId
     * @return
     */
    @Override
    public ResponseEntity<Object> durationDetail(HttpServletRequest request, String invigilatorId) {
        logger.info("监考人ID:{}", invigilatorId);
        ValidateUtil.paramValidate(StringUtils.isBlank(invigilatorId), "监考人ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询详情权限");
            return responseEntity;
        }
        List<DurationDetailVO> durationDetailList = new ArrayList<>();
        // 查询该监考人的考试记录
        ExamDTO examDTO = new ExamDTO();
        examDTO.setInvigilator(invigilatorId);
        List<ExamVO> examVOList = examMapper.query(examDTO);

        if (CollectionUtils.isEmpty(examVOList)) {
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("未找到该监考人的考试记录");
            responseEntity.setData(durationDetailList);
            return responseEntity;
        }

        // 转换为DurationDetailVO列表
        durationDetailList = ExamConverter.convertToDetailVOList(examVOList);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询监考时长详情成功");
        responseEntity.setData(durationDetailList);
        return responseEntity;
    }

    /**
     * 获取需要参考考试学生的数量
     *
     * @param examDTO
     * @return
     */
    private int getExamCandidate(ExamDTO examDTO) {
        int examCandidateCount = 0; // 累加器，用于统计总的学生数量

        // 解析考试组成信息，用于计算考生人数
        String compositionInfo = examDTO.getCompositionInfo();
        if (StringUtils.isNotBlank(compositionInfo)) {
            try {
                // 解析JSON为CompositionInfoPO对象列表
                List<CompositionInfoPO> compositionList = JSON.parseArray(compositionInfo, CompositionInfoPO.class);

                // 根据考试组成信息查询对应的学生
                if (!CollectionUtils.isEmpty(compositionList)) {
                    // 循环每个考试组成信息，分别查询学生数量并累加
                    for (CompositionInfoPO composition : compositionList) {
                        StudentSubjectDTO studentSubjectDTO = new StudentSubjectDTO();
                        studentSubjectDTO.setSubjectCode(composition.getSubjectCode());
                        studentSubjectDTO.setSubjectType(composition.getSubjectType());
                        studentSubjectDTO.setSubjectLevel(composition.getSubjectLevel());

                        // 查询当前组成信息对应的学生列表
                        List<StudentSubjectVO> studentSubjectVOList = studentSubjectMapper.query(studentSubjectDTO);

                        // 累加当前查询结果的数量
                        int currentCount = studentSubjectVOList != null ? studentSubjectVOList.size() : 0;
                        examCandidateCount += currentCount;
                    }
                }
            } catch (Exception e) {
                logger.warn("解析考试组成信息失败: {}", compositionInfo, e);
            }
        }

        logger.info("考试组成信息总计学生数量: {}", examCandidateCount);
        return examCandidateCount; // 返回累加后的总数量
    }
}