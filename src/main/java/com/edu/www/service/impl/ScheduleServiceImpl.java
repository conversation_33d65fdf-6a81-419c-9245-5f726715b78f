package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.dto.ScheduleDTO;
import com.edu.www.dto.SubjectDTO;
import com.edu.www.enums.*;
import com.edu.www.mapper.ScheduleMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.ScheduleService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.ScheduleVO;
import com.edu.www.vo.SubjectVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程表信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class ScheduleServiceImpl implements ScheduleService {
    private static final Logger logger = LoggerFactory.getLogger(ScheduleService.class);

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询课程表信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "课程表ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        ScheduleVO scheduleVO = scheduleMapper.getById(id);
        // 翻译枚举值为可读文本
        if (scheduleVO != null) {
            if (StringUtils.isNotBlank(scheduleVO.getDepartmentCode())) {
                EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(scheduleVO.getDepartmentCode());
                if (deptEnum != null) {
                    scheduleVO.setDepartment(deptEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getGradeCode())) {
                EduGradeCodeEnum gradeEnum = EduGradeCodeEnum.getByKey(scheduleVO.getGradeCode());
                if (gradeEnum != null) {
                    scheduleVO.setGradeCode(gradeEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getClassCode())) {
                EduClassCodeEnum classEnum = EduClassCodeEnum.getByKey(scheduleVO.getClassCode());
                if (classEnum != null) {
                    scheduleVO.setClassCode(classEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getSubjectLevel())) {
                EduSubjectLevelEnum subjectLevelEnum = EduSubjectLevelEnum.getByKey(scheduleVO.getSubjectLevel());
                if (subjectLevelEnum != null) {
                    scheduleVO.setSubjectLevel(subjectLevelEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getWeekday())) {
                EduWeekdayEnum weekdayEnum = EduWeekdayEnum.getByKey(scheduleVO.getWeekday());
                if (weekdayEnum != null) {
                    scheduleVO.setWeekday(weekdayEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getDayPart())) {
                EduDayPartEnum dayPartEnum = EduDayPartEnum.getByKey(scheduleVO.getDayPart());
                if (dayPartEnum != null) {
                    scheduleVO.setDayPart(dayPartEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getPeriod())) {
                EduPeriodEnum periodEnum = EduPeriodEnum.getByKey(scheduleVO.getPeriod());
                if (periodEnum != null) {
                    scheduleVO.setPeriod(periodEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getSemester())) {
                EduSemesterEnum semesterEnum = EduSemesterEnum.getByKey(scheduleVO.getSemester());
                if (semesterEnum != null) {
                    scheduleVO.setSemester(semesterEnum.getDesc());
                }
            }

            if (StringUtils.isNotBlank(scheduleVO.getClassCode())) {
                EduClassCodeEnum classCodeEnum = EduClassCodeEnum.getByKey(scheduleVO.getClassCode());
                if (classCodeEnum != null) {
                    scheduleVO.setClassCode(classCodeEnum.getDesc());
                }
            }
        }
        convertClassCodeToList(scheduleVO);
        responseEntity.setData(scheduleVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询课程表信息成功");
        return responseEntity;
    }

    /**
     * 分页查询课程表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, ScheduleVO scheduleVO) {
        logger.info("分页查询课程表信息入参:{}", JSON.toJSONString(scheduleVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(scheduleVO.getPageStart(), scheduleVO.getPageSize());
        ScheduleDTO scheduleDTO = new ScheduleDTO();
        BeanUtils.copyProperties(scheduleVO, scheduleDTO);
        convertClassCodeToString(scheduleDTO);
        List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);
        // 翻译枚举值为可读文本
        if (scheduleVOList != null && !scheduleVOList.isEmpty()) {
            for (ScheduleVO vo : scheduleVOList) {
                if (StringUtils.isNotBlank(vo.getDepartmentCode())) {
                    EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(vo.getDepartmentCode());
                    if (deptEnum != null) {
                        vo.setDepartmentCode(deptEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getGradeCode())) {
                    EduGradeCodeEnum gradeEnum = EduGradeCodeEnum.getByKey(vo.getGradeCode());
                    if (gradeEnum != null) {
                        vo.setGradeCode(gradeEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getClassCode())) {
                    EduClassCodeEnum classEnum = EduClassCodeEnum.getByKey(vo.getClassCode());
                    if (classEnum != null) {
                        vo.setClassCode(classEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getSubjectLevel())) {
                    EduSubjectLevelEnum subjectLevelEnum = EduSubjectLevelEnum.getByKey(vo.getSubjectLevel());
                    if (subjectLevelEnum != null) {
                        vo.setSubjectLevel(subjectLevelEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getWeekday())) {
                    EduWeekdayEnum weekdayEnum = EduWeekdayEnum.getByKey(vo.getWeekday());
                    if (weekdayEnum != null) {
                        vo.setWeekday(weekdayEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getDayPart())) {
                    EduDayPartEnum dayPartEnum = EduDayPartEnum.getByKey(vo.getDayPart());
                    if (dayPartEnum != null) {
                        vo.setDayPart(dayPartEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getPeriod())) {
                    EduPeriodEnum periodEnum = EduPeriodEnum.getByKey(vo.getPeriod());
                    if (periodEnum != null) {
                        vo.setPeriod(periodEnum.getDesc());
                    }
                }

                if (StringUtils.isNotBlank(vo.getSemester())) {
                    EduSemesterEnum semesterEnum = EduSemesterEnum.getByKey(vo.getSemester());
                    if (semesterEnum != null) {
                        vo.setSemester(semesterEnum.getDesc());
                    }
                }

                convertClassCodeToList(vo);
            }
        }

        PageInfo<ScheduleVO> pageInfo = new PageInfo(scheduleVOList);

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询课程表信息成功");
        return responseEntity;
    }

    /**
     * 个人课表 - 按教师名称和星期分组
     *
     * @param scheduleVO 查询参数
     * @return 课表信息，按教师名称和星期组织
     */
    @Override
    public ResponseEntity<Object> self(ScheduleVO scheduleVO) {
        try {
            // 基础准备工作
            ScheduleDTO scheduleDTO = new ScheduleDTO();
            BeanUtils.copyProperties(scheduleVO, scheduleDTO);

            // 并行获取所需数据
            Map<String, String> teacherNameMap = commonService.getTeacherName();
            List<ScheduleVO> rawScheduleList = scheduleMapper.query(scheduleDTO);

            if (CollectionUtils.isEmpty(rawScheduleList)) {
                logger.info("未查询到课表数据");
                ResponseEntity<Object> emptyResponse = new ResponseEntity<>();
                emptyResponse.setData(Collections.emptyMap());
                emptyResponse.setSuccess(Boolean.TRUE);
                emptyResponse.setMsg("未查询到课表数据");
                return emptyResponse;
            }

            // 预处理数据 - 设置教师名称和翻译年级代码
            List<ScheduleVO> processedSchedules = processScheduleData(rawScheduleList, teacherNameMap);

            // 构建二级嵌套结构
            Map<String, Map<String, List<ScheduleVO>>> result = buildNestedStructure(processedSchedules);

            ResponseEntity<Object> responseEntity = new ResponseEntity<>();
            responseEntity.setData(result);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("获取个人课表信息成功");
            return responseEntity;
        } catch (Exception e) {
            logger.error("查询个人课表异常: ", e);
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("查询个人课表异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 自习课表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> selfStudy(ScheduleVO scheduleVO) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 获取前端已筛选好的数据和教师名称映射
        ScheduleDTO scheduleDTO = new ScheduleDTO();
        BeanUtils.copyProperties(scheduleVO, scheduleDTO);
        List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);
        Map<String, String> map = commonService.getTeacherName();

        // 一次性创建所有星期的结构
        Map<String, List<ScheduleVO>> weekSchedule = CommonConstant.orderedDays.stream().collect(java.util.stream.Collectors.toMap(day -> day, day -> new ArrayList<>(), (v1, v2) -> v1, LinkedHashMap::new));

        // 处理课程数据并分组到对应星期
        scheduleVOList.stream().filter(vo -> vo.getWeekday() != null && CommonConstant.weekdayMap.containsKey(vo.getWeekday())).peek(vo -> {
            // 在流中设置教师名称
            if (vo.getTeacherId() != null && map.containsKey(vo.getTeacherId())) {
                vo.setTeacherName(map.get(vo.getTeacherId()));
            }

            // 翻译科目名称
            if (StringUtils.isNotBlank(vo.getSubjectName())) {
                EduScheduleSubjectEnum subjectEnum = EduScheduleSubjectEnum.getByKey(vo.getSubjectName());
                if (subjectEnum != null) {
                    vo.setSubjectName(subjectEnum.getDesc());
                }
            }
        }).forEach(vo -> {
            String day = CommonConstant.weekdayMap.get(vo.getWeekday());
            weekSchedule.get(day).add(vo);
        });

        // 为所有星期同时进行排序
        weekSchedule.forEach((day, courses) -> courses.sort(Comparator.comparing(ScheduleVO::getPeriod)));

        // 设置响应内容
        responseEntity.setData(weekSchedule);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("获取自习课表信息成功");
        return responseEntity;
    }


    /**
     * 春季班课表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> spring(ScheduleVO scheduleVO) {
        try {
            // 获取课表数据
            ScheduleDTO scheduleDTO = new ScheduleDTO();
            BeanUtils.copyProperties(scheduleVO, scheduleDTO);
            convertClassCodeToString(scheduleDTO);
            List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);

            if (CollectionUtils.isEmpty(scheduleVOList)) {
                ResponseEntity<Object> emptyResponse = new ResponseEntity<>();
                emptyResponse.setData(Collections.emptyMap());
                emptyResponse.setSuccess(Boolean.TRUE);
                emptyResponse.setMsg("未查询到春季班课表信息");
                return emptyResponse;
            }

            // 获取教师名称并为课程设置教师名称
            Map<String, String> teacherNameMap = commonService.getTeacherName();
            scheduleVOList.forEach(vo -> {
                if (vo.getTeacherId() != null) {
                    vo.setTeacherName(teacherNameMap.getOrDefault(vo.getTeacherId(), ""));
                }
                // 翻译科目名称
                if (StringUtils.isNotBlank(vo.getSubjectName())) {
                    EduScheduleSubjectEnum subjectEnum = EduScheduleSubjectEnum.getByKey(vo.getSubjectName());
                    if (subjectEnum != null) {
                        vo.setSubjectName(subjectEnum.getDesc());
                    }
                }
                // 确保班级编码被转换为List
                convertClassCodeToList(vo);
            });

            // 初始化结果结构
            Map<String, List<Map<String, Map<String, List<ScheduleVO>>>>> result = initializeResultStructure();

            // 处理并组织课程数据
            processAndOrganizeCourses(scheduleVOList, result);

            ResponseEntity<Object> successResponse = new ResponseEntity<>();
            successResponse.setData(result);
            successResponse.setSuccess(Boolean.TRUE);
            successResponse.setMsg("获取春季班课表信息成功");
            return successResponse;
        } catch (Exception e) {
            logger.error("获取春季班课表异常: ", e);
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("获取春季班课表异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 10年级课表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> yearTen(ScheduleVO scheduleVO) {
        return getYearSchedule(scheduleVO);
    }

    /**
     * 11年级课表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> yearEleven(ScheduleVO scheduleVO) {
        return getYearSchedule(scheduleVO);
    }

    /**
     * 12年级课表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> yearTwelve(ScheduleVO scheduleVO) {
        return getYearSchedule(scheduleVO);
    }

    /**
     * 通用年级课表信息处理方法
     *
     * @param scheduleVO 查询参数
     * @return 课表信息
     */
    private ResponseEntity<Object> getYearSchedule(ScheduleVO scheduleVO) {
        try {
            // 获取课表数据
            ScheduleDTO scheduleDTO = new ScheduleDTO();
            BeanUtils.copyProperties(scheduleVO, scheduleDTO);
            convertClassCodeToString(scheduleDTO);
            List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);

            if (CollectionUtils.isEmpty(scheduleVOList)) {
                ResponseEntity<Object> emptyResponse = new ResponseEntity<>();
                emptyResponse.setData(Collections.emptyMap());
                emptyResponse.setSuccess(Boolean.TRUE);
                emptyResponse.setMsg("未查询到" + scheduleVO.getGradeCode() + "年级课表信息");
                return emptyResponse;
            }

            // 获取教师名称并为课程设置教师名称
            Map<String, String> teacherNameMap = commonService.getTeacherName();
            scheduleVOList.forEach(vo -> {
                if (vo.getTeacherId() != null) {
                    vo.setTeacherName(teacherNameMap.getOrDefault(vo.getTeacherId(), ""));
                }
                // 翻译科目名称
                if (StringUtils.isNotBlank(vo.getSubjectName())) {
                    EduScheduleSubjectEnum subjectEnum = EduScheduleSubjectEnum.getByKey(vo.getSubjectName());
                    if (subjectEnum != null) {
                        vo.setSubjectName(subjectEnum.getDesc());
                    }
                }
                // 确保班级编码被转换为List
                convertClassCodeToList(vo);
            });

            // 初始化结果结构 - 使用相同的星期->时段结构
            Map<String, List<Map<String, Map<String, List<ScheduleVO>>>>> result = initializeResultStructure();

            // 建立临时分组映射：星期+时段 -> 所有课程列表
            Map<String, List<ScheduleVO>> tempGrouping = new HashMap<>();

            // 第一步：按星期、时段分组课程
            scheduleVOList.stream().filter(vo -> vo.getWeekday() != null && vo.getPeriod() != null && vo.getSubjectName() != null && CommonConstant.weekdayMap.containsKey(vo.getWeekday())).forEach(vo -> {
                String day = CommonConstant.weekdayMap.get(vo.getWeekday());
                String periodKey = Constant.PERIOD + Constant.SPACE + vo.getPeriod();
                String groupKey = day + Constant.HYPHEN + periodKey;

                tempGrouping.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(vo);
            });

            // 第二步：处理每个星期和时段组
            tempGrouping.forEach((groupKey, courseList) -> {
                String[] parts = groupKey.split(Constant.HYPHEN);
                String day = parts[0];
                String periodKey = parts[1];

                // 找到对应的日期容器
                List<Map<String, Map<String, List<ScheduleVO>>>> dayContainer = result.get(day);
                if (!CollectionUtils.isEmpty(dayContainer)) {
                    Map<String, Map<String, List<ScheduleVO>>> periodContainer = dayContainer.get(0);
                    Map<String, List<ScheduleVO>> periodData = periodContainer.get(periodKey);

                    if (periodData != null) {
                        // 收集时段下所有不重复的科目名称
                        List<String> uniqueSubjects = courseList.stream().map(ScheduleVO::getSubjectName).distinct().collect(Collectors.toList());

                        // 所有科目名称拼接，不再特殊处理Self-study
                        String combinedSubjectKey = uniqueSubjects.stream().sorted().collect(Collectors.joining(Constant.SLASH));

                        // 12年级的处理逻辑比较简单，无需处理不同班级的课程
                        if ("12".equals(scheduleVO.getGradeCode())) {
                            processCoursesWithoutClassSplit(combinedSubjectKey, courseList, periodData);
                            return;
                        }

                        // 10年级和11年级的处理逻辑，需要处理班级拆分
                        // 如果组合科目名称包含斜杠，说明是第三级分类，需要根据classCode进行拆分
                        if (combinedSubjectKey.contains(Constant.SLASH)) {
                            // 按班级代码分组
                            Map<String, List<ScheduleVO>> classCourses = new HashMap<>();

                            // 将有班级代码的课程按班级分组
                            courseList.forEach(course -> {
                                if (StringUtils.isNotBlank(course.getClassCode())) {
                                    // 修改此处，处理包含多个班级代码的情况
                                    String[] classCodes = course.getClassCode().split(Constant.SEPARATOR);
                                    for (String code : classCodes) {
                                        // 为每个班级代码添加课程
                                        classCourses.computeIfAbsent(code, k -> new ArrayList<>()).add(course);
                                    }
                                }
                            });

                            // 如果存在多个班级，则进行班级特定的处理
                            if (classCourses.size() > 1) {
                                // 拆分组合科目名称
                                String[] subjectParts = combinedSubjectKey.split(Constant.SLASH);

                                // 处理每个班级
                                classCourses.forEach((classCode, courses) -> {
                                    // 根据班级代码选择对应的科目部分
                                    String subjectKey;

                                    if ("A".equals(classCode) && subjectParts.length > 1) {
                                        // A班使用第二部分科目（按字母排序后的）
                                        subjectKey = subjectParts[1];
                                    } else if ("B".equals(classCode) && subjectParts.length > 0) {
                                        // B班使用第一部分科目
                                        subjectKey = subjectParts[0];
                                    } else {
                                        // 其他班级使用完整的组合名称
                                        subjectKey = combinedSubjectKey;
                                    }

                                    // 确保将课程按照它们的实际科目名称归类到正确的类别中
                                    Map<String, List<ScheduleVO>> coursesByActualSubject = new HashMap<>();

                                    // 按照实际科目名称分组，不再特殊处理Self-study
                                    courses.forEach(course -> {
                                        String actualSubject = course.getSubjectName();
                                        if (StringUtils.isNotBlank(actualSubject)) {
                                            coursesByActualSubject.computeIfAbsent(actualSubject, k -> new ArrayList<>()).add(course);
                                        }
                                    });

                                    // 存储该班级的课程，确保使用正确的科目键
                                    coursesByActualSubject.forEach((actualSubject, subjectCourses) -> {
                                        // 对于特定班级，使用其对应的科目键
                                        String finalSubjectKey = subjectKey;
                                        // 如果是多班合班课程，保留原始科目名称
                                        if (subjectCourses.stream().anyMatch(c -> c.getClassCode() != null && c.getClassCode().contains(Constant.SEPARATOR))) {
                                            finalSubjectKey = actualSubject;
                                        }
                                        periodData.put(finalSubjectKey, new ArrayList<>(subjectCourses));
                                    });
                                });

                                // 处理没有班级代码的课程
                                List<ScheduleVO> noClassCodeCourses = courseList.stream()
                                    .filter(course -> StringUtils.isBlank(course.getClassCode()))
                                    .collect(Collectors.toList());

                                if (!noClassCodeCourses.isEmpty()) {
                                    periodData.put(combinedSubjectKey, noClassCodeCourses);
                                }

                                return; // 直接返回，不执行后续代码
                            }
                        }

                        // 对于没有班级划分的课程，按照标准方式处理
                        processCoursesWithoutClassSplit(combinedSubjectKey, courseList, periodData);
                    }
                }
            });

            // 第三步：清理没有数据的时段
            cleanEmptyPeriods(result);

            ResponseEntity<Object> successResponse = new ResponseEntity<>();
            successResponse.setData(result);
            successResponse.setSuccess(Boolean.TRUE);
            successResponse.setMsg("获取" + scheduleVO.getGradeCode() + "年级课表信息成功");
            return successResponse;
        } catch (Exception e) {
            logger.error("获取" + scheduleVO.getGradeCode() + "年级课表异常: ", e);
            ResponseEntity<Object> errorResponse = new ResponseEntity<>();
            errorResponse.setSuccess(Boolean.FALSE);
            errorResponse.setMsg("获取" + scheduleVO.getGradeCode() + "年级课表异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 处理不需要班级拆分的课程
     *
     * @param combinedSubjectKey 合并后的科目键
     * @param courseList         课程列表
     * @param periodData         时段数据容器
     */
    private void processCoursesWithoutClassSplit(String combinedSubjectKey, List<ScheduleVO> courseList, Map<String, List<ScheduleVO>> periodData) {
        if (!combinedSubjectKey.isEmpty()) {
            // 按科目名称分组，不再特殊处理Self-study
            Map<String, List<ScheduleVO>> coursesBySubject = courseList.stream().collect(Collectors.groupingBy(ScheduleVO::getSubjectName, Collectors.toList()));

            // 将课程按实际科目名称添加到结果中
            coursesBySubject.forEach((subjectName, courses) -> {
                periodData.put(subjectName, new ArrayList<>(courses));
            });
        }
    }

    /**
     * 清理空的时段
     *
     * @param result 结果数据结构
     */
    private void cleanEmptyPeriods(Map<String, List<Map<String, Map<String, List<ScheduleVO>>>>> result) {
        result.values().stream().filter(dayContainer -> !CollectionUtils.isEmpty(dayContainer)).forEach(dayContainer -> {
            Map<String, Map<String, List<ScheduleVO>>> periodContainer = dayContainer.get(0);

            // 收集空时段并移除
            periodContainer.entrySet().stream().filter(entry -> entry.getValue().isEmpty()).map(Map.Entry::getKey).collect(Collectors.toList()).forEach(periodContainer::remove);
        });
    }

    /**
     * 新增课程表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, ScheduleVO scheduleVO) {
        logger.info("新增课程表信息入参:{}", JSON.toJSONString(scheduleVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(scheduleVO.getDescription()) && scheduleVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        ScheduleDTO scheduleDTO = new ScheduleDTO();
        BeanUtils.copyProperties(scheduleVO, scheduleDTO);
        convertClassCodeToString(scheduleDTO);
        if (StringUtils.isNotBlank(scheduleDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(scheduleDTO.getDepartmentCode());
            if (deptEnum != null) {
                scheduleDTO.setDepartment(deptEnum.getDesc());
            }
        }
        String userName = RequestMsgUtil.getSessionUserName();
        scheduleDTO.setCreatedBy(userName);
        scheduleDTO.setUpdatedBy(userName);
        scheduleMapper.insert(scheduleDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增课程表信息成功");
        return responseEntity;
    }

    /**
     * 修改课程表信息
     *
     * @param scheduleVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, ScheduleVO scheduleVO) {
        logger.info("修改课程表信息入参:{}", JSON.toJSONString(scheduleVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(scheduleVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(scheduleVO.getDescription()) && scheduleVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        ScheduleDTO scheduleDTO = new ScheduleDTO();
        BeanUtils.copyProperties(scheduleVO, scheduleDTO);
        convertClassCodeToString(scheduleDTO);
        if (StringUtils.isNotBlank(scheduleDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(scheduleDTO.getDepartmentCode());
            if (deptEnum != null) {
                scheduleDTO.setDepartment(deptEnum.getDesc());
            }
        }
        if(CollectionUtils.isEmpty(scheduleDTO.getClassCodes())){
            scheduleDTO.setClassCode("");
        }
        String userName = RequestMsgUtil.getSessionUserName();
        scheduleDTO.setUpdatedBy(userName);
        scheduleMapper.update(scheduleDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改课程表信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除课程表信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("课程表信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "课程表ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        scheduleMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除课程表信息成功");
        return responseEntity;
    }

    /**
     * 处理课表数据，设置教师名称和年级描述
     */
    private List<ScheduleVO> processScheduleData(List<ScheduleVO> scheduleList, Map<String, String> teacherNameMap) {
        return scheduleList.stream().filter(Objects::nonNull).peek(vo -> {
            // 设置教师名称
            if (vo.getTeacherId() != null && teacherNameMap.containsKey(vo.getTeacherId())) {
                vo.setTeacherName(teacherNameMap.get(vo.getTeacherId()));
            }

            // 翻译年级编码
            if (StringUtils.isNotBlank(vo.getGradeCode())) {
                EduGradeCodeEnum gradeEnum = EduGradeCodeEnum.getByKey(vo.getGradeCode());
                if (gradeEnum != null) {
                    vo.setGradeCode(gradeEnum.getDesc());
                }
            }

            // 翻译科目名称
            if (StringUtils.isNotBlank(vo.getSubjectName())) {
                EduScheduleSubjectEnum subjectEnum = EduScheduleSubjectEnum.getByKey(vo.getSubjectName());
                if (subjectEnum != null) {
                    vo.setSubjectName(subjectEnum.getDesc());
                }
            }
        }).filter(vo -> vo.getTeacherName() != null && !vo.getTeacherName().isEmpty()).collect(Collectors.toList());
    }

    /**
     * 构建嵌套结构：教师 -> 星期 -> 课程列表
     */
    private Map<String, Map<String, List<ScheduleVO>>> buildNestedStructure(List<ScheduleVO> scheduleList) {
        return scheduleList.stream().collect(Collectors.groupingBy(ScheduleVO::getTeacherName, LinkedHashMap::new, Collectors.collectingAndThen(Collectors.toList(), teacherCourses -> {
            // 创建固定顺序的星期结构
            Map<String, List<ScheduleVO>> weekdayMap = CommonConstant.orderedDays.stream().collect(Collectors.toMap(day -> day, day -> new ArrayList<>(), (v1, v2) -> v1, LinkedHashMap::new));

            // 填充星期数据
            teacherCourses.forEach(course -> {
                if (course.getWeekday() != null && CommonConstant.weekdayMap.containsKey(course.getWeekday())) {
                    String dayName = CommonConstant.weekdayMap.get(course.getWeekday());
                    weekdayMap.get(dayName).add(course);
                }
            });

            // 对每个星期的课程排序
            weekdayMap.values().forEach(courses -> courses.sort(Comparator.comparing(ScheduleVO::getPeriod)));

            return weekdayMap;
        })));
    }


    /**
     * 初始化结果数据结构
     *
     * @return 初始化的星期->时段->科目->课程列表结构
     */
    private Map<String, List<Map<String, Map<String, List<ScheduleVO>>>>> initializeResultStructure() {
        Map<String, List<Map<String, Map<String, List<ScheduleVO>>>>> result = new LinkedHashMap<>();

        for (String day : CommonConstant.orderedDays) {
            List<Map<String, Map<String, List<ScheduleVO>>>> dayContainer = new ArrayList<>();
            Map<String, Map<String, List<ScheduleVO>>> periodContainer = new LinkedHashMap<>();

            for (String periodKey : CommonConstant.periodNames) {
                periodContainer.put(Constant.PERIOD + Constant.SPACE + periodKey.split(Constant.SPACE)[1], new LinkedHashMap<>());
            }

            dayContainer.add(periodContainer);
            result.put(day, dayContainer);
        }

        return result;
    }

    /**
     * 处理并组织课程数据到结果结构中
     *
     * @param scheduleVOList 课程列表
     * @param result         结果结构
     */
    private void processAndOrganizeCourses(List<ScheduleVO> scheduleVOList, Map<String, List<Map<String, Map<String, List<ScheduleVO>>>>> result) {
        // 建立临时分组映射：星期+时段 -> 科目 -> 课程列表
        Map<String, Map<String, List<ScheduleVO>>> tempGrouping = new HashMap<>();

        // 第一步：按星期、时段和科目名称分组课程
        scheduleVOList.stream().filter(vo -> vo.getWeekday() != null && vo.getPeriod() != null && vo.getSubjectName() != null && CommonConstant.weekdayMap.containsKey(vo.getWeekday())).forEach(vo -> {
            String day = CommonConstant.weekdayMap.get(vo.getWeekday());
            String periodKey = Constant.PERIOD + Constant.SPACE + vo.getPeriod();
            String subjectName = vo.getSubjectName();
            String groupKey = day + Constant.HYPHEN + periodKey;

            tempGrouping.computeIfAbsent(groupKey, k -> new HashMap<>()).computeIfAbsent(subjectName, k -> new ArrayList<>()).add(vo);
        });

        // 第二步：将临时分组数据填充到结果结构中
        tempGrouping.forEach((groupKey, subjectGroups) -> {
            String[] parts = groupKey.split(Constant.HYPHEN);
            String day = parts[0];
            String periodKey = parts[1];

            List<Map<String, Map<String, List<ScheduleVO>>>> dayContainer = result.get(day);
            if (!CollectionUtils.isEmpty(dayContainer)) {
                Map<String, Map<String, List<ScheduleVO>>> periodContainer = dayContainer.get(0);
                Map<String, List<ScheduleVO>> periodData = periodContainer.get(periodKey);

                if (periodData != null) {
                    periodData.putAll(subjectGroups);
                }
            }
        });

        // 第三步：清理没有数据的时段
        result.values().stream().filter(dayContainer -> !CollectionUtils.isEmpty(dayContainer)).forEach(dayContainer -> {
            Map<String, Map<String, List<ScheduleVO>>> periodContainer = dayContainer.get(0);

            // 收集空时段并移除
            periodContainer.entrySet().stream().filter(entry -> entry.getValue().isEmpty()).map(Map.Entry::getKey).collect(Collectors.toList()).forEach(periodContainer::remove);
        });
    }

    /**
     * 将班级编码List转换为逗号分隔的字符串
     *
     * @param scheduleDTO
     */
    private void convertClassCodeToString(ScheduleDTO scheduleDTO) {
        if (Objects.nonNull(scheduleDTO) && !CollectionUtils.isEmpty(scheduleDTO.getClassCodes())) {
            String classCode = String.join(Constant.SEPARATOR, scheduleDTO.getClassCodes());
            scheduleDTO.setClassCode(classCode);
        }
    }

    /**
     * 将逗号分隔的班级编码字符串转换为List
     *
     * @param scheduleVO
     */
    private void convertClassCodeToList(ScheduleVO scheduleVO) {
        if (StringUtils.isNotBlank(scheduleVO.getClassCode())) {
            List<String> list = new ArrayList<>();
            List<String> classCode = Arrays.asList(scheduleVO.getClassCode().split(Constant.SEPARATOR));
            for (String str : classCode) {
                EduClassCodeEnum classEnum = EduClassCodeEnum.getByKey(str);
                if (classEnum != null) {
                    list.add(classEnum.getDesc());
                }
            }
            scheduleVO.setClassCodes(list);
        }
    }
} 