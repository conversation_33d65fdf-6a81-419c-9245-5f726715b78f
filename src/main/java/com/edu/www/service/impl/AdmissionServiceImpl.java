package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.AdmissionDTO;
import com.edu.www.mapper.AdmissionMapper;
import com.edu.www.service.AdmissionService;
import com.edu.www.service.CommonService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.AdmissionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 招生信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class AdmissionServiceImpl implements AdmissionService {
    private static final Logger logger = LoggerFactory.getLogger(AdmissionService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private AdmissionMapper admissionMapper;

    /**
     * 根据ID查询招生信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询招生信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "招生信息ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        responseEntity.setData(admissionMapper.get(id));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询招生信息成功");
        return responseEntity;
    }

    /**
     * 分页查询招生信息
     *
     * @param admissionVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, AdmissionVO admissionVO) {
        logger.info("分页查询招生信息入参:{}", JSON.toJSONString(admissionVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(admissionVO.getPageStart(), admissionVO.getPageSize());

        AdmissionDTO admissionDTO = new AdmissionDTO();
        BeanUtils.copyProperties(admissionVO, admissionDTO);

        List<AdmissionVO> admissionVOList = admissionMapper.query(admissionDTO);
        PageInfo<AdmissionVO> pageInfo = new PageInfo<>(admissionVOList);
        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询招生信息成功");
        return responseEntity;
    }

    /**
     * 新增招生信息
     *
     * @param admissionVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, AdmissionVO admissionVO) {
        logger.info("新增招生信息入参:{}", JSON.toJSONString(admissionVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(admissionVO.getDescription()) &&
                admissionVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        AdmissionDTO admissionDTO = new AdmissionDTO();
        BeanUtils.copyProperties(admissionVO, admissionDTO);

        String userName = RequestMsgUtil.getSessionUserName();
        admissionDTO.setCreatedBy(userName);
        admissionDTO.setUpdatedBy(userName);

        admissionMapper.insert(admissionDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增招生信息成功");
        return responseEntity;
    }

    /**
     * 修改招生信息
     *
     * @param admissionVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, AdmissionVO admissionVO) {
        logger.info("修改招生信息入参:{}", JSON.toJSONString(admissionVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(admissionVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(admissionVO.getDescription()) &&
                admissionVO.getDescription().length() > 255, "说名长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        AdmissionDTO admissionDTO = new AdmissionDTO();
        BeanUtils.copyProperties(admissionVO, admissionDTO);

        String userName = RequestMsgUtil.getSessionUserName();
        admissionDTO.setUpdatedBy(userName);
        admissionDTO.setUpdatedAt(new Date());

        admissionMapper.update(admissionDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改招生信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除招生信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("招生信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "招生信息ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        admissionMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除招生信息成功");
        return responseEntity;
    }
} 