package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.PurchaseOrderDetailDTO;
import com.edu.www.mapper.PurchaseOrderDetailMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.PurchaseOrderDetailService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.PurchaseOrderDetailVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 采购明细管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class PurchaseOrderDetailServiceImpl implements PurchaseOrderDetailService {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderDetailService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询采购明细信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "采购明细ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无查询权限");
                return responseEntity;
            }

            // 查询采购明细信息
            PurchaseOrderDetailVO purchaseOrderDetailVO = purchaseOrderDetailMapper.get(id);
            if (purchaseOrderDetailVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购明细信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(purchaseOrderDetailVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询采购明细信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 根据采购单号查询采购详细信息
     *
     * @param request
     * @param purchaseNo
     * @return
     */
    @Override
    public ResponseEntity<Object> getDetailByPurchaseNo(HttpServletRequest request, String purchaseNo) {
        logger.info("根据采购单号查询采购详细信息入参:{}", purchaseNo);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(purchaseNo), "采购单号不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无查询详情权限");
                return responseEntity;
            }

            // 查询采购明细信息
            PurchaseOrderDetailDTO purchaseOrderDetailDTO = new PurchaseOrderDetailDTO();
            purchaseOrderDetailDTO.setPurchaseNo(purchaseNo);
            List<PurchaseOrderDetailVO> purchaseOrderDetailList = purchaseOrderDetailMapper.query(purchaseOrderDetailDTO);
            if (CollectionUtils.isEmpty(purchaseOrderDetailList)) {
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("根据采购单号查询采购详细信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("根据采购单号查询采购详细信息成功");
            responseEntity.setData(purchaseOrderDetailList);
        } catch (Exception e) {
            logger.error("根据采购单号查询采购详细信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, PurchaseOrderDetailVO purchaseOrderDetailVO) {
        logger.info("分页查询采购明细信息入参:{}", JSON.toJSONString(purchaseOrderDetailVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setData(new PageInfo());
                responseEntity.setSuccess(Boolean.TRUE);
                responseEntity.setMsg("无分页查询权限");
                return responseEntity;
            }

            // 设置分页参数
            PageHelper.startPage(purchaseOrderDetailVO.getPageStart(), purchaseOrderDetailVO.getPageSize());

            // 转换为DTO
            PurchaseOrderDetailDTO purchaseOrderDetailDTO = new PurchaseOrderDetailDTO();
            BeanUtils.copyProperties(purchaseOrderDetailVO, purchaseOrderDetailDTO);

            // 查询数据
            List<PurchaseOrderDetailVO> purchaseOrderDetailList = purchaseOrderDetailMapper.query(purchaseOrderDetailDTO);
            PageInfo<PurchaseOrderDetailVO> pageInfo = new PageInfo<>(purchaseOrderDetailList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");
        } catch (Exception e) {
            logger.error("分页查询采购明细信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, PurchaseOrderDetailVO purchaseOrderDetailVO) {
        logger.info("新增采购明细信息入参:{}", JSON.toJSONString(purchaseOrderDetailVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePurchaseOrderDetail(purchaseOrderDetailVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无新增权限");
                return responseEntity;
            }

            // 转换为DTO
            PurchaseOrderDetailDTO purchaseOrderDetailDTO = new PurchaseOrderDetailDTO();
            BeanUtils.copyProperties(purchaseOrderDetailVO, purchaseOrderDetailDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            purchaseOrderDetailDTO.setCreatedBy(currentUser);
            purchaseOrderDetailDTO.setUpdatedBy(currentUser);

            // 新增采购明细信息
            purchaseOrderDetailMapper.insert(purchaseOrderDetailDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");
        } catch (Exception e) {
            logger.error("新增采购明细信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, PurchaseOrderDetailVO purchaseOrderDetailVO) {
        logger.info("修改采购明细信息入参:{}", JSON.toJSONString(purchaseOrderDetailVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validatePurchaseOrderDetail(purchaseOrderDetailVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无修改权限");
                return responseEntity;
            }

            // 检查采购明细是否存在
            PurchaseOrderDetailVO existPurchaseOrderDetail = purchaseOrderDetailMapper.get(purchaseOrderDetailVO.getId());
            if (existPurchaseOrderDetail == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购明细信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            PurchaseOrderDetailDTO purchaseOrderDetailDTO = new PurchaseOrderDetailDTO();
            BeanUtils.copyProperties(purchaseOrderDetailVO, purchaseOrderDetailDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            purchaseOrderDetailDTO.setUpdatedBy(currentUser);

            // 修改采购明细信息
            purchaseOrderDetailMapper.update(purchaseOrderDetailDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");
        } catch (Exception e) {
            logger.error("修改采购明细信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除采购明细信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "采购明细ID不能为空");

            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无删除权限");
                return responseEntity;
            }

            // 检查采购明细是否存在
            PurchaseOrderDetailVO existPurchaseOrderDetail = purchaseOrderDetailMapper.get(id);
            if (existPurchaseOrderDetail == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("采购明细信息不存在");
                return responseEntity;
            }

            // 删除采购明细信息
            purchaseOrderDetailMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");
        } catch (Exception e) {
            logger.error("删除采购明细信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证采购明细信息
     */
    private String validatePurchaseOrderDetail(PurchaseOrderDetailVO purchaseOrderDetailVO, boolean isUpdate) {
        if (purchaseOrderDetailVO == null) {
            return "采购明细信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(purchaseOrderDetailVO.getId())) {
            return "采购明细ID不能为空";
        }

        if (StringUtils.isBlank(purchaseOrderDetailVO.getPurchaseNo())) {
            return "采购ID不能为空";
        }

        if (StringUtils.isBlank(purchaseOrderDetailVO.getBookName())) {
            return "书名不能为空";
        }

        if (purchaseOrderDetailVO.getUnitPrice() == null || purchaseOrderDetailVO.getUnitPrice().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return "单价必须大于0";
        }

        if (purchaseOrderDetailVO.getQuantity() == null || purchaseOrderDetailVO.getQuantity() <= 0) {
            return "数量必须大于0";
        }

        return null;
    }
}
