package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.convert.BookConverter;
import com.edu.www.dto.BookDTO;
import com.edu.www.mapper.BookMapper;
import com.edu.www.service.BookService;
import com.edu.www.service.CommonService;
import com.edu.www.service.FileService;
import com.edu.www.service.FileUploadService;
import com.edu.www.utils.FileUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.validates.BookValidate;
import com.edu.www.vo.BookVO;
import com.edu.www.vo.FileVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 书籍信息管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class BookServiceImpl implements BookService {
    private static final Logger logger = LoggerFactory.getLogger(BookService.class);

    @Autowired
    private BookMapper bookMapper;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private FileService fileService;

    @Autowired
    private CommonService commonService;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询书籍信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "书籍ID不能为空");

            // 查询书籍信息
            BookVO bookVO = bookMapper.get(id);
            if (Objects.isNull(bookVO)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍信息不存在");
                return responseEntity;
            }

            // 将逗号分隔的年级字符串转换为List
            BookConverter.convertGradeLevelToList(bookVO);
            Map<String, FileVO> map = commonService.getFileInfo(Arrays.asList(bookVO.getFrontCoverImage(), bookVO.getBackCoverImage()));
            if (MapUtils.isNotEmpty(map)) {
                bookVO.setFrontCoverImage(map.get(bookVO.getFrontCoverImage()).getFileName());
                bookVO.setBackCoverImage(map.get(bookVO.getBackCoverImage()).getFileName());
            }
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(bookVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询书籍信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, BookVO bookVO) {
        logger.info("分页查询书籍信息入参:{}", JSON.toJSONString(bookVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 设置分页参数
            PageHelper.startPage(bookVO.getPageStart(), bookVO.getPageSize());

            // 转换为DTO
            BookDTO bookDTO = new BookDTO();
            BeanUtils.copyProperties(bookVO, bookDTO);

            // 如果传入多个年级，将List转换为逗号分隔的字符串用于查询
            BookConverter.convertGradeLevelsToString(bookDTO);

            // 查询数据
            List<BookVO> bookVOList = bookMapper.query(bookDTO);

            if (!CollectionUtils.isEmpty(bookVOList)) {
                List<String> allCoverImages = bookVOList.stream()
                        .flatMap(book -> Stream.of(book.getFrontCoverImage(), book.getBackCoverImage()))
                        .collect(Collectors.toList());
                Map<String, FileVO> map = commonService.getFileInfo(allCoverImages);
                for (BookVO vo : bookVOList) {
                    // 处理查询结果，将逗号分隔的年级字符串转换为List
                    BookConverter.convertGradeLevelToList(vo);
                    // 转换文件名称
                    if (MapUtils.isNotEmpty(map)) {
                        vo.setFrontCoverImage(map.get(vo.getFrontCoverImage()).getFileName());
                        vo.setBackCoverImage(map.get(vo.getBackCoverImage()).getFileName());
                    }
                }
            }

            PageInfo<BookVO> pageInfo = new PageInfo<>(bookVOList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询书籍信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, BookVO bookVO) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            BookValidate.validateBook(bookVO, false);
            // 处理封面图片上传
            MultipartFile frontCoverImageFile = bookVO.getFrontCoverImageFile();
            if (frontCoverImageFile != null && !frontCoverImageFile.isEmpty()) {
                String frontCoverFileId = uploadImageFile(frontCoverImageFile, "封面图片");
                if (frontCoverFileId != null) {
                    bookVO.setFrontCoverImage(frontCoverFileId);
                }
            }

            // 处理封底图片上传
            MultipartFile backCoverImageFile = bookVO.getBackCoverImageFile();
            if (backCoverImageFile != null && !backCoverImageFile.isEmpty()) {
                String backCoverFileId = uploadImageFile(backCoverImageFile, "封底图片");
                if (backCoverFileId != null) {
                    bookVO.setBackCoverImage(backCoverFileId);
                }
            }

            // 转换为DTO
            BookDTO bookDTO = new BookDTO();
            BeanUtils.copyProperties(bookVO, bookDTO);

            // 将年级List转换为逗号分隔的字符串用于存储
            BookConverter.convertGradeLevelsToString(bookDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            bookDTO.setCreatedBy(currentUser);
            bookDTO.setUpdatedBy(currentUser);

            // 新增书籍信息
            bookMapper.insert(bookDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增书籍信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, BookVO bookVO) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            BookValidate.validateBook(bookVO, true);
            // 检查书籍是否存在
            BookVO existBook = bookMapper.get(bookVO.getId());
            if (Objects.isNull(existBook)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍信息不存在");
                return responseEntity;
            }

            // 处理封面图片上传
            MultipartFile frontCoverImageFile = bookVO.getFrontCoverImageFile();
            if (frontCoverImageFile != null && !frontCoverImageFile.isEmpty()) {
                String frontCoverFileId = uploadImageFile(frontCoverImageFile, "封面图片");
                if (frontCoverFileId != null) {
                    bookVO.setFrontCoverImage(frontCoverFileId);
                    // 删除旧的封面图片
                    if (StringUtils.isNotBlank(existBook.getFrontCoverImage())) {
                        deleteImageFile(existBook.getFrontCoverImage(), "旧封面图片");
                    }
                } else {
                    // 如果上传失败，保持原有的图片
                    bookVO.setFrontCoverImage(existBook.getFrontCoverImage());
                }
            } else {
                // 如果没有上传新图片，保持原有的图片
                bookVO.setFrontCoverImage(existBook.getFrontCoverImage());
            }

            // 处理封底图片上传
            MultipartFile backCoverImageFile = bookVO.getBackCoverImageFile();
            if (backCoverImageFile != null && !backCoverImageFile.isEmpty()) {
                String backCoverFileId = uploadImageFile(backCoverImageFile, "封底图片");
                if (backCoverFileId != null) {
                    bookVO.setBackCoverImage(backCoverFileId);
                    // 删除旧的封底图片
                    if (StringUtils.isNotBlank(existBook.getBackCoverImage())) {
                        deleteImageFile(existBook.getBackCoverImage(), "旧封底图片");
                    }
                } else {
                    // 如果上传失败，保持原有的图片
                    bookVO.setBackCoverImage(existBook.getBackCoverImage());
                }
            } else {
                // 如果没有上传新图片，保持原有的图片
                bookVO.setBackCoverImage(existBook.getBackCoverImage());
            }

            // 转换为DTO
            BookDTO bookDTO = new BookDTO();
            BeanUtils.copyProperties(bookVO, bookDTO);

            // 将年级List转换为逗号分隔的字符串用于存储
            BookConverter.convertGradeLevelsToString(bookDTO);

            // 设置修改信息
            bookDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());

            // 修改书籍信息
            bookMapper.update(bookDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改书籍信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除书籍信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(id), "书籍ID不能为空");

            // 检查书籍是否存在
            BookVO existBook = bookMapper.get(id);
            if (Objects.isNull(existBook)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍信息不存在");
                return responseEntity;
            }

            // 删除相关的图片文件
            if (StringUtils.isNotBlank(existBook.getFrontCoverImage())) {
                deleteImageFile(existBook.getFrontCoverImage(), "封面图片");
            }

            if (StringUtils.isNotBlank(existBook.getBackCoverImage())) {
                deleteImageFile(existBook.getBackCoverImage(), "封底图片");
            }

            // 删除书籍信息
            bookMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除书籍信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 上传图片文件
     *
     * @param imageFile 图片文件
     * @param imageType 图片类型描述
     * @return 文件ID，上传失败返回null
     */
    private String uploadImageFile(MultipartFile imageFile, String imageType) {
        try {
            if (imageFile == null || imageFile.isEmpty()) {
                logger.warn("{}文件为空", imageType);
                return null;
            }

            // 验证文件类型是否为图片
            String originalFileName = imageFile.getOriginalFilename();
            if (StringUtils.isBlank(originalFileName)) {
                logger.warn("{}文件名为空", imageType);
                return null;
            }

            String extension = originalFileName.substring(originalFileName.lastIndexOf(".") + 1).toLowerCase();
            if (!FileUtil.isImageFile(extension)) {
                logger.warn("{}文件类型不支持: {}", imageType, extension);
                return null;
            }

            // 调用文件上传服务
            ResponseEntity<Object> uploadResult = fileUploadService.uploadFile(imageFile);
            if (uploadResult.getSuccess() && uploadResult.getData() != null) {
                @SuppressWarnings("unchecked")
                String fileId = (String) uploadResult.getData();
                logger.info("{}上传成功: fileId={}, fileName={}", imageType, fileId, originalFileName);
                return fileId;
            } else {
                logger.error("{}上传失败: {}", imageType, uploadResult.getMsg());
                return null;
            }
        } catch (Exception e) {
            logger.error("{}上传异常", imageType, e);
            return null;
        }
    }

    /**
     * 删除图片文件
     *
     * @param fileId    文件ID
     * @param imageType 图片类型描述
     */
    private void deleteImageFile(String fileId, String imageType) {
        try {
            if (StringUtils.isBlank(fileId)) {
                return;
            }

            ResponseEntity<Object> deleteResult = fileUploadService.deleteFile(fileId);
            if (deleteResult.getSuccess()) {
                logger.info("{}删除成功: fileId={}", imageType, fileId);
            } else {
                logger.warn("{}删除失败: fileId={}, error={}", imageType, fileId, deleteResult.getMsg());
            }

        } catch (Exception e) {
            logger.error("{}删除异常: fileId={}", imageType, fileId, e);
        }
    }

}
