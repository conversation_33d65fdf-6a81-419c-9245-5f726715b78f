package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.Constant;
import com.edu.www.dto.FileDTO;
import com.edu.www.mapper.FileMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.FileService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.FileVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 文件信息管理 Service实现
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Service
public class FileServiceImpl implements FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileServiceImpl.class);

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询文件信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询文件信息，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "文件信息ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        responseEntity.setData(fileMapper.get(id));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询文件信息成功");
        return responseEntity;
    }

    /**
     * 根据文件ID查询文件信息
     *
     * @param fileId
     * @return
     */
    @Override
    public ResponseEntity<Object> getByFileId(HttpServletRequest request, String fileId) {
        logger.info("根据文件ID查询文件信息，fileId：{}", fileId);
        ValidateUtil.paramValidate(StringUtils.isBlank(fileId), "文件ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        responseEntity.setData(fileMapper.getByFileId(fileId));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询文件信息成功");
        return responseEntity;
    }

    /**
     * 根据多个文件ID查询文件信息
     *
     * @param fileIds
     * @return
     */
    @Override
    public List<FileVO> getFileName(List<String> fileIds) {
        // 参数验证
        ValidateUtil.paramValidate(CollectionUtils.isEmpty(fileIds), "文件ID不能为空");
        return fileMapper.getByFileIds(fileIds);
    }

    /**
     * 分页查询文件信息
     *
     * @param fileVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, FileVO fileVO) {
        logger.info("分页查询文件信息入参:{}", JSON.toJSONString(fileVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        FileDTO fileDTO = new FileDTO();
        BeanUtils.copyProperties(fileVO, fileDTO);

        PageHelper.startPage(fileVO.getPageStart(), fileVO.getPageSize());
        List<FileVO> fileList = fileMapper.query(fileDTO);
        PageInfo<FileVO> pageInfo = new PageInfo<>(fileList);

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询文件信息成功");
        return responseEntity;
    }

    /**
     * 新增文件信息
     *
     * @param fileVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, FileVO fileVO) {
        logger.info("新增文件信息入参:{}", JSON.toJSONString(fileVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(fileVO.getFileId()), "文件ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(fileVO.getFileName()), "文件名不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(fileVO.getDescription()) &&
                fileVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 如果request不为null，进行权限检查
        if (request != null) {
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无新增权限");
                return responseEntity;
            }
        }

        FileDTO fileDTO = new FileDTO();
        BeanUtils.copyProperties(fileVO, fileDTO);

        String userName = RequestMsgUtil.getSessionUserName();
        fileDTO.setCreatedBy(userName);
        fileDTO.setUpdatedBy(userName);

        fileMapper.insert(fileDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增文件信息成功");
        return responseEntity;
    }

    /**
     * 修改文件信息
     *
     * @param fileVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, FileVO fileVO) {
        logger.info("修改文件信息入参:{}", JSON.toJSONString(fileVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(fileVO.getId()), "文件信息ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(fileVO.getDescription()) &&
                fileVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        FileDTO fileDTO = new FileDTO();
        BeanUtils.copyProperties(fileVO, fileDTO);

        String userName = RequestMsgUtil.getSessionUserName();
        fileDTO.setUpdatedBy(userName);

        fileMapper.update(fileDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改文件信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除文件信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("文件信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "文件信息ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        fileMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除文件信息成功");
        return responseEntity;
    }

    /**
     * 根据文件ID删除文件信息
     *
     * @param fileId
     * @return
     */
    @Override
    public ResponseEntity<Object> deleteByFileId(HttpServletRequest request, String fileId) {
        logger.info("根据文件ID删除文件信息，fileId:{}", fileId);
        ValidateUtil.paramValidate(StringUtils.isBlank(fileId), "文件ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 如果request不为null，进行权限检查
        if (request != null) {
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无删除权限");
                return responseEntity;
            }
        }

        fileMapper.deleteByFileId(fileId);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除文件信息成功");
        return responseEntity;
    }

    /**
     * 批量删除文件信息
     *
     * @param ids
     * @return
     */
    @Override
    public ResponseEntity<Object> batchDelete(HttpServletRequest request, String ids) {
        logger.info("批量删除文件信息，ids:{}", ids);
        ValidateUtil.paramValidate(StringUtils.isBlank(ids), "文件信息ID列表不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        List<String> idList = Arrays.asList(ids.split(Constant.DELIMITER_COMMA));
        fileMapper.batchDelete(idList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("批量删除文件信息成功");
        return responseEntity;
    }

    /**
     * 根据文件类型查询文件信息
     *
     * @param fileType
     * @return
     */
    @Override
    public ResponseEntity<Object> queryByFileType(HttpServletRequest request, String fileType) {
        logger.info("根据文件类型查询文件信息，fileType:{}", fileType);
        ValidateUtil.paramValidate(StringUtils.isBlank(fileType), "文件类型不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        List<FileVO> fileList = fileMapper.queryByFileType(fileType);
        responseEntity.setData(fileList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询文件信息成功");
        return responseEntity;
    }
}
