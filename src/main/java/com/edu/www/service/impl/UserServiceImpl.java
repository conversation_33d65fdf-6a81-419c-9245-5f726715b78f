package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.convert.UserConverter;
import com.edu.www.dto.InviteCodeDTO;
import com.edu.www.dto.UserDTO;
import com.edu.www.dto.UserRoleDTO;
import com.edu.www.enums.BizErrorCodeEnum;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduLanguageEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.InviteCodeMapper;
import com.edu.www.mapper.UserMapper;
import com.edu.www.mapper.UserRoleMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.UserService;
import com.edu.www.utils.*;
import com.edu.www.validates.ParamValidate;
import com.edu.www.constants.Constant;
import com.edu.www.vo.InviteCodeVO;
import com.edu.www.vo.PermissionVO;
import com.edu.www.vo.UserVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 指标缓存刷新
 *
 * <AUTHOR>
 * @date 2020/11/17
 */
@Service
public class UserServiceImpl implements UserService {
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private InviteCodeMapper inviteCodeMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询用户信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "用户ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        UserVO userVO = userMapper.getById(id);
        // 置空敏感密码
        userVO.setPageSize(null);
        userVO.setPageStart(null);
        userVO.setPassword(null);
        responseEntity.setData(userVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询用户信息成功");
        return responseEntity;
    }

    /**
     * 分页查询用户信息
     *
     * @param userVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, UserVO userVO) {
        logger.info("分页查询用户信息入参:{}", JSON.toJSONString(userVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(userVO, userDTO);

        PageHelper.startPage(userVO.getPageStart(), userVO.getPageSize());
        PageInfo<UserVO> pageInfo = new PageInfo(userMapper.query(userDTO));

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询用户信息成功");
        return responseEntity;
    }

    /**
     * 用户注册
     *
     * @param userVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> register(UserVO userVO) {
        logger.info("用户注册信息：{}", JSON.toJSONString(userVO));
        // 参数校验
        ParamValidate.validUserRegisterParam(userVO);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 判断邮箱号是否存在
        if (userMapper.existsByEmail(userVO.getEmail())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("您所输入的邮箱号已存在");
            return responseEntity;
        }

        // 校验邀请码是否有效
        InviteCodeVO inviteCodeVO = inviteCodeMapper.getByCode(userVO.getInviteCode());
        if (Objects.isNull(inviteCodeVO) || DateUtil.isDateExpired(inviteCodeVO.getExpiryTime())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("邀请码已过期");
            return responseEntity;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(userVO, userDTO);

        // 加密密码
        ResponseEntity<Object> encryptPwdResponse = EncryptUtil.encrypt(userVO.getPassword());
        if (!encryptPwdResponse.getSuccess()) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("注册信息时发生异常，请联系系统管理员");
            return encryptPwdResponse;
        }

        userDTO.setPassword((String) encryptPwdResponse.getData());
        String fullName = userVO.getLastName() + userVO.getFirstName();
        userDTO.setNameZh(fullName);
        userDTO.setNameEn(fullName);
        if (StringUtils.isNotBlank(userDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(userDTO.getDepartmentCode());
            if (deptEnum != null) {
                userDTO.setDepartmentName(deptEnum.getDesc());
            }
        }
        userDTO.setLanguage(EduLanguageEnum.CHINESE.getKey());
        userDTO.setCreatedBy(fullName);
        userDTO.setUpdatedBy(fullName);
        userMapper.insert(userDTO);

        // 更新邀请码信息
        InviteCodeDTO inviteCodeDTO = new InviteCodeDTO();
        inviteCodeDTO.setId(inviteCodeVO.getId());
        // 获取用户插入成功后返回的ID
        inviteCodeDTO.setUserId(userDTO.getId());
        inviteCodeDTO.setIsUsed(EduYesOrNoEnum.YES.getKey());
        inviteCodeDTO.setUsedTime(new Date());
        inviteCodeMapper.update(inviteCodeDTO);

        // 新增用户角色关联(默认无权限)
//        UserRoleDTO userRoleDTO = new UserRoleDTO();
//        userRoleDTO.setUserId(userDTO.getId());
//        userRoleDTO.setRoleId(Constant.NO_PERMISSION);
//        userRoleDTO.setCreatedBy(fullName);
//        userRoleDTO.setUpdatedBy(fullName);
//        userRoleMapper.insert(userRoleDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("注册成功");
        return responseEntity;
    }

    /**
     * 新增用户
     *
     * @param userVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, UserVO userVO) {
        logger.info("新增用户信息：{}", JSON.toJSONString(userVO));
        // 参数校验
        ParamValidate.validUserInsertParam(userVO);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        // 判断邮箱号是否存在
        if (userMapper.existsByEmail(userVO.getEmail())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("您所输入的邮箱号已存在");
            return responseEntity;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(userVO, userDTO);

        // 加密密码
        ResponseEntity<Object> encryptPwdResponse = EncryptUtil.encrypt(userVO.getPassword());
        if (!encryptPwdResponse.getSuccess()) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增用户时发生异常，请联系系统管理员");
            return encryptPwdResponse;
        }

        userDTO.setPassword((String) encryptPwdResponse.getData());
        String fullName = userVO.getLastName() + userVO.getFirstName();
        userDTO.setNameZh(fullName);
        userDTO.setNameEn(fullName);
        if (StringUtils.isNotBlank(userDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(userDTO.getDepartmentCode());
            if (deptEnum != null) {
                userDTO.setDepartmentName(deptEnum.getDesc());
            }
        }
        userDTO.setLanguage(EduLanguageEnum.CHINESE.getKey());
        userDTO.setCreatedBy(fullName);
        userDTO.setUpdatedBy(fullName);
        userMapper.insert(userDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增用户成功");
        return responseEntity;
    }

    /**
     * 用户修改信息
     *
     * @param userVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> modify(UserVO userVO) {
        logger.info("用户修改信息:{}", JSON.toJSONString(userVO));
        // 参数校验
        ParamValidate.validUserModifyParam(userVO);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(userVO, userDTO);
        // 禁止修改部门编号
        userDTO.setDepartmentCode(null);
        // 禁止修改工号
        userDTO.setEmpNo(null);
        userDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        userMapper.update(userDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改信息成功");
        return responseEntity;
    }

    /**
     * 修改用户信息
     *
     * @param request
     * @param userVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, UserVO userVO) {
        logger.info("修改用户信息:{}", JSON.toJSONString(userVO));
        // 参数校验
        ParamValidate.validUserModifyParam(userVO);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(userVO, userDTO);
        if (StringUtils.isNotBlank(userDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(userDTO.getDepartmentCode());
            if (deptEnum != null) {
                userDTO.setDepartmentName(deptEnum.getDesc());
            }
        }
        userDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        userMapper.update(userDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改信息成功");
        return responseEntity;
    }

    /**
     * 修改密码
     *
     * @param id
     * @param oldPwd
     * @param newPwd
     * @return
     */
    @Override
    public ResponseEntity<Object> modifyPwd(String id, String oldPwd, String newPwd) {
        logger.info("用户修改密码ID:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(oldPwd), "旧密码不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(newPwd), "新密码不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 加密旧密码
        ResponseEntity<Object> oldEncryptResponse = EncryptUtil.encrypt(oldPwd);
        if (!oldEncryptResponse.getSuccess()) {
            return oldEncryptResponse;
        }
        // 根据ID查询用户信息
        UserVO userVO = userMapper.getById(id);
        if (Objects.isNull(userVO) || StringUtils.isBlank(userVO.getPassword()) ||
                !userVO.getPassword().equals(oldEncryptResponse.getData())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("您输入的旧密码不正确");
            return responseEntity;
        }

        // 加密新密码
        ResponseEntity<Object> newEncryptResponse = EncryptUtil.encrypt(newPwd);
        if (!newEncryptResponse.getSuccess()) {
            return newEncryptResponse;
        }

        if (userVO.getPassword().equals(newEncryptResponse.getData())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新密码与旧密码相同，请重新输入新密码");
            return responseEntity;
        }

        UserDTO userDTO = new UserDTO();
        userDTO.setId(id);
        userDTO.setPassword((String) newEncryptResponse.getData());
        userDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        userMapper.updatePwdById(userDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setNeedLogin(Boolean.TRUE);
        responseEntity.setMsg("修改密码成功");
        return responseEntity;
    }

    /**
     * 修改密码
     *
     * @param request
     * @param id
     * @param oldPwd
     * @param newPwd
     * @return
     */
    @Override
    public ResponseEntity<Object> updatePwd(HttpServletRequest request, String id, String oldPwd, String newPwd) {
        logger.info("用户修改密码ID:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(oldPwd), "旧密码不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(newPwd), "新密码不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改密码权限");
            return responseEntity;
        }

        // 加密旧密码
        ResponseEntity<Object> oldEncryptResponse = EncryptUtil.encrypt(oldPwd);
        if (!oldEncryptResponse.getSuccess()) {
            return oldEncryptResponse;
        }
        // 根据ID查询用户信息
        UserVO userVO = userMapper.getById(id);
        if (Objects.isNull(userVO) || StringUtils.isBlank(userVO.getPassword()) ||
                !userVO.getPassword().equals(oldEncryptResponse.getData())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("您输入的旧密码不正确");
            return responseEntity;
        }

        // 加密新密码
        ResponseEntity<Object> newEncryptResponse = EncryptUtil.encrypt(newPwd);
        if (!newEncryptResponse.getSuccess()) {
            return newEncryptResponse;
        }

        if (userVO.getPassword().equals(newEncryptResponse.getData())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新密码与旧密码相同，请重新输入新密码");
            return responseEntity;
        }

        UserDTO userDTO = new UserDTO();
        userDTO.setId(id);
        userDTO.setPassword((String) newEncryptResponse.getData());
        userDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        userMapper.updatePwdById(userDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setNeedLogin(Boolean.TRUE);
        responseEntity.setMsg("修改密码成功");
        return responseEntity;
    }

    /**
     * 用户登录
     *
     * @param userVO
     * @return
     */
    @Override
    public ResponseEntity<Object> login(UserVO userVO) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getEmail()), "用户名不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(userVO.getPassword()), "密码不能为空");
        ValidateUtil.paramValidate(!FormatValidUtil.isValidEmail(userVO.getEmail()), "用户名(邮箱)格式不正确");

        String attemptKey = Constant.LOGIN_ATTEMPT_KEY + userVO.getEmail();
        String lockKey = Constant.LOCK_TIME_KEY + userVO.getEmail();
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        ResponseEntity<Object> encryptPwdResponse = EncryptUtil.encrypt(userVO.getPassword());
        if (!encryptPwdResponse.getSuccess()) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("登录失败，请重新登录");
            responseEntity.setErrorCode(BizErrorCodeEnum.USER_LOGIN_FAILED);
            return encryptPwdResponse;
        }
        // 检查是否被锁定
        Object lockTime = redisTemplate.opsForValue().get(lockKey);
        if (Objects.nonNull(lockTime)) {
            responseEntity.setSuccess(Boolean.FALSE);
            if (StringUtils.isNotEmpty(lockTime + "")) {
                String formattedTime = DateUtil.formatTime24Hour(lockTime + "");
                responseEntity.setMsg("登录失败，请在 " + formattedTime + " 后重试");
            }
            return responseEntity;
        }

        // 根据用户名和密码查询用户信息
        UserVO user = userMapper.getByUsernameAndPassword(userVO.getEmail().trim(), (String) encryptPwdResponse.getData());
        if (Objects.isNull(user)) {
            // 增加失败次数
            Long attempts = redisTemplate.opsForValue().increment(attemptKey);
            if (attempts == 1) {
                redisTemplate.expire(attemptKey, Constant.LOCK_TIME_MINUTES, TimeUnit.MINUTES);
            }

            // 达到 3 次失败
            if (attempts >= Constant.MAX_LOGIN_ATTEMPTS) {
                LocalDateTime unlockTime = LocalDateTime.now().plusMinutes(Constant.LOCK_TIME_MINUTES);
                String formattedUnlockTime = unlockTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                redisTemplate.opsForValue().set(lockKey, formattedUnlockTime, Constant.LOCK_TIME_MINUTES, TimeUnit.MINUTES);
                responseEntity.setSuccess(Boolean.FALSE);
                String formattedTime = DateUtil.formatTime24Hour(formattedUnlockTime);
                responseEntity.setMsg("登录失败，你登录的错误次数过多，请在 " + formattedTime + " 后重试");
                return responseEntity;
            }
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("用户名或密码错误，您还可以尝试 " + (Constant.MAX_LOGIN_ATTEMPTS - attempts) + " 次");
            return responseEntity;
        }

        // 登录成功，清除 Redis 记录
        redisTemplate.delete(attemptKey);
        redisTemplate.delete(lockKey);

        // 生成 JWT token
        String token = jwtTokenUtil.generateToken(user.getEmail());
        // 将用户信息存入 Redis 且 1小时过期
        redisUtil.set(Constant.LOGIN_REDIS_KEY + user.getEmail(), user, Constant.ONE_HOURS);
        // UserVO转换UserDTO
        UserDTO dto = UserConverter.userVO2DTO(user);

        // 设置 session
        HttpServletRequest request = RequestMsgUtil.getRequest();
        if (request != null) {
            // 将用户信息存入 session
            HttpSession session = request.getSession(true); // 使用 true 参数，确保创建新的 session
            session.setAttribute(Constant.SESSION_USER, dto);
            // 同时使用另一个键名存储，确保兼容性
            session.setAttribute(Constant.USER, dto);
            session.setAttribute(Constant.TOKEN, token);
            // 设置 session 过期时间（可选）
            session.setMaxInactiveInterval(3600); // 1小时
        }

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("登录成功");
        responseEntity.setData(Map.of(Constant.SESSION_USER, dto, Constant.TOKEN, token));
        return responseEntity;
    }

    /**
     * 用户登出
     *
     * @param username
     * @return
     */
    @Override
    public ResponseEntity<Object> logout(String username) {
        ValidateUtil.paramValidate(StringUtils.isBlank(username), "用户名不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 从 session 中移除用户信息
            HttpServletRequest request = RequestMsgUtil.getRequest();
            if (request != null) {
                HttpSession session = request.getSession(false);
                if (session != null) {
                    session.removeAttribute(Constant.SESSION_USER);
                    session.removeAttribute(Constant.TOKEN);
                    session.invalidate(); // 使整个 session 失效
                }
            }

            // 从 Redis 中删除用户信息
            if (username != null) {
                // 清除 Redis 中的用户信息
                redisTemplate.delete(Constant.LOGIN_REDIS_KEY + username);
                // 清除登录尝试记录
                redisTemplate.delete(Constant.LOGIN_ATTEMPT_KEY + username);
                // 清除锁定记录
                redisTemplate.delete(Constant.LOCK_TIME_KEY + username);
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("退出登录成功");
        } catch (Exception e) {
            logger.error("退出登录失败:msg={}", e.getMessage(), e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("退出登录失败");
        }

        return responseEntity;
    }

    /**
     * 用户注销
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> logoff(String id) {
        logger.info("用户信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        userMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setNeedLogin(Boolean.TRUE);
        responseEntity.setMsg("您的账户已成功注销，感谢您的使用！3秒后将返回注册页面");
        return responseEntity;
    }

    /**
     * 删除用户
     *
     * @param request
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("用户信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        userMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setNeedLogin(Boolean.TRUE);
        responseEntity.setMsg("删除用户信息成功");
        return responseEntity;
    }

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> assignRole(String userId, List<String> roleIds) {
        logger.info("为用户分配角色入参, userId:{}, roleIds:{}", userId, JSON.toJSONString(roleIds));
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 先删除用户原有的角色关联
        userRoleMapper.deleteByUserId(userId);

        List<UserRoleDTO> userRoleDTOList = new ArrayList<>();
        String userName = RequestMsgUtil.getSessionUserName();

        if (!CollectionUtils.isEmpty(roleIds)) {
            roleIds.forEach(id -> {
                UserRoleDTO dto = new UserRoleDTO();
                dto.setUserId(userId);
                dto.setRoleId(id);
                dto.setUpdatedBy(userName);
                dto.setCreatedBy(userName);
                userRoleDTOList.add(dto);
            });
            // 批量添加角色权限关联
            userRoleMapper.batchInsert(userRoleDTOList);
        }

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("用户角色分配成功");
        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryUserRole(String userId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        responseEntity.setData(userRoleMapper.getByUserId(userId));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询用户角色列表成功");
        return responseEntity;
    }
}
