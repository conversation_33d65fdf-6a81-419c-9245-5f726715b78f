package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.InviteCodeDTO;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.InviteCodeMapper;
import com.edu.www.mapper.UserMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.InviteCodeService;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.UniqueCodeUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.InviteCodeVO;
import com.edu.www.vo.UserVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 邀请码管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class InviteCodeServiceImpl implements InviteCodeService {
    private static final Logger logger = LoggerFactory.getLogger(InviteCodeService.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private InviteCodeMapper inviteCodeMapper;

    /**
     * 根据ID查询邀请码
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询邀请码，id：{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "邀请码ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        InviteCodeVO inviteCodeVO = inviteCodeMapper.get(id);
        if (Objects.nonNull(inviteCodeVO)) {
            UserVO userVO = userMapper.getById(inviteCodeVO.getUserId());
            if (Objects.nonNull(userVO)) {
                inviteCodeVO.setUserId(userVO.getNameZh());
            }
        }
        responseEntity.setData(inviteCodeVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询邀请码成功");
        return responseEntity;
    }

    /**
     * 分页查询邀请码
     *
     * @param inviteCodeVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, InviteCodeVO inviteCodeVO) {
        logger.info("分页查询邀请码入参:{}", JSON.toJSONString(inviteCodeVO));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(inviteCodeVO.getPageStart(), inviteCodeVO.getPageSize());

        InviteCodeDTO inviteCodeDTO = new InviteCodeDTO();
        BeanUtils.copyProperties(inviteCodeVO, inviteCodeDTO);

        List<InviteCodeVO> inviteCodeVOList = inviteCodeMapper.query(inviteCodeDTO);
        if (!CollectionUtils.isEmpty(inviteCodeVOList)) {
            List<String> userIds = inviteCodeVOList.stream().map(InviteCodeVO::getUserId).collect(Collectors.toList());
            List<UserVO> userVOList = userMapper.getByIds(userIds);
            if (!CollectionUtils.isEmpty(userVOList)) {
                // 创建userId到nameZh的映射
                Map<String, String> userIdToNameMap = userVOList.stream()
                        .collect(Collectors.toMap(UserVO::getId, UserVO::getNameZh, (v1, v2) -> v1));

                // 更新inviteCodeVOList中的userId为对应的nameZh
                inviteCodeVOList.forEach(inviteCode -> {
                    String userId = inviteCode.getUserId();
                    if (userId != null && userIdToNameMap.containsKey(userId)) {
                        inviteCode.setUserId(userIdToNameMap.get(userId));
                    }
                });
            }
        }

        PageInfo<InviteCodeVO> pageInfo = new PageInfo<>(inviteCodeVOList);
        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询邀请码成功");
        return responseEntity;
    }

    /**
     * 生成邀请码
     *
     * @return
     */
    @Override
    public ResponseEntity<Object> generate(HttpServletRequest request) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无生成权限");
            return responseEntity;
        }

        InviteCodeDTO inviteCodeDTO = new InviteCodeDTO();
        String code = UniqueCodeUtil.generateInviteCode();
        Integer count = inviteCodeMapper.getByCodeCount(code);
        do {
            code = UniqueCodeUtil.generateInviteCode();
        } while (count != null && count > 0);
        inviteCodeDTO.setCode(code);
        // 默认设置为未使用状态
        inviteCodeDTO.setIsUsed(EduYesOrNoEnum.NO.getKey());
        inviteCodeDTO.setExpiryTime(DateUtil.addMinutesToCurrentDate(30));
        String userName = RequestMsgUtil.getSessionUserName();
        String currentDate = DateUtil.getCurrentDate(DateUtil.FORMAT_DATE_TIME_CHINESE);
        inviteCodeDTO.setDescription(userName + currentDate + "生成的邀请码");
        inviteCodeDTO.setCreatedBy(userName);
        inviteCodeDTO.setUpdatedBy(userName);

        inviteCodeMapper.insert(inviteCodeDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("生成邀请码成功");
        return responseEntity;
    }

    /**
     * 修改邀请码
     *
     * @param inviteCodeVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, InviteCodeVO inviteCodeVO) {
        logger.info("修改邀请码入参:{}", JSON.toJSONString(inviteCodeVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(inviteCodeVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(inviteCodeVO.getDescription()) &&
                inviteCodeVO.getDescription().length() > 255, "描述长度不能超过255个字符");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        InviteCodeDTO inviteCodeDTO = new InviteCodeDTO();
        BeanUtils.copyProperties(inviteCodeVO, inviteCodeDTO);

        String userName = RequestMsgUtil.getSessionUserName();
        inviteCodeDTO.setUpdatedBy(userName);
        inviteCodeDTO.setUpdatedAt(new Date());

        inviteCodeMapper.update(inviteCodeDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改邀请码成功");
        return responseEntity;
    }

    /**
     * 根据ID删除邀请码
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("邀请码已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "邀请码ID不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        inviteCodeMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除邀请码成功");
        return responseEntity;
    }
} 