package com.edu.www.service.impl;

import com.alibaba.excel.EasyExcel;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.constants.Constant;
import com.edu.www.convert.DutyConverter;
import com.edu.www.dto.DutyDTO;
import com.edu.www.enums.EduBaseGroupEnum;
import com.edu.www.enums.EduDPDutyTypeEnum;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.handler.DutyExcelWriteHandler;
import com.edu.www.handler.TitleWriteHandler;
import com.edu.www.mapper.DutyMapper;
import com.edu.www.po.DutyStatisticExcelPO;
import com.edu.www.service.CommonService;
import com.edu.www.service.DutyService;
import com.edu.www.utils.CommonUtil;
import com.edu.www.utils.DateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.DutyStatisticVO;
import com.edu.www.vo.DutyVO;
import com.edu.www.vo.TeacherVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 值日信息管理 Service实现
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class DutyServiceImpl implements DutyService {
    private static final Logger logger = LoggerFactory.getLogger(DutyServiceImpl.class);

    @Autowired
    private DutyMapper dutyMapper;

    @Autowired
    private CommonService commonService;

    // 缓存教师姓名映射，避免重复查询
    private Map<String, String> teacherNameCache;

    /**
     * 查询三个月的值日数据(上月、本月、下月)
     *
     * @param request
     * @return
     */
    @Override
    public ResponseEntity<Object> query(HttpServletRequest request) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        try {
            // 1. 计算三个月的日期范围
            Calendar calendar = Calendar.getInstance();

            // 上个月
            calendar.add(Calendar.MONTH, -1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date lastMonthStart = calendar.getTime();
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

            // 本月
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

            // 下个月
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date nextMonthEnd = calendar.getTime();

            // 2. 查询三个月的非基组数据
            List<DutyVO> threeMonthsData = queryNonBaseGroupDataByDateRange(lastMonthStart, nextMonthEnd);

            // 3. 按seqWeek分组数据，并组装为带ID的格式
            Map<String, Map<String, List<Map<String, String>>>> groupedDataWithId = DutyConverter.groupDataBySeqWeekWithId(threeMonthsData);

            // 4. 重新编号为连续的Week序号，去掉年月前缀
            Map<String, Map<String, List<Map<String, String>>>> finalData = DutyConverter.renumberWeeksWithId(groupedDataWithId);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("查询成功");
            responseEntity.setData(finalData);
        } catch (Exception e) {
            logger.error("查询三个月值日数据失败", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }
        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> detail(HttpServletRequest request) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        try {
            // 1. 计算三个月的日期范围
            Calendar calendar = Calendar.getInstance();

            // 上个月
            calendar.add(Calendar.MONTH, -1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date lastMonthStart = calendar.getTime();
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

            // 本月
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

            // 下个月
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date nextMonthEnd = calendar.getTime();

            // 2. 查询三个月的非基组数据
            List<DutyVO> threeMonthsData = queryNonBaseGroupDataByDateRange(lastMonthStart, nextMonthEnd);

            // 3. 按seqWeek分组数据，并组装为带ID的格式
            Map<String, Map<String, List<Map<String, String>>>> groupedDataWithId = DutyConverter.groupDataBySeqWeekWithId(threeMonthsData);

            // 4. 重新编号为连续的Week序号，去掉年月前缀
            Map<String, Map<String, List<Map<String, String>>>> finalData = DutyConverter.renumberWeeksWithId(groupedDataWithId);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("查询成功");
            responseEntity.setData(finalData);
        } catch (Exception e) {
            logger.error("查询三个月值日数据详情失败", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }
        return responseEntity;
    }

    /**
     * 预览自动填充结果
     *
     * @param request
     * @return
     */
    @Override
    public ResponseEntity<Object> previewAutoFill(HttpServletRequest request) {
        var responseEntity = new ResponseEntity<Object>();

        // 权限检查 - 使用Optional避免直接布尔判断
        return Optional.of(commonService.hasButtonPermission(request))
                .filter(permission -> permission)
                .map(permission -> generatePreviewData(responseEntity))
                .orElseGet(() -> {
                    responseEntity.setSuccess(Boolean.FALSE);
                    responseEntity.setMsg("无预览权限");
                    return responseEntity;
                });
    }

    /**
     * 生成预览数据的核心逻辑
     */
    private ResponseEntity<Object> generatePreviewData(ResponseEntity<Object> responseEntity) {
        try {
            // 1. 确定目标月份
            var targetMonth = determineTargetMonth();

            // 2. 获取基组元数据并验证
            var baseGroupMetaMap = getBaseGroupMetaMap();
            if (baseGroupMetaMap.isEmpty()) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未找到基组元数据，请先配置基组数据");
                return responseEntity;
            }

            // 3. 构建轮换配置
            var rotationConfig = buildRotationConfig(baseGroupMetaMap);

            // 4. 生成目标月份的完整日期
            var monthDates = generateCompleteMonthDate(targetMonth);

            // 5. 生成预览数据（不保存）
            var previewDuties = generateDutyData(monthDates, baseGroupMetaMap, rotationConfig);

            // 6. 将预览数据转换为与query接口相同的格式
            // convertDTOListToVOList

            var finalData = previewDuties.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            dtoList -> DutyConverter.convertDTOListToVOList(dtoList)))
                    .stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            dutyData -> DutyConverter.groupDataBySeqWeek(dutyData)))
                    .entrySet().stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    Map.Entry::getKey,
                                    Map.Entry::getValue,
                                    (e1, e2) -> e1,
                                    LinkedHashMap::new),
                            dutyData -> DutyConverter.renumberWeeks(dutyData)));

            responseEntity.setData(finalData);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("预览数据生成成功");

        } catch (Exception e) {
            logger.error("""
                    预览自动填充失败
                    错误信息: {}
                    错误堆栈: {}
                    """, e.getMessage(), e.getStackTrace());
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("预览失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 自动填充下个月值日数据
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public ResponseEntity<Object> autoFillNextMonth(HttpServletRequest request) {
        var responseEntity = new ResponseEntity<Object>();

        // 权限检查 - 使用Optional链式调用
        return Optional.of(commonService.hasButtonPermission(request))
                .filter(permission -> permission)
                .map(permission -> executeAutoFill(responseEntity))
                .orElseGet(() -> {
                    responseEntity.setSuccess(Boolean.FALSE);
                    responseEntity.setMsg("无新增权限");
                    return responseEntity;
                });
    }

    /**
     * 执行自动填充的核心逻辑
     */
    private ResponseEntity<Object> executeAutoFill(ResponseEntity<Object> responseEntity) {
        try {
            // 1. 确定目标月份
            var targetMonth = determineTargetMonth();

            // 2. 获取基组元数据并验证
            var baseGroupMetadata = getBaseGroupMetaMap();
            if (baseGroupMetadata.isEmpty()) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未找到基组元数据，请先配置基组数据");
                return responseEntity;
            }

            // 3. 构建轮换配置
            var rotationConfig = buildRotationConfig(baseGroupMetadata);

            // 4. 生成目标月份的完整日期
            var monthDates = generateCompleteMonthDate(targetMonth);

            // 5. 按周分组并生成数据
            var generatedDuties = generateDutyData(monthDates, baseGroupMetadata, rotationConfig);

            // 6. 批量保存 - 使用Stream API优化
            var savedCount = batchSaveDuties(generatedDuties);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增值日安排成功");

            logger.info("""
                            自动填充完成
                            目标月份: {}
                            生成记录数: {}
                            成功保存数: {}
                            """,
                    DateUtil.FormatDate(targetMonth, "yyyy-MM"),
                    generatedDuties.size(),
                    savedCount);

        } catch (Exception e) {
            logger.error("""
                    自动填充失败
                    错误信息: {}
                    错误类型: {}
                    """, e.getMessage(), e.getClass().getSimpleName(), e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("自动填充失败：" + e.getMessage());
            // 事务会自动回滚
        }

        return responseEntity;
    }

    /**
     * 导出值日信息到Excel
     *
     * @param request
     * @param response
     * @return
     */
    @Override
    public ResponseEntity<Object> exportDutyInfoToExcel(HttpServletRequest request, HttpServletResponse response) {
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 1. 权限检查
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无导出权限");
                return responseEntity;
            }

            // 2. 查询并验证数据
            List<DutyVO> allDutyData = queryAllDutyData();
            logger.info("查询到值日数据条数: {}", allDutyData.size());
            if (CollectionUtils.isEmpty(allDutyData)) {
                handleNoDataToExport(response);
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未查询到所要导出值日的信息");
                return responseEntity;
            }

            // 3. 设置响应头（在写入数据前设置）
            setExcelResponseHeaders(response);

            // 4. 导出Excel文件
            exportToExcelFile(response.getOutputStream(), allDutyData);

            logger.info("Excel导出完成，共导出 {} 条记录", allDutyData.size());
            // 成功导出Excel时不返回ResponseEntity，避免Spring序列化冲突
            return null;
        } catch (Exception e) {
            logger.error("导出值日信息失败", e);
            // 只在异常时返回错误信息
            if (!response.isCommitted()) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("导出值日信息失败！");
                return responseEntity;
            }
        }
        return responseEntity;
    }

    /**
     * 置换表格内容信息
     *
     * @param masterId 交换的课程表ID
     * @param slaveId  被交换的课程表ID
     * @return 响应结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> swap(HttpServletRequest request, String masterId, String slaveId) {
        logger.info("置换表格内容信息入参:交换的ID={},被交换的ID={}", masterId, slaveId);

        // 参数验证
        ValidateUtil.paramValidate(StringUtils.isBlank(masterId), "交换的ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(slaveId), "被交换的ID不能为空");
        ValidateUtil.paramValidate(masterId.equals(slaveId), "交换的ID不能与被交换的ID相同");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无置换权限");
            return responseEntity;
        }

        try {
            // 获取两个课程表信息
            DutyVO masterVO = dutyMapper.get(masterId);
            DutyVO slaveVO = dutyMapper.get(slaveId);

            // 验证记录是否存在
            if (masterVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("交换的值日表不存在，ID: " + masterId);
                return responseEntity;
            }

            if (slaveVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("被交换的值日表不存在，ID: " + slaveId);
                return responseEntity;
            }

            String userName = RequestMsgUtil.getSessionUserName();
            // 准备交换数据
            String masterTeacherId = masterVO.getTeacherId();
            String masterTeacherName = masterVO.getTeacherName();
            String slaveTeacherId = slaveVO.getTeacherId();
            String slaveTeacherName = slaveVO.getTeacherName();

            // 更新从课程表
            DutyDTO slaveDTO = new DutyDTO();
            slaveDTO.setId(slaveId);
            slaveDTO.setTeacherId(masterTeacherId);
            slaveDTO.setTeacherName(masterTeacherName);
            slaveDTO.setUpdatedBy(userName);

            // 更新主课程表
            DutyDTO masterDTO = new DutyDTO();
            masterDTO.setId(masterId);
            masterDTO.setTeacherId(slaveTeacherId);
            masterDTO.setTeacherName(slaveTeacherName);
            masterDTO.setUpdatedBy(userName);

            // 执行更新操作
            int slaveResult = dutyMapper.update(slaveDTO);
            int masterResult = dutyMapper.update(masterDTO);

            // 验证更新结果
            if (slaveResult <= 0 || masterResult <= 0) {
                logger.error("置换值日表失败: 主ID={}, 从ID={}, 主结果={}, 从结果={}",
                        masterId, slaveId, masterResult, slaveResult);
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("置换值日表失败");
                return responseEntity;
            }

            logger.info("成功置换值日表: 主ID={}, 从ID={}", masterId, slaveId);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("置换值日表表格内容信息成功");
        } catch (Exception e) {
            logger.error("置换值日表发生异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("置换值日表失败: " + e.getMessage());
            // 事务会自动回滚
        }

        return responseEntity;
    }

    /**
     * 统计值日信息
     *
     * @param request
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public ResponseEntity<Object> statistic(HttpServletRequest request, String startDate, String endDate) {
        logger.info("统计值日信息入参:开始日期={},结束日期={}", startDate, endDate);
        // 参数验证
        ValidateUtil.paramValidate(StringUtils.isBlank(startDate), "开始日期不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(endDate), "结束日期不能为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无统计权限");
            return responseEntity;
        }
        Map<String, DutyStatisticVO> map = new HashMap<>();

        DutyDTO queryDTO = new DutyDTO();
        queryDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        // 非基组数据
        queryDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey());
        List<DutyVO> dutyVOList = dutyMapper.queryByDateRange(
                queryDTO,
                DateUtil.stringToDate(startDate, DateUtil.FORMAT_DATE_SHORT),
                DateUtil.stringToDate(endDate, DateUtil.FORMAT_DATE_SHORT)
        );
        if (CollectionUtils.isEmpty(dutyVOList)) {
            responseEntity.setData(map);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("未查询到统计值日信息");
            return responseEntity;
        }

        TeacherVO teacherVO = commonService.getTeacherByEmpNo(Constant.ROBOT_EMP_NO);
        // 按teacherId分组统计
        Map<String, List<DutyVO>> groupedByTeacherId = dutyVOList.stream()
                .filter(duty -> StringUtils.isNotBlank(duty.getTeacherId()))
                .filter(dutyVO -> StringUtils.isNotBlank(teacherVO.getId()) && !teacherVO.getId().equals(dutyVO.getTeacherId()))
                .collect(Collectors.groupingBy(DutyVO::getTeacherId));

        // 为每个教师创建统计信息并设置序号
        int seq = 1;
        for (Map.Entry<String, List<DutyVO>> entry : groupedByTeacherId.entrySet()) {
            String teacherId = entry.getKey();
            List<DutyVO> teacherDuties = entry.getValue();

            DutyStatisticVO statisticVO = DutyConverter.createDutyStatistic(teacherDuties);
            statisticVO.setSeq(seq++);
            map.put(teacherId, statisticVO);
        }

        responseEntity.setData(map);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("统计成功");
        return responseEntity;
    }

    /**
     * 导出值日统计信息到Excel
     *
     * @param request
     * @param response
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public ResponseEntity<Object> exportStatisticToExcel(HttpServletRequest request, HttpServletResponse response, String startDate, String endDate) {
        logger.info("导出值日统计信息入参:开始日期={},结束日期={}", startDate, endDate);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            ValidateUtil.paramValidate(StringUtils.isBlank(startDate), "开始日期不能为空");
            ValidateUtil.paramValidate(StringUtils.isBlank(endDate), "结束日期不能为空");

            // 权限验证
            boolean permission = commonService.hasButtonPermission(request);
            if (!permission) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("无导出权限");
                return responseEntity;
            }

            // 获取统计数据
            Map<String, DutyStatisticVO> statisticData = getStatisticData(startDate, endDate);
            if (statisticData.isEmpty()) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未查询到统计数据");
                return responseEntity;
            }

            // 转换为Excel导出格式
            List<DutyStatisticExcelPO> excelData = DutyConverter.convertToExcelData(statisticData);

            // 设置响应头
            setStatisticExcelResponseHeaders(response, startDate, endDate);

            // 生成标题
            String title = generateExcelTitle(startDate, endDate);

            // 导出Excel文件，使用注解样式
            EasyExcel.write(response.getOutputStream(), DutyStatisticExcelPO.class)
                    .registerWriteHandler(new TitleWriteHandler(title))
                    .sheet("值日统计表")
                    .doWrite(excelData);

            logger.info("值日统计Excel导出完成，共导出 {} 条记录", excelData.size());
            return null; // 成功导出时返回null，避免Spring序列化冲突

        } catch (Exception e) {
            logger.error("导出值日统计信息失败", e);
            if (!response.isCommitted()) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("导出值日统计信息失败：" + e.getMessage());
                return responseEntity;
            }
        }
        return responseEntity;
    }

    /**
     * 获取统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计数据
     */
    private Map<String, DutyStatisticVO> getStatisticData(String startDate, String endDate) {
        Map<String, DutyStatisticVO> map = new HashMap<>();

        DutyDTO queryDTO = new DutyDTO();
        queryDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        queryDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey());

        List<DutyVO> dutyVOList = dutyMapper.queryByDateRange(
                queryDTO,
                DateUtil.stringToDate(startDate, DateUtil.FORMAT_DATE_SHORT),
                DateUtil.stringToDate(endDate, DateUtil.FORMAT_DATE_SHORT)
        );

        if (CollectionUtils.isEmpty(dutyVOList)) {
            return map;
        }

        TeacherVO teacherVO = commonService.getTeacherByEmpNo(Constant.ROBOT_EMP_NO);
        // 按teacherId分组统计
        Map<String, List<DutyVO>> groupedByTeacherId = dutyVOList.stream()
                .filter(duty -> StringUtils.isNotBlank(duty.getTeacherId()))
                .filter(dutyVO -> StringUtils.isNotBlank(teacherVO.getId()) && !teacherVO.getId().equals(dutyVO.getTeacherId()))
                .collect(Collectors.groupingBy(DutyVO::getTeacherId));

        // 为每个教师创建统计信息并设置序号
        int seq = 1;
        for (Map.Entry<String, List<DutyVO>> entry : groupedByTeacherId.entrySet()) {
            String teacherId = entry.getKey();
            List<DutyVO> teacherDuties = entry.getValue();
            DutyStatisticVO statisticVO = DutyConverter.createDutyStatistic(teacherDuties);
            statisticVO.setSeq(seq++);
            map.put(teacherId, statisticVO);
        }

        return map;
    }

    /**
     * 生成Excel标题
     */
    private String generateExcelTitle(String startDate, String endDate) {
        try {
            // 解析日期并格式化为 XX月XX日 格式
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputFormat = new SimpleDateFormat("MM月dd日");

            Date start = inputFormat.parse(startDate);
            Date end = inputFormat.parse(endDate);

            String formattedStartDate = outputFormat.format(start);
            String formattedEndDate = outputFormat.format(end);

            return "杭州世外中学DP部 " + formattedStartDate + "至" + formattedEndDate;
        } catch (Exception e) {
            logger.warn("日期格式化失败，使用原始日期", e);
            return "杭州世外中学DP部 " + startDate + "至" + endDate;
        }
    }

    /**
     * 设置统计Excel响应头
     *
     * @param response  响应对象
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    private void setStatisticExcelResponseHeaders(HttpServletResponse response, String startDate, String endDate) throws IOException {
        try {
            String fileName = String.format("值日统计表_%s至%s_%s.xlsx",
                    startDate.replace("-", ""),
                    endDate.replace("-", ""),
                    DateUtil.FormatDate(new Date(), DateUtil.FORMAT_DATE_TIME_COMPRESSED_UNDERSCORE));
            String encodedFileName = URLEncoder.encode(fileName, Constant.UTF_8);

            // 设置响应类型为Excel文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(Constant.UTF_8);

            // 设置文件下载头
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + encodedFileName);

            // 防止缓存
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);

            logger.info("设置统计Excel响应头完成，文件名: {}", fileName);
        } catch (Exception e) {
            logger.error("设置统计Excel响应头失败", e);
            throw new IOException("设置响应头失败", e);
        }
    }

    /**
     * 处理无数据导出的情况
     */
    private void handleNoDataToExport(HttpServletResponse response) {
        try {
            response.setStatus(HttpServletResponse.SC_NO_CONTENT);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"success\":false,\"message\":\"暂无数据可导出\"}");
        } catch (IOException e) {
            logger.error("写入无数据响应失败", e);
        }
    }

    /**
     * 导出到Excel文件
     */
    private void exportToExcelFile(OutputStream outputStream, List<DutyVO> dutyData) throws IOException {
        // 按周分组数据，保留完整的DutyVO信息
        var weeklyDataWithDetails = DutyConverter.groupDataBySeqWeekWithDetails(dutyData);
        var finalData = DutyConverter.renumberWeeksWithDetails(weeklyDataWithDetails);

        logger.info("按周分组完成，共 {} 周数据", finalData.size());

        // 使用EasyExcel写入，采用自定义处理器
        Map<String, Map<String, Object>> convertedData = DutyConverter.convertToHandlerFormat(finalData);
        EasyExcel.write(outputStream)
                .registerWriteHandler(new DutyExcelWriteHandler(convertedData))
                .sheet("值日安排表")
                .doWrite(new ArrayList<>());
    }

    /**
     * 查询所有值日数据（用于导出）
     */
    private List<DutyVO> queryAllDutyData() {
        try {
            var queryDTO = new DutyDTO();
            queryDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
            queryDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据

            var allData = dutyMapper.query(queryDTO);
            if (allData == null) {
                logger.warn("查询值日数据返回null");
                return new ArrayList<>();
            }

            // 过滤并排序数据
            var filteredData = allData.stream()
                    .filter(duty -> duty != null && duty.getDutyDate() != null)
                    .sorted(Comparator.comparing(DutyVO::getDutyDate))
                    .collect(Collectors.toList());

            logger.info("查询到有效值日数据 {} 条，过滤后 {} 条", allData.size(), filteredData.size());
            return filteredData;

        } catch (Exception e) {
            logger.error("查询值日数据失败", e);
            return new ArrayList<>();
        }
    }


    /**
     * 设置Excel响应头
     */
    private void setExcelResponseHeaders(HttpServletResponse response) throws IOException {
        try {
            var fileName = "值日安排表_" + DateUtil.FormatDate(new Date(), DateUtil.FORMAT_DATE_TIME_COMPRESSED_UNDERSCORE) + ".xlsx";
            var encodedFileName = URLEncoder.encode(fileName, Constant.UTF_8);

            // 设置响应类型为Excel文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(Constant.UTF_8);

            // 设置文件下载头
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + encodedFileName);

            // 防止缓存
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);

            logger.info("设置Excel响应头完成，文件名: {}", fileName);

        } catch (Exception e) {
            logger.error("设置Excel响应头失败", e);
            throw new IOException("设置响应头失败", e);
        }
    }

    /**
     * 确定目标月份
     */
    private Date determineTargetMonth() {
        // 查询所有非基组数据，按DUTY_DATE升序排序
        DutyDTO dutyDTO = new DutyDTO();
        dutyDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        dutyDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据
        List<DutyVO> nonBaseGroupData = dutyMapper.queryOrderByDutyDateAsc(dutyDTO);

        // 第一次新增，使用下个月
        if (CollectionUtils.isEmpty(nonBaseGroupData)) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.MONTH, 1);
            cal.set(Calendar.DAY_OF_MONTH, 1); // 设置为1号
            return cal.getTime();
        }
        // 非第一次，取最后一条记录的DUTY_DATE月份+1
        DutyVO lastRecord = nonBaseGroupData.get(nonBaseGroupData.size() - 1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(lastRecord.getDutyDate());
        calendar.add(Calendar.MONTH, 1); // 月份+1
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为1号
        return calendar.getTime();
    }

    /**
     * 获取基组元数据
     *
     * @return
     */
    private Map<String, List<DutyVO>> getBaseGroupMetaMap() {
        DutyDTO dutyDTO = new DutyDTO();
        dutyDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        dutyDTO.setIsBaseGroup(EduYesOrNoEnum.YES.getKey()); // 基组数据
        List<DutyVO> baseGroupData = dutyMapper.query(dutyDTO);
        // 按基组编码分组
        return baseGroupData.stream()
                .collect(Collectors.groupingBy(DutyVO::getBaseGroupCode));
    }

    /**
     * 构建轮换配置
     *
     * @param baseGroupMetaMap
     * @return
     */
    private Map<String, List<String>> buildRotationConfig(Map<String, List<DutyVO>> baseGroupMetaMap) {
        var rotationConfig = new HashMap<String, List<String>>();

        // 定义值日类型过滤列表
        var validDutyTypes = Set.of(
                EduDPDutyTypeEnum.YEAR_10_AB.getKey(),
                EduDPDutyTypeEnum.YEAR_11_AB.getKey(),
                EduDPDutyTypeEnum.YEAR_12_SPRING.getKey()
        );

        baseGroupMetaMap.entrySet().stream()
                .forEach(entry -> {
                    var baseGroupCode = entry.getKey();
                    var duties = entry.getValue();

                    // 为每个星期几构建轮换序列
                    IntStream.rangeClosed(1, 4) // 周一到周四
                            .forEach(weekday -> {
                                var weekdayKey = String.valueOf(weekday);

                                // 获取该星期几的值日类型1、2、3的教师作为轮换序列
                                var rotationSequence = duties.stream()
                                        .filter(duty -> weekdayKey.equals(duty.getWeekday()))
                                        .filter(duty -> validDutyTypes.contains(duty.getDutyType()))
                                        .sorted(Comparator.comparing(DutyVO::getDutyType))
                                        .map(DutyVO::getTeacherId)
                                        .collect(Collectors.toList());

                                var configKey = baseGroupCode + "_" + weekday;
                                rotationConfig.put(configKey, rotationSequence);

                                logger.debug("基组 {} 星期{} 的轮换序列: {}", baseGroupCode, weekday, rotationSequence);
                            });
                });

        return rotationConfig;
    }

    /**
     * 生成完整月份的日期
     */
    private List<Date> generateCompleteMonthDate(Date targetMonth) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(targetMonth);

        // 设置为目标月份的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date monthStart = calendar.getTime();

        // 设置为目标月份的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date monthEnd = calendar.getTime();

        // 生成从monthStart到monthEnd的所有日期
        List<Date> dates = new ArrayList<>();
        calendar.setTime(monthStart);

        while (!calendar.getTime().after(monthEnd)) {
            dates.add(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        return dates;
    }

    /**
     * 生成值日数据
     */
    private List<DutyDTO> generateDutyData(List<Date> monthDates,
                                           Map<String, List<DutyVO>> baseGroupMetadata,
                                           Map<String, List<String>> rotationConfig) {
        List<DutyDTO> allDuties = new ArrayList<>();

        // 按周分组日期
        List<List<Date>> weekGroups = groupDatesByWeek(monthDates);

        // 为每个基组的每个星期几分别维护值日类型0的轮换计数器
        // 从数据库查询上次的轮换位置，实现续接
        Map<String, Integer> rotationCounters = getLastRotationCounters(rotationConfig);

        // 查询上次最后使用的基组，确定基组交替的起始位置
        String lastBaseGroup = getLastUsedBaseGroup();
        boolean startWithGroupA = shouldStartWithGroupA(lastBaseGroup);

        // 按8周循环模式生成数据
        for (int weekIndex = 0; weekIndex < weekGroups.size(); weekIndex++) {
            List<Date> weekDates = weekGroups.get(weekIndex);

            // 确定当前周使用的基组（A和B交替，根据上次的基组续接）
            String currentBaseGroup;
            if (startWithGroupA) {
                currentBaseGroup = (weekIndex % 2 == 0) ? EduBaseGroupEnum.GROUP_A.getKey() : EduBaseGroupEnum.GROUP_B.getKey();
            } else {
                currentBaseGroup = (weekIndex % 2 == 0) ? EduBaseGroupEnum.GROUP_B.getKey() : EduBaseGroupEnum.GROUP_A.getKey();
            }

            logger.info("第{}周，使用基组{}", weekIndex + 1, currentBaseGroup);

            // 为该周的每一天生成值日数据
            int seqWeek = weekIndex + 1; // 周序号从1开始

            // 计算周五值日类型0的教师（使用固定5人轮换序列）
            String fridayDutyTeacher = getFridayDutyTeacher(weekIndex);

            for (Date date : weekDates) {
                List<DutyDTO> dayDuties = generateDayDutiesWithRotation(date, currentBaseGroup,
                        baseGroupMetadata.get(currentBaseGroup), seqWeek, fridayDutyTeacher,
                        rotationConfig, rotationCounters);
                allDuties.addAll(dayDuties);
            }
        }

        return allDuties;
    }

    /**
     * 查询上次最后使用的基组
     */
    private String getLastUsedBaseGroup() {
        var queryDTO = new DutyDTO();
        queryDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        queryDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据

        var lastRecords = dutyMapper.query(queryDTO);

        return Optional.ofNullable(lastRecords)
                .filter(records -> !records.isEmpty())
                .map(records -> records.stream()
                        .max(Comparator.comparing(DutyVO::getDutyDate))
                        .map(DutyVO::getBaseGroupCode)
                        .orElse(null))
                .map(lastBaseGroup -> {
                    logger.info("上次最后使用的基组: {}", lastBaseGroup);
                    return lastBaseGroup;
                })
                .orElseGet(() -> {
                    logger.info("未找到历史记录，从基组A开始");
                    return null;
                });
    }

    /**
     * 根据上次的基组确定这次应该从哪个基组开始
     */
    private boolean shouldStartWithGroupA(String lastBaseGroup) {
        if (StringUtils.isBlank(lastBaseGroup)) {
            // 第一次创建，从基组A开始
            logger.info("第一次创建，从基组A开始");
            return true;
        }

        if (EduBaseGroupEnum.GROUP_A.getKey().equals(lastBaseGroup)) {
            // 上次是基组A，这次从基组B开始
            logger.info("上次是基组A，这次从基组B开始");
            return false;
        }
        // 上次是基组B，这次从基组A开始
        logger.info("上次是基组B，这次从基组A开始");
        return true;
    }

    /**
     * 获取上次轮换的计数器位置，实现续接
     */
    private Map<String, Integer> getLastRotationCounters(Map<String, List<String>> rotationConfig) {
        Map<String, Integer> rotationCounters = new HashMap<>();

        // 初始化所有计数器为0
        for (String baseGroup : Arrays.asList(EduBaseGroupEnum.GROUP_A.getKey(), EduBaseGroupEnum.GROUP_B.getKey())) {
            for (int weekday = 1; weekday <= 4; weekday++) {
                String counterKey = baseGroup + "_" + weekday;
                rotationCounters.put(counterKey, 0);
            }
        }

        // 查询数据库中最后一次的值日记录
        DutyDTO queryDTO = new DutyDTO();
        queryDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        queryDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据
        queryDTO.setDutyType(EduDPDutyTypeEnum.DUTY.getKey()); // 值日类型0

        List<DutyVO> lastDutyRecords = dutyMapper.query(queryDTO);

        if (CollectionUtils.isEmpty(lastDutyRecords)) {
            logger.info("未找到历史值日记录，从头开始轮换");
            return rotationCounters;
        }

        // 按值日日期降序排序，获取最新的记录
        lastDutyRecords.sort((a, b) -> b.getDutyDate().compareTo(a.getDutyDate()));

        // 分析每个基组每个星期几的最后轮换位置
        for (String baseGroup : Arrays.asList(EduBaseGroupEnum.GROUP_A.getKey(), EduBaseGroupEnum.GROUP_B.getKey())) {
            for (int weekday = 1; weekday <= 4; weekday++) {
                String configKey = baseGroup + "_" + weekday;
                String counterKey = baseGroup + "_" + weekday;
                String weekdayStr = String.valueOf(weekday);

                List<String> rotationSequence = rotationConfig.get(configKey);
                if (rotationSequence == null || rotationSequence.isEmpty()) {
                    continue;
                }

                // 查找该基组该星期几的最后一次值日记录
                Optional<DutyVO> lastRecord = lastDutyRecords.stream()
                        .filter(duty -> baseGroup.equals(duty.getBaseGroupCode()))
                        .filter(duty -> weekdayStr.equals(duty.getWeekday()))
                        .filter(duty -> EduDPDutyTypeEnum.DUTY.getKey().equals(duty.getDutyType()))
                        .findFirst();

                if (lastRecord.isPresent()) {
                    String lastTeacherId = lastRecord.get().getTeacherId();
                    int lastIndex = rotationSequence.indexOf(lastTeacherId);

                    if (lastIndex >= 0) {
                        // 下次应该使用的索引（循环到下一个）
                        int nextIndex = (lastIndex + 1) % rotationSequence.size();
                        rotationCounters.put(counterKey, nextIndex);

                        logger.info("基组{}星期{}续接：上次教师索引{}, 下次从索引{}开始",
                                baseGroup, weekday, lastIndex, nextIndex);
                    }
                }
            }
        }

        return rotationCounters;
    }

    /**
     * 生成单天的值日数据（带轮换逻辑）
     */
    private List<DutyDTO> generateDayDutiesWithRotation(Date date, String baseGroupCode,
                                                        List<DutyVO> baseGroupData, int seqWeek,
                                                        String fridayDutyTeacher,
                                                        Map<String, List<String>> rotationConfig,
                                                        Map<String, Integer> rotationCounters) {
        List<DutyDTO> dayDuties = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        String weekday = String.valueOf(dayOfWeek - 1); // 转换为1-7的星期几

        // 确保每天都生成所有4种值日类型的记录
        EduDPDutyTypeEnum[] allDutyTypes = EduDPDutyTypeEnum.values();

        for (EduDPDutyTypeEnum dutyTypeEnum : allDutyTypes) {
            String dutyType = dutyTypeEnum.getKey();

            // 查找基组数据中是否有该值日类型的配置
            DutyVO baseData = baseGroupData.stream()
                    .filter(duty -> weekday.equals(duty.getWeekday()))
                    .filter(duty -> baseGroupCode.equals(duty.getBaseGroupCode()))
                    .filter(duty -> dutyType.equals(duty.getDutyType()))
                    .findFirst()
                    .orElse(null);

            // 创建值日记录
            DutyDTO dutyDTO;
            if (baseData != null) {
                // 有基组数据，使用基组数据创建
                dutyDTO = DutyConverter.createDutyFromBase(baseData, date, seqWeek);
            } else {
                // 没有基组数据，创建空记录
                dutyDTO = createEmptyDutyRecord(date, seqWeek, dutyType, weekday);
            }

            dutyDTO.setBaseGroupCode(baseGroupCode);

            // 特殊处理值日类型0：按星期几轮换
            if (EduDPDutyTypeEnum.DUTY.getKey().equals(dutyType)) {
                if (dayOfWeek == Calendar.FRIDAY) {
                    // 周五使用固定的5人轮换序列
                    dutyDTO.setTeacherName(fridayDutyTeacher);
                    String teacherId = getTeacherIdByName(fridayDutyTeacher);
                    dutyDTO.setTeacherId(teacherId);
                } else if (dayOfWeek >= Calendar.MONDAY && dayOfWeek <= Calendar.THURSDAY) {
                    // 周一到周四：按基组和星期几轮换
                    String configKey = baseGroupCode + "_" + (dayOfWeek - 1);
                    String counterKey = baseGroupCode + "_" + (dayOfWeek - 1);

                    List<String> rotationSequence = rotationConfig.get(configKey);
                    if (rotationSequence != null && !rotationSequence.isEmpty()) {
                        int rotationIndex = rotationCounters.get(counterKey);
                        String dutyType0Teacher = rotationSequence.get(rotationIndex);

                        dutyDTO.setTeacherId(dutyType0Teacher);
                        String teacherName = getTeacherNameById(dutyType0Teacher);
                        dutyDTO.setTeacherName(teacherName);

                        logger.info("基组{}星期{}值日（{}），轮换索引{}，教师: {}",
                                baseGroupCode, dayOfWeek - 1,
                                DateUtil.FormatDate(date, DateUtil.FORMAT_DATE_SHORT),
                                rotationIndex, teacherName);

                        // 增加该基组该星期几的计数器，为下次做准备
                        int nextIndex = (rotationIndex + 1) % rotationSequence.size();
                        rotationCounters.put(counterKey, nextIndex);
                    }
                }
            }

            dayDuties.add(dutyDTO);
        }

        return dayDuties;
    }

    /**
     * 按周分组日期
     */
    private List<List<Date>> groupDatesByWeek(List<Date> dates) {
        List<List<Date>> weekGroups = new ArrayList<>();
        List<Date> currentWeek = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();

        for (Date date : dates) {
            calendar.setTime(date);
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

            // 如果是周一且当前周不为空，开始新的一周
            if (dayOfWeek == Calendar.MONDAY && !currentWeek.isEmpty()) {
                weekGroups.add(new ArrayList<>(currentWeek));
                currentWeek.clear();
            }

            currentWeek.add(date);
        }

        // 添加最后一周
        if (!currentWeek.isEmpty()) {
            weekGroups.add(currentWeek);
        }

        return weekGroups;
    }

    /**
     * 获取周五值日类型0的教师（固定5人轮换序列）
     */
    private String getFridayDutyTeacher(int weekIndex) {
        int rotationIndex = weekIndex % CommonConstant.FRIDAY_DUTY_ROTATION.size();
        return CommonConstant.FRIDAY_DUTY_ROTATION.get(rotationIndex);
    }

    /**
     * 创建空的值日记录（当基组数据中没有该值日类型时）
     */
    private DutyDTO createEmptyDutyRecord(Date dutyDate, int seqWeek, String dutyType, String weekday) {
        DutyDTO dutyDTO = new DutyDTO();

        // 设置基本字段
        dutyDTO.setDepartment(EduDeptCodeEnum.DP.getDesc());
        dutyDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        dutyDTO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        dutyDTO.setSemester(CommonUtil.getCurrentSemester());

        dutyDTO.setDutyType(dutyType);
        // 设置新增数据的特有字段
        dutyDTO.setDutyDate(dutyDate);
        dutyDTO.setWeekday(weekday);
        dutyDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据
        dutyDTO.setSeqWeek(String.valueOf(seqWeek)); // 使用传入的周序号

        String userName = RequestMsgUtil.getSessionUserName();
        dutyDTO.setCreatedBy(userName);
        dutyDTO.setUpdatedBy(userName);
        dutyDTO.setCreatedAt(new Date());
        dutyDTO.setUpdatedAt(new Date());
        return dutyDTO;
    }

    /**
     * 批量保存值日记录
     */
    private int batchSaveDuties(List<DutyDTO> duties) {
        if (CollectionUtils.isEmpty(duties)) {
            logger.warn("批量保存的值日记录列表为空");
            return 0;
        }

        try {
            int savedCount = dutyMapper.batchInsert(duties);
            logger.info("批量保存完成，成功保存 {} 条记录", savedCount);
            return savedCount;
        } catch (Exception e) {
            logger.error("批量保存值日记录失败，记录数量: {}", duties.size(), e);
            return 0;
        }
    }

    /**
     * 根据教师ID获取教师姓名
     */
    private String getTeacherNameById(String teacherId) {
        try {
            // 初始化缓存
            if (teacherNameCache == null) {
                teacherNameCache = commonService.getTeacherName();
            }

            // 从缓存中获取教师姓名
            String teacherName = teacherNameCache.get(teacherId);
            if (teacherName != null) {
                return teacherName;
            } else {
                logger.warn("未找到教师ID对应的姓名: {}", teacherId);
                return "未知教师";
            }
        } catch (Exception e) {
            logger.error("获取教师姓名失败，teacherId: {}", teacherId, e);
            return "未知教师";
        }
    }

    /**
     * 根据教师姓名获取教师ID（反向查找）
     */
    private String getTeacherIdByName(String teacherName) {
        try {
            // 初始化缓存
            if (teacherNameCache == null) {
                teacherNameCache = commonService.getTeacherName();
            }

            // 反向查找教师ID
            for (Map.Entry<String, String> entry : teacherNameCache.entrySet()) {
                if (teacherName.equals(entry.getValue())) {
                    return entry.getKey();
                }
            }

            logger.warn("未找到教师姓名对应的ID: {}", teacherName);
            return "unknown_teacher_id";
        } catch (Exception e) {
            logger.error("获取教师ID失败，teacherName: {}", teacherName, e);
            return "unknown_teacher_id";
        }
    }

    /**
     * 根据日期范围查询非基组数据
     */
    private List<DutyVO> queryNonBaseGroupDataByDateRange(Date startDate, Date endDate) {
        DutyDTO queryDTO = new DutyDTO();
        queryDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        queryDTO.setIsBaseGroup(EduYesOrNoEnum.NO.getKey()); // 非基组数据

        // 使用Mapper查询日期范围内的数据
        return dutyMapper.queryByDateRange(queryDTO, startDate, endDate);
    }

}