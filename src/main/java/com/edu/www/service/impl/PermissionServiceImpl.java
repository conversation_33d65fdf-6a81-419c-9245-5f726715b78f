package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.PermissionDTO;
import com.edu.www.enums.EduMenuTypeEnum;
import com.edu.www.enums.EduPermissionTypeEnum;
import com.edu.www.enums.EduSwitchEnum;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.MenuMapper;
import com.edu.www.mapper.PermissionMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.PermissionService;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.vo.MenuVO;
import com.edu.www.vo.PermissionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限信息管理服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Service
public class PermissionServiceImpl implements PermissionService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PermissionMapper permissionMapper;

    /**
     * 根据ID查询权限信息
     *
     * @param id 权限ID
     * @return 权限信息
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "权限ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        PermissionVO permissionVO = permissionMapper.getById(id);
        responseEntity.setData(permissionVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询权限信息成功");
        return responseEntity;
    }

    /**
     * 分页查询权限信息
     *
     * @param permissionVO 查询条件
     * @return 权限列表
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, PermissionVO permissionVO) {
        logger.info("分页查询角色信息入参:{}", JSON.toJSONString(permissionVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PermissionDTO permissionDTO = new PermissionDTO();
        BeanUtils.copyProperties(permissionVO, permissionDTO);

        // 1. 查询所有权限
        List<PermissionVO> allList = permissionMapper.query(permissionDTO);

        // 2. 只保留页面类型的菜单权限和按钮权限
        List<PermissionVO> filteredList = allList.stream().filter(e -> {
            MenuVO menuVO = menuMapper.getById(e.getMenuId());
            return Objects.nonNull(menuVO) && EduMenuTypeEnum.PAGE.getKey().equals(menuVO.getType());
        }).collect(Collectors.toList());

        // 3. 按要求插入按钮权限
        Map<String, List<PermissionVO>> buttonMap = filteredList.stream()
                .filter(p -> EduPermissionTypeEnum.BUTTON.getKey().equals(p.getType()))
                .collect(Collectors.groupingBy(PermissionVO::getMenuId));
        List<PermissionVO> orderedList = new ArrayList<>();
        filteredList.stream()
                .filter(p -> EduPermissionTypeEnum.MENU.getKey().equals(p.getType()))
                .forEach(menu -> {
                    orderedList.add(menu);
                    List<PermissionVO> buttons = buttonMap.get(menu.getMenuId());
                    if (Objects.nonNull(buttons)) {
                        orderedList.addAll(buttons);
                    }
                });

        // 4. 手动分页
        int pageNum = permissionVO.getPageStart() == null ? 1 : permissionVO.getPageStart();
        int pageSize = permissionVO.getPageSize() == null ? 10 : permissionVO.getPageSize();
        int total = orderedList.size();
        int fromIndex = Math.max((pageNum - 1) * pageSize, 0);
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<PermissionVO> pageList = fromIndex < toIndex ? orderedList.subList(fromIndex, toIndex) : new ArrayList<>();

        PageInfo<PermissionVO> pageInfo = new PageInfo<>(pageList);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((total + pageSize - 1) / pageSize);

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询权限信息成功");
        return responseEntity;
    }

    /**
     * 分页查询权限信息
     *
     * @param permissionVO 查询条件
     * @return 权限列表
     */
    @Override
    public ResponseEntity<Object> queryByPage(HttpServletRequest request, PermissionVO permissionVO) {
        logger.info("分页查询权限信息入参:{}", JSON.toJSONString(permissionVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PermissionDTO permissionDTO = new PermissionDTO();
        BeanUtils.copyProperties(permissionVO, permissionDTO);

        // 1. 先分页查出权限列表
        PageHelper.startPage(permissionVO.getPageStart(), permissionVO.getPageSize());
        List<PermissionVO> permissionVOList = permissionMapper.query(permissionDTO);

        // 2. 批量获取所有涉及的菜单，避免N+1
        List<String> menuIds = permissionVOList.stream()
                .map(PermissionVO::getMenuId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, MenuVO> menuMap = new HashMap<>();
        for (String menuId : menuIds) {
            MenuVO menuVO = menuMapper.getById(menuId);
            if (menuVO != null) {
                menuMap.put(menuId, menuVO);
            }
        }

        // 3. 只保留页面菜单类型的权限
        List<PermissionVO> filteredList = permissionVOList.stream()
                .filter(e -> {
                    MenuVO menuVO = menuMap.get(e.getMenuId());
                    return menuVO != null && EduMenuTypeEnum.PAGE.getKey().equals(menuVO.getType());
                })
                .collect(Collectors.toList());

        // 4. 按要求插入按钮权限
        List<PermissionVO> orderedList = new ArrayList<>();
        Map<String, List<PermissionVO>> buttonMap = filteredList.stream()
                .filter(p -> EduPermissionTypeEnum.BUTTON.getKey().equals(p.getType()))
                .collect(Collectors.groupingBy(PermissionVO::getMenuId));
        filteredList.stream()
                .filter(p -> EduPermissionTypeEnum.MENU.getKey().equals(p.getType()))
                .forEach(menu -> {
                    orderedList.add(menu);
                    List<PermissionVO> buttons = buttonMap.get(menu.getMenuId());
                    if (buttons != null) {
                        orderedList.addAll(buttons);
                    }
                });
        PageInfo<PermissionVO> permissionVOPageInfo = new PageInfo<>(orderedList);
        responseEntity.setData(permissionVOPageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询权限信息成功");
        return responseEntity;
    }


    /**
     * 根据菜单ID查询权限列表
     *
     * @param menuId 菜单ID
     * @return 权限列表
     */
    @Override
    public ResponseEntity<Object> queryByMenuId(String menuId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(menuId), "菜单ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        List<PermissionVO> permissionVOList = permissionMapper.queryByMenuId(menuId);
        responseEntity.setData(permissionVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询菜单权限列表成功");
        return responseEntity;
    }

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public ResponseEntity<Object> queryByUserId(HttpServletRequest request, String userId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        List<PermissionVO> permissionVOList = permissionMapper.queryByUserId(userId);
        responseEntity.setData(permissionVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询用户权限列表成功");
        return responseEntity;
    }

    /**
     * 根据用户ID查询按钮权限列表
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    @Override
    public ResponseEntity<Object> queryButtonPermissionsByUserId(String userId) {
        ValidateUtil.paramValidate(StringUtils.isBlank(userId), "用户ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        List<PermissionVO> permissionVOList = permissionMapper.queryButtonPermissionsByUserId(userId);
        responseEntity.setData(permissionVOList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询用户按钮权限列表成功");
        return responseEntity;
    }

    /**
     * 校验用户是否有指定权限
     *
     * @param userId         用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    @Override
    public boolean hasPermission(String userId, String permissionCode) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(permissionCode)) {
            return false;
        }

        List<PermissionVO> permissionVOList = permissionMapper.queryByUserId(userId);
        if (permissionVOList == null || permissionVOList.isEmpty()) {
            return false;
        }

        return permissionVOList.stream()
                .anyMatch(permission -> permissionCode.equals(permission.getCode()));
    }

    /**
     * 校验用户是否有指定权限列表中的任意一个
     *
     * @param userId          用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否有权限
     */
    @Override
    public boolean hasAnyPermission(String userId, List<String> permissionCodes) {
        if (StringUtils.isBlank(userId) || permissionCodes == null || permissionCodes.isEmpty()) {
            return false;
        }

        List<PermissionVO> permissionVOList = permissionMapper.queryByUserId(userId);
        if (permissionVOList == null || permissionVOList.isEmpty()) {
            return false;
        }

        List<String> userPermissionCodes = permissionVOList.stream()
                .map(PermissionVO::getCode)
                .collect(Collectors.toList());

        return permissionCodes.stream()
                .anyMatch(userPermissionCodes::contains);
    }

    /**
     * 校验用户是否有指定权限列表中的所有权限
     *
     * @param userId          用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否有权限
     */
    @Override
    public boolean hasAllPermissions(String userId, List<String> permissionCodes) {
        if (StringUtils.isBlank(userId) || permissionCodes == null || permissionCodes.isEmpty()) {
            return false;
        }

        List<PermissionVO> permissionVOList = permissionMapper.queryByUserId(userId);
        if (permissionVOList == null || permissionVOList.isEmpty()) {
            return false;
        }

        List<String> userPermissionCodes = permissionVOList.stream()
                .map(PermissionVO::getCode)
                .collect(Collectors.toList());

        return permissionCodes.stream()
                .allMatch(userPermissionCodes::contains);
    }

    /**
     * 新增权限
     *
     * @param permissionVO 权限信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, PermissionVO permissionVO) {
        logger.info("新增权限信息入参:{}", JSON.toJSONString(permissionVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(permissionVO.getType()), "权限类型不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(permissionVO.getName()), "权限名称不能为空");
        ValidateUtil.paramValidate(StringUtils.isBlank(permissionVO.getCode()), "权限编码不能为空");
        if (EduPermissionTypeEnum.MENU.getKey().equals(permissionVO.getType())) {
            ValidateUtil.paramValidate(StringUtils.isBlank(permissionVO.getMenuId()), "菜单ID不能为空");
        }

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        // 检查权限编码是否已存在
        if (permissionMapper.existsByCode(permissionVO.getCode())) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("权限编码已存在");
            return responseEntity;
        }

        PermissionDTO permissionDTO = new PermissionDTO();
        BeanUtils.copyProperties(permissionVO, permissionDTO);

        // 根据菜单ID查询菜单信息
        MenuVO menuVO = menuMapper.getById(permissionVO.getMenuId());
        if (Objects.nonNull(menuVO)) {
            permissionDTO.setMenuParentId(menuVO.getParentId());
        }
        String userName = RequestMsgUtil.getSessionUserName();
        permissionDTO.setCreatedBy(userName);
        permissionDTO.setUpdatedBy(userName);

        permissionMapper.insert(permissionDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增权限成功");
        return responseEntity;
    }

    /**
     * 更新权限
     *
     * @param permissionVO 权限信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, PermissionVO permissionVO) {
        logger.info("更新权限信息入参:{}", JSON.toJSONString(permissionVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(permissionVO.getId()), "权限ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        // 检查权限编码是否已存在（排除自身）
        if (StringUtils.isNotBlank(permissionVO.getCode())) {
            PermissionDTO queryDTO = new PermissionDTO();
            queryDTO.setCode(permissionVO.getCode());
            List<PermissionVO> existPermissions = permissionMapper.query(queryDTO);
            if (existPermissions != null && !existPermissions.isEmpty() && !existPermissions.get(0).getId().equals(permissionVO.getId())) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("权限编码已存在");
                return responseEntity;
            }
        }

        PermissionDTO permissionDTO = new PermissionDTO();
        BeanUtils.copyProperties(permissionVO, permissionDTO);

        // 根据菜单ID查询菜单信息
        MenuVO menuVO = menuMapper.getById(permissionVO.getMenuId());
        if (Objects.nonNull(menuVO)) {
            permissionDTO.setMenuParentId(menuVO.getParentId());
        }
        permissionDTO.setUpdatedBy(RequestMsgUtil.getSessionUserName());
        permissionMapper.update(permissionDTO);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("更新权限成功");
        return responseEntity;
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "权限ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        // 删除权限
        permissionMapper.delete(id);

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除权限成功");
        return responseEntity;
    }
} 