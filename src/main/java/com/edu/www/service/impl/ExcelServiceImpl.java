package com.edu.www.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.edu.www.mapper.StudentMapper;
import com.edu.www.service.ExcelService;
import com.edu.www.utils.AmountUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.ItemDetailVO;
import com.edu.www.vo.ItemVO;
import com.edu.www.vo.SplitExpenseVO;
import com.edu.www.vo.StudentVO;
import com.edu.www.vo.TableContentVO;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.List;

@Service
public class ExcelServiceImpl implements ExcelService {
    private static final Logger logger = LoggerFactory.getLogger(ExcelService.class);

    @Autowired
    private StudentMapper studentMapper;

    /**
     * 生成分摊表
     *
     * @param splitExpenseVO
     * @return
     */
    @Override
    public byte[] generateSplitExpenseExcel(SplitExpenseVO splitExpenseVO) {
        logger.info("生成分摊表入参：{}", JSON.toJSONString(splitExpenseVO));
        ValidateUtil.paramValidate(CollectionUtils.isEmpty(splitExpenseVO.getStudentIds()), "学生ID不能为空");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Users Data");

        // Create header row
        String[] headers = {"ID", "Name", "Email", "Registration Date"};
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(Boolean.TRUE);
        headerStyle.setFont(headerFont);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 1. 标题合并和样式
//        WriteHandler titleHandler = new WriteHandler() {
//            @Override
//            public void afterCellDispose(CellWriteHandlerContext context) {
//                // 只处理第一行第一列
//                if (context.getRowIndex() == 0 && context.getColumnIndex() == 0) {
//                    Sheet sheet = context.getWriteSheetHolder().getSheet();
//                    // 合并A1到N1（假设14列，实际按你的表格列数调整）
//                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 13));
//                    Cell cell = context.getCell();
//                    CellStyle style = cell.getSheet().getWorkbook().createCellStyle();
//                    Font font = cell.getSheet().getWorkbook().createFont();
//                    font.setFontHeightInPoints((short) 20);
//                    font.setBold(true);
//                    style.setFont(font);
//                    style.setAlignment(HorizontalAlignment.CENTER);
//                    style.setVerticalAlignment(VerticalAlignment.CENTER);
//                    cell.setCellStyle(style);
//                }
//            }
//        };


        // 2. 表头样式
        CellStyle headStyle = workbook.createCellStyle();
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setBorderTop(BorderStyle.THIN);
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);

        Font headFont = workbook.createFont();
        headFont.setBold(true);
        headFont.setFontHeightInPoints((short) 12);
        headStyle.setFont(headFont);

        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setBorderTop(BorderStyle.THIN);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);

        // HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // List<DetailVO> detailVOList = getDetailVOList(splitExpenseVO);
        // if (CollectionUtils.isEmpty(detailVOList)) {
        //     return null;
        // }


        // Create data rows
//        for (User user : users) {
//            Row row = sheet.createRow(rowNum++);
//            row.createCell(0).setCellValue(user.getId());
//            row.createCell(1).setCellValue(user.getName());
//            row.createCell(2).setCellValue(user.getEmail());
//            row.createCell(3).setCellValue(user.getRegistrationDate().format(formatter));
//        }

        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        workbook.write(outputStream);
//        workbook.close();
        // outputStream.toByteArray();

        // 3. 写出
//        EasyExcel.write(outputStream, YourDataClass.class)
//                .registerWriteHandler(titleHandler)
//                .registerWriteHandler(styleStrategy)
//                .sheet("IC9")
//                .doWrite(dataList);
        return null;
    }


    /**
     * 获取表格内容VO
     *
     * @param splitExpenseVO
     * @return
     */
    private TableContentVO getDetailVOList(SplitExpenseVO splitExpenseVO) {
        TableContentVO tableContentVO = new TableContentVO();

        // 根据图片对应关系设置字段
        // IC9 → title
        tableContentVO.setTitle(splitExpenseVO.getTitle());

        // 9年级 → partName (这里需要根据实际业务逻辑获取年级信息)
        tableContentVO.setPartName(splitExpenseVO.getSheetName()); // 或者从其他地方获取年级信息

        // 合计（元）→ sum
        tableContentVO.setSum(splitExpenseVO.getSum());

        // 于勇涛 → creator
        tableContentVO.setCreator(splitExpenseVO.getCreator());

        // 设置总金额
        tableContentVO.setTotalAmount(splitExpenseVO.getTotalAmount());

        List<StudentVO> studentVOList = studentMapper.getByIds(splitExpenseVO.getStudentIds());
        if (CollectionUtils.isEmpty(studentVOList)) {
            return tableContentVO;
        }

        // 分摊金额计算
        List<BigDecimal> result = AmountUtil.splitExpense(splitExpenseVO.getTotalAmount(), studentVOList.size());

        List<ItemVO> itemVOList = Lists.newArrayList();

        // 修复循环边界错误：应该是 i < studentVOList.size()
        for (int i = 0; i < studentVOList.size(); i++) {
            ItemVO itemVO = new ItemVO();
            itemVO.setSeq(i + 1); // 序号从1开始
            itemVO.setNameZh(studentVOList.get(i).getNameZh());
            itemVO.setNameEn(studentVOList.get(i).getNameEn());
            itemVO.setStudentCode(studentVOList.get(i).getStudentCode());
            itemVO.setCardNum(studentVOList.get(i).getCardNum());

            // 如果需要设置分摊金额，可以添加到ItemDetailVO中
            if (i < result.size()) {
                // 这里可以根据需要创建ItemDetailVO来存储分摊金额
                List<ItemDetailVO> itemDetails = Lists.newArrayList();
                ItemDetailVO itemDetailVO = new ItemDetailVO();
                itemDetailVO.setDetailName("分摊费用");
                itemDetailVO.setQuantity(1);
                itemDetailVO.setAmount(result.get(i));
                itemDetails.add(itemDetailVO);
                itemVO.setItemDetails(itemDetails);
            }

            itemVOList.add(itemVO);
        }

        tableContentVO.setItems(itemVOList);

        return tableContentVO;
    }

}
