package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.dto.BookBorrowingDTO;
import com.edu.www.mapper.BookBorrowingMapper;
import com.edu.www.service.BookBorrowingService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.vo.BookBorrowingVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 书籍领用管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
public class BookBorrowingServiceImpl implements BookBorrowingService {
    private static final Logger logger = LoggerFactory.getLogger(BookBorrowingService.class);

    @Autowired
    private BookBorrowingMapper bookBorrowingMapper;

    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        logger.info("根据ID查询书籍领用信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍领用ID不能为空");
                return responseEntity;
            }

            // 查询书籍领用信息
            BookBorrowingVO bookBorrowingVO = bookBorrowingMapper.get(id);
            if (bookBorrowingVO == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍领用信息不存在");
                return responseEntity;
            }

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(bookBorrowingVO);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("根据ID查询书籍领用信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, BookBorrowingVO bookBorrowingVO) {
        logger.info("分页查询书籍领用信息入参:{}", JSON.toJSONString(bookBorrowingVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 设置分页参数
            PageHelper.startPage(bookBorrowingVO.getPageStart(), bookBorrowingVO.getPageSize());

            // 转换为DTO
            BookBorrowingDTO bookBorrowingDTO = new BookBorrowingDTO();
            BeanUtils.copyProperties(bookBorrowingVO, bookBorrowingDTO);

            // 查询数据
            List<BookBorrowingVO> bookBorrowingList = bookBorrowingMapper.query(bookBorrowingDTO);
            PageInfo<BookBorrowingVO> pageInfo = new PageInfo<>(bookBorrowingList);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setData(pageInfo);
            responseEntity.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("分页查询书籍领用信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("查询失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, BookBorrowingVO bookBorrowingVO) {
        logger.info("新增书籍领用信息入参:{}", JSON.toJSONString(bookBorrowingVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateBookBorrowing(bookBorrowingVO, false);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 转换为DTO
            BookBorrowingDTO bookBorrowingDTO = new BookBorrowingDTO();
            BeanUtils.copyProperties(bookBorrowingVO, bookBorrowingDTO);

            // 设置创建信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            bookBorrowingDTO.setCreatedBy(currentUser);
            bookBorrowingDTO.setUpdatedBy(currentUser);

            // 新增书籍领用信息
            bookBorrowingMapper.insert(bookBorrowingDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("新增成功");

        } catch (Exception e) {
            logger.error("新增书籍领用信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("新增失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, BookBorrowingVO bookBorrowingVO) {
        logger.info("修改书籍领用信息入参:{}", JSON.toJSONString(bookBorrowingVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            String validateResult = validateBookBorrowing(bookBorrowingVO, true);
            if (StringUtils.isNotBlank(validateResult)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg(validateResult);
                return responseEntity;
            }

            // 检查书籍领用是否存在
            BookBorrowingVO existBookBorrowing = bookBorrowingMapper.get(bookBorrowingVO.getId());
            if (existBookBorrowing == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍领用信息不存在");
                return responseEntity;
            }

            // 转换为DTO
            BookBorrowingDTO bookBorrowingDTO = new BookBorrowingDTO();
            BeanUtils.copyProperties(bookBorrowingVO, bookBorrowingDTO);

            // 设置修改信息
            String currentUser = RequestMsgUtil.getSessionUserName();
            bookBorrowingDTO.setUpdatedBy(currentUser);

            // 修改书籍领用信息
            bookBorrowingMapper.update(bookBorrowingDTO);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("修改成功");

        } catch (Exception e) {
            logger.error("修改书籍领用信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("修改失败：" + e.getMessage());
        }

        return responseEntity;
    }

    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("删除书籍领用信息入参:{}", id);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        try {
            // 参数验证
            if (StringUtils.isBlank(id)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍领用ID不能为空");
                return responseEntity;
            }

            // 检查书籍领用是否存在
            BookBorrowingVO existBookBorrowing = bookBorrowingMapper.get(id);
            if (existBookBorrowing == null) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("书籍领用信息不存在");
                return responseEntity;
            }

            // 删除书籍领用信息
            bookBorrowingMapper.delete(id);

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("删除成功");

        } catch (Exception e) {
            logger.error("删除书籍领用信息异常", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("删除失败：" + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 验证书籍领用信息
     */
    private String validateBookBorrowing(BookBorrowingVO bookBorrowingVO, boolean isUpdate) {
        if (bookBorrowingVO == null) {
            return "书籍领用信息不能为空";
        }

        if (isUpdate && StringUtils.isBlank(bookBorrowingVO.getId())) {
            return "书籍领用ID不能为空";
        }

        if (StringUtils.isBlank(bookBorrowingVO.getDepartment())) {
            return "部门名称不能为空";
        }

        if (StringUtils.isBlank(bookBorrowingVO.getDepartmentCode())) {
            return "部门编码不能为空";
        }

        if (StringUtils.isBlank(bookBorrowingVO.getYear())) {
            return "年度不能为空";
        }

        if (StringUtils.isBlank(bookBorrowingVO.getBorrowNo())) {
            return "领用单号不能为空";
        }

        if (StringUtils.isBlank(bookBorrowingVO.getBookId())) {
            return "书籍ID不能为空";
        }

        if (StringUtils.isBlank(bookBorrowingVO.getBorrowerName())) {
            return "领用人姓名不能为空";
        }

        return null;
    }
}
