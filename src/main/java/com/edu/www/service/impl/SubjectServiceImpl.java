package com.edu.www.service.impl;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.Constant;
import com.edu.www.dto.SubjectDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduGroupCodeEnum;
import com.edu.www.enums.EduSubjectCodeEnum;
import com.edu.www.mapper.SubjectMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.SubjectService;
import com.edu.www.utils.RequestMsgUtil;
import com.edu.www.utils.ValidateUtil;
import com.edu.www.vo.SubjectVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学科信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Service
public class SubjectServiceImpl implements SubjectService {
    private static final Logger logger = LoggerFactory.getLogger(SubjectService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private SubjectMapper subjectMapper;

    /**
     * 根据ID查询学科信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "学科ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        SubjectVO subjectVO = subjectMapper.getById(id);

        // 翻译部门编码
        if (subjectVO != null && StringUtils.isNotBlank(subjectVO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(subjectVO.getDepartmentCode());
            if (deptEnum != null) {
                subjectVO.setDepartmentName(deptEnum.getDesc());
            }
        }

        // 将逗号分隔的教授班级字符串转换为List
        convertTeachingClassToList(subjectVO);

        responseEntity.setData(subjectVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询学科信息成功");
        return responseEntity;
    }

    /**
     * 分页查询学科信息
     *
     * @param subjectVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, SubjectVO subjectVO) {
        logger.info("分页查询学科信息入参:{}", JSON.toJSONString(subjectVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(subjectVO.getPageStart(), subjectVO.getPageSize());
        SubjectDTO subjectDTO = new SubjectDTO();
        BeanUtils.copyProperties(subjectVO, subjectDTO);

        // 如果传入多个教授班级，将List转换为逗号分隔的字符串用于查询
        convertTeachingClassesToString(subjectDTO);

        List<SubjectVO> subjectVOList = subjectMapper.query(subjectDTO);

        // 翻译部门编码并处理教授班级
        if (subjectVOList != null && !subjectVOList.isEmpty()) {
            for (SubjectVO vo : subjectVOList) {
                if (StringUtils.isNotBlank(vo.getDepartmentCode())) {
                    EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(vo.getDepartmentCode());
                    if (deptEnum != null) {
                        vo.setDepartmentName(deptEnum.getDesc());
                    }
                }

                // 将逗号分隔的教授班级字符串转换为List
                convertTeachingClassToList(vo);
            }
        }

        PageInfo<SubjectVO> pageInfo = new PageInfo(subjectVOList);

        responseEntity.setData(pageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询学科信息成功");
        return responseEntity;
    }

    /**
     * 新增学科信息
     *
     * @param subjectVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, SubjectVO subjectVO) {
        logger.info("新增学科信息入参:{}", JSON.toJSONString(subjectVO));
        ValidateUtil.paramValidate(StringUtils.isNotBlank(subjectVO.getDescription()) &&
                subjectVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        SubjectDTO subjectDTO = new SubjectDTO();
        BeanUtils.copyProperties(subjectVO, subjectDTO);

        // 处理教授班级，将List转换为逗号分隔的字符串
        convertTeachingClassesToString(subjectDTO);

        if (StringUtils.isNotBlank(subjectDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(subjectDTO.getDepartmentCode());
            if (deptEnum != null) {
                subjectDTO.setDepartment(deptEnum.getDesc());
            }
        }
        if (StringUtils.isNotBlank(subjectDTO.getGroupCode())) {
            EduGroupCodeEnum groupCodeEnum = EduGroupCodeEnum.getByKey(subjectDTO.getGroupCode());
            if (groupCodeEnum != null) {
                subjectDTO.setGroupName(groupCodeEnum.getDesc());
            }
        }
        if (StringUtils.isNotBlank(subjectDTO.getCode())) {
            EduSubjectCodeEnum subjectCodeEnum = EduSubjectCodeEnum.getByKey(subjectDTO.getCode());
            if (subjectCodeEnum != null) {
                subjectDTO.setName(subjectCodeEnum.getDesc());
            }
        }
        String userName = RequestMsgUtil.getSessionUserName();
        subjectDTO.setCreatedBy(userName);
        subjectDTO.setUpdatedBy(userName);
        subjectMapper.insert(subjectDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增学科信息成功");
        return responseEntity;
    }

    /**
     * 修改学科信息
     *
     * @param subjectVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, SubjectVO subjectVO) {
        logger.info("修改学科信息入参:{}", JSON.toJSONString(subjectVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(subjectVO.getId()), "ID不能为空");
        ValidateUtil.paramValidate(StringUtils.isNotBlank(subjectVO.getDescription()) &&
                subjectVO.getDescription().length() > 255, "描述长度不能超过255个字符");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        SubjectDTO subjectDTO = new SubjectDTO();
        BeanUtils.copyProperties(subjectVO, subjectDTO);

        // 处理教授班级，将List转换为逗号分隔的字符串
        convertTeachingClassesToString(subjectDTO);

        if (StringUtils.isNotBlank(subjectDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(subjectDTO.getDepartmentCode());
            if (deptEnum != null) {
                subjectDTO.setDepartment(deptEnum.getDesc());
            }
        }
        if (StringUtils.isNotBlank(subjectDTO.getGroupCode())) {
            EduGroupCodeEnum groupCodeEnum = EduGroupCodeEnum.getByKey(subjectDTO.getGroupCode());
            if (groupCodeEnum != null) {
                subjectDTO.setGroupName(groupCodeEnum.getDesc());
            }
        }
        if (StringUtils.isNotBlank(subjectDTO.getCode())) {
            EduSubjectCodeEnum subjectCodeEnum = EduSubjectCodeEnum.getByKey(subjectDTO.getCode());
            if (subjectCodeEnum != null) {
                subjectDTO.setName(subjectCodeEnum.getDesc());
            }
        }
        String userName = RequestMsgUtil.getSessionUserName();
        subjectDTO.setUpdatedBy(userName);
        subjectMapper.update(subjectDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改学科信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除学科信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("学科信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "学科ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        subjectMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除学科信息成功");
        return responseEntity;
    }

    /**
     * 将逗号分隔的教授班级字符串转换为List
     *
     * @param subjectVO 学科VO对象
     */
    private void convertTeachingClassToList(SubjectVO subjectVO) {
        if (subjectVO != null && StringUtils.isNotBlank(subjectVO.getTeachingClassId())) {
            List<String> teachingClasses = Arrays.asList(subjectVO.getTeachingClassId().split(Constant.SEPARATOR));
            subjectVO.setTeachingClassIds(teachingClasses);
        }
    }

    /**
     * 将教授班级List转换为逗号分隔的字符串
     *
     * @param subjectDTO 学科DTO对象
     */
    private void convertTeachingClassesToString(SubjectDTO subjectDTO) {
        if (Objects.nonNull(subjectDTO) && !CollectionUtils.isEmpty(subjectDTO.getTeachingClassIds())) {
            String teachingClassString = String.join(Constant.SEPARATOR, subjectDTO.getTeachingClassIds());
            subjectDTO.setTeachingClassId(teachingClassString);
        }
    }
} 