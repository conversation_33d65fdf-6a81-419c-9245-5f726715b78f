package com.edu.www.service.impl;

import com.edu.www.client.managebac.MBStudentClient;
import com.edu.www.common.SyncResultEntity;
import com.edu.www.constants.Constant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.convert.StudentConverter;
import com.edu.www.dto.ClassesDTO;
import com.edu.www.dto.StudentDTO;
import com.edu.www.po.MBStudentPO;
import com.edu.www.po.MBStudentResponsePO;
import com.edu.www.enums.*;
import com.edu.www.mapper.ClassesMapper;
import com.edu.www.mapper.StudentMapper;
import com.edu.www.service.CommonService;
import com.edu.www.service.StudentService;
import com.edu.www.utils.*;
import com.edu.www.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 学生信息管理
 *
 * <AUTHOR>
 * @date 2020/11/17
 */
@Service
public class StudentServiceImpl implements StudentService {
    private static final Logger logger = LoggerFactory.getLogger(StudentService.class);

    @Value("${auth.token.mb}")
    private String authToken;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StudentMapper studentMapper;

    @Autowired
    private ClassesMapper classesMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 根据ID查询学生信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> get(HttpServletRequest request, String id) {
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "用户ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无查询权限");
            return responseEntity;
        }

        responseEntity.setData(studentMapper.getById(id));
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询学生信息成功");
        return responseEntity;
    }

    /**
     * 查询所有学生信息
     *
     * @return 所有学生信息列表
     */
    @Override
    public ResponseEntity<Object> queryAll() {
        logger.info("查询所有学生信息");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 使用空的DTO对象查询所有学生
        StudentDTO studentDTO = new StudentDTO();
        List<StudentVO> studentList = studentMapper.query(studentDTO);

        responseEntity.setData(studentList);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("查询所有学生信息成功");
        return responseEntity;
    }

    /**
     * 分页查询学生信息
     *
     * @param studentVO
     * @return
     */
    @Override
    public ResponseEntity<Object> queryPage(HttpServletRequest request, StudentVO studentVO) {
        logger.info("分页查询学生信息入参:{}", JSON.toJSONString(studentVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setData(new PageInfo());
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("无分页查询权限");
            return responseEntity;
        }

        PageHelper.startPage(studentVO.getPageStart(), studentVO.getPageSize());
        StudentDTO studentDTO = new StudentDTO();
        BeanUtils.copyProperties(studentVO, studentDTO);
        PageInfo<UserVO> userVOPageInfo = new PageInfo(studentMapper.query(studentDTO));

        responseEntity.setData(userVOPageInfo);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("分页查询学生信息成功");
        return responseEntity;
    }

    /**
     * 新增学生信息
     *
     * @param studentVO
     * @return
     */
    @Override
    public ResponseEntity<Object> insert(HttpServletRequest request, StudentVO studentVO) {
        logger.info("新增学生信息入参:{}", JSON.toJSONString(studentVO));
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无新增权限");
            return responseEntity;
        }

        StudentDTO studentDTO = new StudentDTO();
        BeanUtils.copyProperties(studentVO, studentDTO);
        if (StringUtils.isNotBlank(studentDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(studentDTO.getDepartmentCode());
            if (deptEnum != null) {
                studentDTO.setDepartmentName(deptEnum.getDesc());
            }
        }
        String userName = RequestMsgUtil.getSessionUserName();
        studentDTO.setCreatedBy(userName);
        studentDTO.setUpdatedBy(userName);
        studentMapper.insert(studentDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("新增学生信息成功");
        return responseEntity;
    }

    /**
     * 修改学生信息
     *
     * @param studentVO
     * @return
     */
    @Override
    public ResponseEntity<Object> update(HttpServletRequest request, StudentVO studentVO) {
        logger.info("修改学生信息入参:{}", JSON.toJSONString(studentVO));
        ValidateUtil.paramValidate(StringUtils.isBlank(studentVO.getId()), "ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无修改权限");
            return responseEntity;
        }

        StudentDTO studentDTO = new StudentDTO();
        BeanUtils.copyProperties(studentVO, studentDTO);
        if (StringUtils.isNotBlank(studentDTO.getDepartmentCode())) {
            EduDeptCodeEnum deptEnum = EduDeptCodeEnum.getByKey(studentDTO.getDepartmentCode());
            if (deptEnum != null) {
                studentDTO.setDepartmentName(deptEnum.getDesc());
            }
        }
        if (EduYesOrNoEnum.NO.getKey().equals(studentVO.getIsTransferred())) {
            studentDTO.setTransferInDate(null);
        }
        if (EduYesOrNoEnum.YES.getKey().equals(studentVO.getIsOnlyChild())) {
            studentDTO.setSituation(null);
        }
        if (!EduClothingSizeEnum.OTHER.getKey().equals(studentVO.getClothingSize())) {
            studentDTO.setSizeSituation(null);
        }
        String userName = RequestMsgUtil.getSessionUserName();
        studentDTO.setUpdatedBy(userName);
        studentMapper.update(studentDTO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("修改学生信息成功");
        return responseEntity;
    }

    /**
     * 根据ID删除学生信息
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> delete(HttpServletRequest request, String id) {
        logger.info("学生信息已被删除id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无删除权限");
            return responseEntity;
        }

        studentMapper.delete(id);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("删除学生信息成功");
        return responseEntity;
    }

    /**
     * 生成在读证明文档
     *
     * @param id
     * @return
     */
    @Override
    public ResponseEntity<Object> generateReadingCertificate(HttpServletRequest request, String id) {
        logger.info("生成在读证明文档id:{}", id);
        ValidateUtil.paramValidate(StringUtils.isBlank(id), "ID不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无生成权限");
            return responseEntity;
        }

        // 1. 获取学生信息
        StudentVO studentVO = studentMapper.getById(id);
        if (Objects.isNull(studentVO)) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("未找到对应学生信息");
            return responseEntity;
        }
        ReadingCertificateVO readingCertificateVO = new ReadingCertificateVO();
        readingCertificateVO.setTitleZh("在读证明");
        readingCertificateVO.setTitleEn("Schooling Certificate");

        readingCertificateVO.setSchoolNameZh("杭州上海世外中学");
        readingCertificateVO.setSchoolNameEn("Hangzhou World Foreign Language School");

        readingCertificateVO.setNameZh(StringUtils.isNotBlank(studentVO.getCardNameZh()) ? studentVO.getCardNameZh() : studentVO.getNameZh());
        readingCertificateVO.setNameEn(StringUtils.isNotBlank(studentVO.getCardNameEn()) ? studentVO.getCardNameEn() : studentVO.getNameEn());


        readingCertificateVO.setGenderZh(studentVO.getGender() == 0 ? "女" : "男");
        readingCertificateVO.setGenderEn(studentVO.getGender() == 0 ? "Female" : "Male");

        readingCertificateVO.setBirthDateZh(DateUtil.formatReadingCertificateDate(studentVO.getBirthDate(), false, false));
        readingCertificateVO.setBirthDateEn(DateUtil.formatReadingCertificateDate(studentVO.getBirthDate(), true, false));

        readingCertificateVO.setAdmissionDateZh(DateUtil.formatReadingCertificateDate(studentVO.getAdmissionDate(), false, true));
        readingCertificateVO.setAdmissionDateEn(DateUtil.formatReadingCertificateDate(studentVO.getAdmissionDate(), true, true));

        Date currentDate = new Date();
        readingCertificateVO.setCurrentDateZh(DateUtil.formatReadingCertificateDate(currentDate, false, true));
        readingCertificateVO.setCurrentDateEn(DateUtil.formatReadingCertificateDate(currentDate, true, true));
        String cardType = studentVO.getCardType();
        if (StringUtils.isNotBlank(cardType) && EduCardTypeEnum.PASSPORT.getKey().equals(cardType)) {
            if (EduDeptCodeEnum.IC.getKey().equals(studentVO.getDepartmentCode())) {
                readingCertificateVO.setCardTypeTipZh("护照号：");
                readingCertificateVO.setCardTypeTipEn("Passport number:");
                readingCertificateVO.setCardNum(studentVO.getCardNum());
            } else {
                readingCertificateVO.setCardTypeTipZh("护照号");
                readingCertificateVO.setCardTypeTipEn("Passport number is");
                readingCertificateVO.setCardNum(studentVO.getCardNum());
            }
        } else {
            if (EduDeptCodeEnum.IC.getKey().equals(studentVO.getDepartmentCode())) {
                readingCertificateVO.setCardTypeTipZh("身份证号：");
                readingCertificateVO.setCardTypeTipEn("ID number:");
                readingCertificateVO.setCardNum(studentVO.getCardNum());
            } else {
                readingCertificateVO.setCardTypeTipZh("身份证号");
                readingCertificateVO.setCardTypeTipEn("ID number is");
                readingCertificateVO.setCardNum(studentVO.getCardNum());
            }
        }

        // 代词(英文)
        readingCertificateVO.setPronounEn(studentVO.getGender() == 0 ? "She" : "He");

        // 部门编号
        readingCertificateVO.setDeptCode(studentVO.getDepartmentCode());
        // 年级
        if (EduDeptCodeEnum.IC.getKey().equals(studentVO.getDepartmentCode())) {
            if (StringUtils.isNotBlank(studentVO.getClassId())) {
                ClassesVO classesVO = classesMapper.getById(studentVO.getClassId());
                if (Objects.nonNull(classesVO)) {
                    readingCertificateVO.setGradeZh(classesVO.getGradeName());
                    readingCertificateVO.setGradeEn("Grade " + classesVO.getGradeCode());
                }
            }
        }


        responseEntity.setData(readingCertificateVO);
        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setMsg("生成在读证明成功");
        return responseEntity;
    }

    /**
     * 生成多学生在读证明
     *
     * @param ids 学生ID列表
     * @return 证明信息列表
     */
    @Override
    public ResponseEntity<Object> generateGroupReadingCertificate(HttpServletRequest request, List<String> ids) {
        logger.info("生成多学生在读证明文档ids:{}", JSON.toJSONString(ids));

        // 参数校验
        ValidateUtil.paramValidate(CollectionUtils.isEmpty(ids), "学生ID列表不能为空");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无生成权限");
            return responseEntity;
        }

        try {
            // 批量获取学生信息
            List<StudentVO> studentList = studentMapper.getByIds(ids);
            if (CollectionUtils.isEmpty(studentList)) {
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未找到对应学生信息");
                return responseEntity;
            }

            GroupReadingCertificateVO certificate = new GroupReadingCertificateVO();
            certificate.setTitle("在读证明");
            certificate.setSchoolName("杭州上海世外中学");
            certificate.setCurrentDate(DateUtil.formatReadingCertificateDate(new Date(), false, true));

            // 处理入学日期
            StudentConverter.processAdmissionDates(studentList, certificate);

            // 处理年级信息
            processGradeInfo(studentList, certificate);

            // 处理学生证件信息
            certificate.setVoList(StudentConverter.getStudentCardNumVOList(studentList));

            responseEntity.setData(certificate);
            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg("生成多学生在读证明成功");
            return responseEntity;
        } catch (Exception e) {
            logger.error("生成多学生在读证明失败", e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("生成多学生在读证明失败: " + e.getMessage());
            return responseEntity;
        }
    }

    @Override
    public ResponseEntity<Object> randomGroup(Integer groupNum) {
        ValidateUtil.paramValidate(Objects.isNull(groupNum), "请选择组号");
        ResponseEntity<Object> responseEntity = new ResponseEntity<>();
        int maxGroupNum = CommonConstant.specialStudents.size();  // 基组学生有5个
        if (groupNum > maxGroupNum) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("最多支持分" + maxGroupNum + "组");
            return responseEntity;
        }

        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());

        ClassesDTO classesDTO = new ClassesDTO();
        classesDTO.setDepartmentCode(EduDeptCodeEnum.DP.getKey());
        classesDTO.setGradeCode(EduGradeCodeEnum.SPRING.getKey());
        List<ClassesVO> classesVOList = classesMapper.query(classesDTO);
        if (!CollectionUtils.isEmpty(classesVOList)) {
            studentDTO.setClassId(classesVOList.get(NumberUtils.INTEGER_ZERO).getId());
        }
        List<StudentVO> studentList = studentMapper.query(studentDTO);

        if (CollectionUtils.isEmpty(studentList)) {
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("未找到学生信息");
            return responseEntity;
        }

        // Redis键定义
        String baseGroupKey = "student:randomGroup:baseGroup";  // 存储基组学生的键
        String normalGroupKey = "student:randomGroup:normalGroup";  // 存储普通组学生的键
        if (NumberUtils.INTEGER_ONE == groupNum) {
            // 清除Redis中该方法存在的所有键值对
            Set<String> baseKeys = redisUtil.keys(baseGroupKey + ":*");
            Set<String> normalKeys = redisUtil.keys(normalGroupKey + ":*");

            // 删除所有基组学生键
            if (!CollectionUtils.isEmpty(baseKeys)) {
                logger.info("清除基组学生缓存，键数量：{}", baseKeys.size());
                for (String key : baseKeys) {
                    redisUtil.delete(key);
                }
            }

            // 删除所有普通组学生键
            if (!CollectionUtils.isEmpty(normalKeys)) {
                logger.info("清除普通组学生缓存，键数量：{}", normalKeys.size());
                for (String key : normalKeys) {
                    redisUtil.delete(key);
                }
            }

            // 筛选出基组学生
            List<StudentVO> baseGroupStudents = studentList.stream()
                    .filter(student -> EduCardTypeEnum.IDCARD.getKey().equals(student.getCardType())
                            && CommonConstant.specialStudents.contains(student.getCardNum()))
                    .collect(Collectors.toList());

            // 筛选出普通组学生（排除基组学生）
            List<StudentVO> normalGroupStudents = studentList.stream()
                    .filter(student -> !(EduCardTypeEnum.IDCARD.getKey().equals(student.getCardType())
                            && CommonConstant.specialStudents.contains(student.getCardNum())))
                    .collect(Collectors.toList());

            // 存储基组学生到Redis
            for (StudentVO student : baseGroupStudents) {
                redisUtil.set(baseGroupKey + ":" + student.getId(), JSON.toJSONString(student), 10 * 60);  // 10分钟过期
            }

            // 存储普通组学生到Redis
            for (StudentVO student : normalGroupStudents) {
                redisUtil.set(normalGroupKey + ":" + student.getId(), JSON.toJSONString(student), 10 * 60);  // 10分钟过期
            }
        }

        // 获取所有基组学生的键
        Set<String> baseGroupKeys = redisUtil.keys(baseGroupKey + ":*");
        // 获取所有普通组学生的键
        Set<String> normalGroupKeys = redisUtil.keys(normalGroupKey + ":*");

        Set<StudentVO> currentGroupStudents = new HashSet<>();

        // 计划分配的Redis键
        String groupPlanKey = "student:randomGroup:plan";
        int normalStudentsPerGroup = 0;

        // 第1组时，计算所有组的分配计划并存储到Redis
        if (NumberUtils.INTEGER_ONE.equals(groupNum)) {
            // 计算每组应该分配的普通学生数量
            int normalStudentsTotal = normalGroupKeys.size();
            int baseGroupStudentsTotal = baseGroupKeys.size();
            int totalStudents = normalStudentsTotal + baseGroupStudentsTotal;

            // 存储分配计划到Redis
            Map<String, Integer> groupPlan = new HashMap<>();

            if (!CollectionUtils.isEmpty(normalGroupKeys)) {
                // 检查总学生数是否能被基组数量整除
                if (totalStudents % maxGroupNum == 0) {
                    // 如果能整除，则均分普通组学生
                    int studentsPerGroup = totalStudents / maxGroupNum;
                    // 每组的普通组学生数量 = 每组总数 - 基组学生数量(1)
                    int normalCount = studentsPerGroup - 1;
                    // 所有组均分配相同数量的普通组学生
                    for (int i = 1; i <= maxGroupNum; i++) {
                        groupPlan.put("group:" + i, normalCount);
                    }
                } else {
                    // 如果不能整除，则使用类似Grouping的算法
                    int baseCount = normalStudentsTotal / maxGroupNum; // 平均每组分配的数量
                    int remainder = normalStudentsTotal % maxGroupNum; // 余数

                    // 分配普通组学生数量
                    for (int i = 1; i <= maxGroupNum; i++) {
                        int count = baseCount;
                        // 前remainder个组多分配1个学生
                        if (i <= remainder) {
                            count += 1;
                        }
                        groupPlan.put("group:" + i, count);
                    }
                }

                // 将分配计划存储到Redis，过期时间与学生数据一致
                redisUtil.set(groupPlanKey, JSON.toJSONString(groupPlan), 10 * 60);
            }

            // 获取当前组的分配数量
            normalStudentsPerGroup = groupPlan.getOrDefault("group:" + groupNum, 0);
        } else {
            // 从Redis中获取之前计算好的分配计划
            if (redisUtil.hasKey(groupPlanKey)) {
                String planJson = redisUtil.get(groupPlanKey).toString();
                Map<String, Integer> groupPlan = JSON.parseObject(planJson, new TypeReference<Map<String, Integer>>() {
                });
                normalStudentsPerGroup = groupPlan.getOrDefault("group:" + groupNum, 0);
            } else {
                // 如果没有找到分配计划，使用简单计算方法
                normalStudentsPerGroup = !CollectionUtils.isEmpty(normalGroupKeys) ?
                        (int) Math.ceil((double) normalGroupKeys.size() / maxGroupNum) : 0;
            }
        }

        // 1. 从基组学生中随机取一个
        if (!CollectionUtils.isEmpty(baseGroupKeys)) {
            List<String> baseGroupKeysList = new ArrayList<>(baseGroupKeys);
            Collections.shuffle(baseGroupKeysList, new Random(System.currentTimeMillis()));

            // 获取并删除一个基组学生
            if (!baseGroupKeysList.isEmpty()) {
                String randomBaseStudentKey = baseGroupKeysList.get(0);
                String studentJson = redisUtil.get(randomBaseStudentKey).toString();
                StudentVO baseStudent = JSON.parseObject(studentJson, StudentVO.class);
                currentGroupStudents.add(baseStudent);
                // 从Redis中删除已分配的基组学生
                redisUtil.delete(randomBaseStudentKey);
            }
        }

        // 3. 从普通组学生中随机取normalStudentsPerGroup个
        if (!CollectionUtils.isEmpty(normalGroupKeys)) {
            List<String> normalGroupKeysList = new ArrayList<>(normalGroupKeys);
            Collections.shuffle(normalGroupKeysList, new Random(System.currentTimeMillis()));

            // 获取并删除指定数量的普通组学生
            for (int i = 0; i < normalStudentsPerGroup && i < normalGroupKeysList.size(); i++) {
                String randomNormalStudentKey = normalGroupKeysList.get(i);
                String studentJson = redisUtil.get(randomNormalStudentKey).toString();
                StudentVO normalStudent = JSON.parseObject(studentJson, StudentVO.class);
                currentGroupStudents.add(normalStudent);

                // 从Redis中删除已分配的普通组学生
                redisUtil.delete(randomNormalStudentKey);
            }
        }

        // 构建返回结果
        RandomGroupStudentVO randomGroupStudentVO = new RandomGroupStudentVO();
        randomGroupStudentVO.setGroupNum(groupNum);
        randomGroupStudentVO.setStudentVOSet(currentGroupStudents);

        // 打印分组结果
        if (groupNum == 1) {
            logger.info("组号          中文名             英文名             性别");
        }
        currentGroupStudents.forEach(e -> {
            System.out.println();
            logger.info(groupNum + "             " + e.getNameZh() + "               " + e.getNameEn()
                    + "               " + (e.getGender() == 0 ? "女" : "男"));
        });

        responseEntity.setSuccess(Boolean.TRUE);
        responseEntity.setData(randomGroupStudentVO);
        responseEntity.setMsg("随机分组成功");
        return responseEntity;
    }


    /**
     * 处理学生数据同步
     *
     * @param mbStudentPOList ManageBac学生数据列表
     * @return 同步结果
     */
    private SyncResultEntity processStudentSync(List<MBStudentPO> mbStudentPOList) {
        SyncResultEntity syncResult = new SyncResultEntity();
        String currentUser = RequestMsgUtil.getSessionUserName();

        for (MBStudentPO mbStudent : mbStudentPOList) {
            try {
                // 根据学生编号查询是否已存在
                StudentDTO queryDTO = new StudentDTO();
                queryDTO.setStudentCode(mbStudent.getStudentId());
                StudentVO existingStudent = studentMapper.queryOne(queryDTO);

                if (Objects.nonNull(existingStudent)) {
                    // 更新现有学生信息
                    studentMapper.update(StudentConverter.buildUpdateStudentDTO(mbStudent, existingStudent.getId(), currentUser));
                    syncResult.incrementUpdate();
                    logger.debug("更新学生信息: {}", mbStudent.getStudentId());
                } else {
                    // 新增学生信息
                    studentMapper.insert(StudentConverter.buildInsertStudentDTO(mbStudent, currentUser));
                    syncResult.incrementInsert();
                    logger.debug("新增学生信息: {}", mbStudent.getStudentId());
                }
            } catch (Exception e) {
                logger.error("处理学生{}数据时发生错误: {}", mbStudent.getStudentId(), e.getMessage(), e);
                syncResult.incrementError();
            }
        }

        return syncResult;
    }

    /**
     * 同步学生信息（支持ManageBac API参数）
     *
     * @param request     HTTP请求
     * @param studentIds  学生ID列表（多个）
     * @param yearGroupId 年级组ID（单个）
     * @return 同步结果
     */
    @Override
    public ResponseEntity<Object> sync(HttpServletRequest request, List<String> studentIds, Long yearGroupId) {
        logger.info("开始同步学生信息，API参数 - 学生ID: {}, 年级组ID: {}", JSON.toJSON(studentIds), yearGroupId);

        // 参数验证
        ValidateUtil.paramValidate(StringUtils.isBlank(authToken), "API接口Token为空");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>();

        // 权限验证
        boolean permission = commonService.hasButtonPermission(request);
        if (!permission) {
            logger.warn("用户无同步权限");
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("无同步权限");
            return responseEntity;
        }

        try {
            // 从ManageBac API获取学生数据
            List<MBStudentPO> mbStudentPOList = syncWithApiFilters(studentIds, yearGroupId);

            if (CollectionUtils.isEmpty(mbStudentPOList)) {
                logger.info("未获取到符合条件的学生信息");
                responseEntity.setSuccess(Boolean.FALSE);
                responseEntity.setMsg("未获取到符合条件的学生信息");
                responseEntity.setData(StudentConverter.createSyncResult(0, 0, 0));
                return responseEntity;
            }

            // 处理学生数据同步
            SyncResultEntity syncResult = processStudentSync(mbStudentPOList);

            logger.info("学生信息同步完成，共处理{}名学生，新增{}名，更新{}名",
                    mbStudentPOList.size(), syncResult.getInsertCount(), syncResult.getUpdateCount());

            responseEntity.setSuccess(Boolean.TRUE);
            responseEntity.setMsg(String.format("同步成功，共处理%d名学生，新增%d名，更新%d名",
                    mbStudentPOList.size(), syncResult.getInsertCount(), syncResult.getUpdateCount()));

            // 构建详细返回结果
            Map<String, Object> result = StudentConverter.createSyncResult(mbStudentPOList.size(), syncResult.getInsertCount(), syncResult.getUpdateCount());
            result.put("filters", StudentConverter.buildApiFilterInfo(studentIds, yearGroupId));
            result.put("students", mbStudentPOList);
            responseEntity.setData(result);

        } catch (Exception e) {
            logger.error("同步学生信息失败: {}", e.getMessage(), e);
            responseEntity.setSuccess(Boolean.FALSE);
            responseEntity.setMsg("同步学生信息失败: " + e.getMessage());
        }

        return responseEntity;
    }

    /**
     * 同步学生信息（支持ManageBac API过滤条件）
     *
     * @param studentIds  学生ID列表
     * @param yearGroupId 年级组ID
     * @return 学生PO列表
     * @throws Exception 异常
     */
    public List<MBStudentPO> syncWithApiFilters(List<String> studentIds, Long yearGroupId) throws Exception {
        final int MAX_STUDENT_IDS_PER_BATCH = 5; // 每批最多处理5个学生ID

        logger.info("开始从ManageBac API获取学生信息，API过滤条件 - 学生ID数量: {}, 年级组ID: {}",
                studentIds != null ? studentIds.size() : 0, yearGroupId);

        // 如果没有指定学生ID或学生ID数量较少，使用原有逻辑
        if (CollectionUtils.isEmpty(studentIds) || studentIds.size() <= MAX_STUDENT_IDS_PER_BATCH) {
            return syncWithApiFiltersInternal(studentIds, yearGroupId);
        }

        // 大量学生ID，分批处理
        List<MBStudentPO> allStudents = new ArrayList<>();
        List<List<String>> batches = partitionList(studentIds, MAX_STUDENT_IDS_PER_BATCH);

        logger.info("学生ID数量较多({}个)，分成{}批处理", studentIds.size(), batches.size());

        for (int i = 0; i < batches.size(); i++) {
            List<String> batch = batches.get(i);
            logger.info("处理第{}/{}批学生ID: {}", i + 1, batches.size(), batch);

            try {
                List<MBStudentPO> batchStudents = syncWithApiFiltersInternal(batch, yearGroupId);
                allStudents.addAll(batchStudents);
                logger.info("第{}/{}批处理完成，获取到{}名学生", i + 1, batches.size(), batchStudents.size());

                // 批次间添加延迟，避免API限流
                if (i < batches.size() - 1) {
                    Thread.sleep(500);
                }
            } catch (Exception e) {
                logger.error("第{}/{}批处理失败: {}", i + 1, batches.size(), e.getMessage(), e);
                // 继续处理下一批，不中断整个流程
            }
        }

        logger.info("分批处理完成，总共获取到{}名学生", allStudents.size());
        return allStudents;
    }

    /**
     * 内部同步方法，处理单批学生ID
     */
    private List<MBStudentPO> syncWithApiFiltersInternal(List<String> studentIds, Long yearGroupId) throws Exception {
        MBStudentClient apiClient = new MBStudentClient();
        List<MBStudentPO> allStudents = new ArrayList<>();
        int totalSyncCount = 0;
        int currentPage = 1;
        int retryCount = 0;
        final int MAX_RETRY = 3;
        final int MAX_PAGES = 1000; // 最大页数限制，防止无限循环
        boolean hasMorePages = true;

        logger.debug("开始处理学生ID批次: {}, 年级组ID: {}", studentIds, yearGroupId);

        try {
            // 循环遍历所有页面直到遍历完毕
            while (hasMorePages && currentPage <= MAX_PAGES) {
                logger.debug("正在获取第{}页学生数据...", currentPage);

                // 安全检查：如果页数超过限制，停止循环
                if (currentPage > MAX_PAGES) {
                    logger.warn("已达到最大页数限制{}，停止同步", MAX_PAGES);
                    break;
                }

                try {
                    // 构建查询参数
                    Map<String, String> queryParams = StudentConverter.buildApiQueryParams(studentIds, yearGroupId, currentPage);
                    logger.debug("查询参数: {}", queryParams);

                    // 调用API获取数据
                    Optional<String> studentsResponse = apiClient.getStudentByPageWithParams(authToken, queryParams);
                    if (!studentsResponse.isPresent()) {
                        logger.warn("第{}页数据获取失败，尝试重试...", currentPage);
                        retryCount++;
                        if (retryCount >= MAX_RETRY) {
                            logger.error("第{}页数据获取失败，已达到最大重试次数{}，跳过该页", currentPage, MAX_RETRY);
                            currentPage++;
                            retryCount = 0;
                            continue;
                        }
                        Thread.sleep(1000); // 重试前等待1秒
                        continue;
                    }

                    // 重置重试计数
                    retryCount = 0;

                    // 解析响应为PO对象
                    String responseJson = studentsResponse.get();
                    logger.debug("第{}页API响应长度: {}", currentPage, responseJson.length());

                    MBStudentResponsePO responsePO = JSON.parseObject(responseJson, MBStudentResponsePO.class);

                    if (responsePO == null) {
                        logger.warn("第{}页响应解析失败，跳过该页", currentPage);
                        currentPage++;
                        continue;
                    }

                    if (CollectionUtils.isEmpty(responsePO.getStudents())) {
                        logger.info("第{}页没有学生数据，结束同步", currentPage);
                        break;
                    }

                    // 过滤符合条件的学生（二次过滤确保准确性）
                    List<MBStudentPO> filteredStudents = StudentConverter.filterApiStudents(responsePO.getStudents(),
                            studentIds, yearGroupId);
                    allStudents.addAll(filteredStudents);

                    logger.info("第{}页获取到{}名学生，过滤后{}名学生符合条件",
                            currentPage, responsePO.getStudents().size(), filteredStudents.size());

                    // 检查是否还有更多页面
                    if (responsePO.getMeta() != null) {
                        hasMorePages = currentPage < responsePO.getMeta().getTotalPages();
                        totalSyncCount = responsePO.getMeta().getTotalCount();
                        logger.debug("分页信息 - 当前页: {}, 总页数: {}, 总记录数: {}",
                                currentPage, responsePO.getMeta().getTotalPages(), totalSyncCount);
                    } else {
                        logger.warn("第{}页响应中缺少分页元数据，结束同步", currentPage);
                        hasMorePages = false;
                    }

                    currentPage++;

                } catch (Exception pageException) {
                    logger.error("处理第{}页数据时发生错误: {}", currentPage, pageException.getMessage(), pageException);
                    retryCount++;
                    if (retryCount >= MAX_RETRY) {
                        logger.error("第{}页处理失败，已达到最大重试次数{}，跳过该页", currentPage, MAX_RETRY);
                        currentPage++;
                        retryCount = 0; // 重置重试计数
                        continue; // 跳过当前页，继续下一页
                    }
                    // 如果还没达到最大重试次数，不增加currentPage，重试当前页
                }

                // 添加延迟避免API限流
                try {
                    Thread.sleep(200);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    logger.warn("线程被中断，停止同步");
                    break;
                }
            }

            logger.info("ManageBac API数据获取完成，共处理{}页数据，获取到{}名符合条件的学生，API总记录数: {}",
                    currentPage - 1, allStudents.size(), totalSyncCount);

        } catch (Exception e) {
            logger.error("同步学生信息异常：{}", e.getMessage(), e);
            throw new RuntimeException("同步学生信息失败: " + e.getMessage(), e);
        }

        return allStudents;
    }

    /**
     * 处理并设置年级信息
     *
     * @param studentList 学生列表
     * @param certificate 证书对象
     */
    private void processGradeInfo(List<StudentVO> studentList, GroupReadingCertificateVO certificate) {
        List<String> classIdsList = studentList.stream()
                .map(StudentVO::getClassId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(classIdsList)) {
            return;
        }

        List<ClassesVO> classesVOList = classesMapper.getByIds(classIdsList);
        if (CollectionUtils.isEmpty(classesVOList)) {
            return;
        }

        List<String> gradeNames = classesVOList.stream()
                .map(ClassesVO::getGradeName)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        String result = switch (gradeNames.size()) {
            case 0 -> "";
            case 1 -> gradeNames.get(0);
            case 2 -> String.join("和", gradeNames);
            default -> {
                // 除最后两个元素外的所有元素，用"、"连接
                String prefix = gradeNames.subList(0, gradeNames.size() - 2)
                        .stream()
                        .collect(Collectors.joining(Constant.COMMA));

                // 最后两个元素用"和"连接
                String lastTwo = String.join("和",
                        gradeNames.get(gradeNames.size() - 2),
                        gradeNames.get(gradeNames.size() - 1));

                yield prefix.isEmpty() ? lastTwo : prefix + Constant.COMMA + lastTwo;
            }
        };

        certificate.setGrade(result);
    }

    /**
     * 将列表分割成指定大小的批次
     *
     * @param list 原始列表
     * @param batchSize 批次大小
     * @return 分割后的批次列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return partitions;
        }

        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            partitions.add(new ArrayList<>(list.subList(i, end)));
        }

        return partitions;
    }

}
