package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.PurchaseOrderVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购订单管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface PurchaseOrderService {

    /**
     * 根据ID查询采购订单信息
     *
     * @param id 采购订单信息ID
     * @return 采购订单信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询采购订单信息
     *
     * @param purchaseOrderVO 查询条件
     * @return 采购订单信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, PurchaseOrderVO purchaseOrderVO);

    /**
     * 新增采购订单信息
     *
     * @param purchaseOrderVO 采购订单信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, PurchaseOrderVO purchaseOrderVO);

    /**
     * 修改采购订单信息
     *
     * @param purchaseOrderVO 采购订单信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, PurchaseOrderVO purchaseOrderVO);

    /**
     * 删除采购订单信息
     *
     * @param id 采购订单信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
