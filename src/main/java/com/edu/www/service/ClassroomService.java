package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.ClassroomVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 教室信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface ClassroomService {

    /**
     * 根据ID查询教室信息
     *
     * @param id 教室信息ID
     * @return 教室信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询教室信息
     *
     * @param classroomVO 查询条件
     * @return 教室信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, ClassroomVO classroomVO);

    /**
     * 根据部门编号查询教室信息
     *
     * @param deptCode 部门编号(SY：双语部、IC：融合部、DP：高中部)
     * @return 教室信息列表
     */
    ResponseEntity<Object> getClassroomByDeptCode(String deptCode);

    /**
     * 新增教室信息
     *
     * @param classroomVO 教室信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, ClassroomVO classroomVO);

    /**
     * 修改教室信息
     *
     * @param classroomVO 教室信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, ClassroomVO classroomVO);

    /**
     * 根据ID删除教室信息
     *
     * @param id 教室信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 