package com.edu.www.service;

import com.edu.www.po.MBYearGroupPO;
import com.edu.www.vo.FileVO;
import com.edu.www.vo.RescheduleVO;
import com.edu.www.vo.TeacherVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 公共服务
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface CommonService {

    /**
     * 获取未来6年的学年时间段列表
     *
     * @return
     */
    List<String> getAcademicYearList();

    /**
     * 是否有课(true：有课，false：无课)
     *
     * @param rescheduleVO
     * @param weekday
     * @param period
     * @param teacherId
     * @return
     */
    boolean isScheduled(RescheduleVO rescheduleVO, String weekday, String period, String teacherId);

    /**
     * 获取教师信息和ID
     *
     * @return
     */
    Map<String, TeacherVO> getTeacherInfo();

    /**
     * 获取教师名和ID
     *
     * @return
     */
    Map<String, String> getTeacherName();

    /**
     * 获取子学科名称和ID
     *
     * @return
     */
    Map<String, String> getSubSubjectName();

    /**
     * 判断是否为超级管理员
     *
     * @param userId
     * @return
     */
    boolean isSuperAdmin(String userId);

    /**
     * 校验是否有按钮权限
     *
     * @param request
     * @return
     */
    boolean hasButtonPermission(HttpServletRequest request);

    /**
     * 获取年级组信息
     *
     * @return 年级组信息列表
     */
    List<MBYearGroupPO> getYearGroups();

    /**
     * 根据工号获取教师信息
     *
     * @param empNo
     * @return
     */
    TeacherVO getTeacherByEmpNo(String empNo);

    /**
     * 根据多个文件ID查询文件信息
     *
     * @param fileIds
     * @return
     */
    Map<String, FileVO> getFileInfo(List<String> fileIds);
}
