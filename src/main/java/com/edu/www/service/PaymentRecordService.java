package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.PaymentRecordVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付记录管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface PaymentRecordService {

    /**
     * 根据ID查询支付记录信息
     *
     * @param id 支付记录信息ID
     * @return 支付记录信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询支付记录信息
     *
     * @param paymentRecordVO 查询条件
     * @return 支付记录信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, PaymentRecordVO paymentRecordVO);

    /**
     * 新增支付记录信息
     *
     * @param paymentRecordVO 支付记录信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, PaymentRecordVO paymentRecordVO);

    /**
     * 修改支付记录信息
     *
     * @param paymentRecordVO 支付记录信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, PaymentRecordVO paymentRecordVO);

    /**
     * 删除支付记录信息
     *
     * @param id 支付记录信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
