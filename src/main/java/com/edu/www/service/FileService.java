package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.FileVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 文件信息管理
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
public interface FileService {

    /**
     * 根据ID查询文件信息
     *
     * @param id 文件信息ID
     * @return 文件信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 根据文件ID查询文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    ResponseEntity<Object> getByFileId(HttpServletRequest request, String fileId);

    /**
     * 根据多个文件ID查询文件信息
     *
     * @param fileIds
     * @return
     */
    List<FileVO> getFileName(List<String> fileIds);

    /**
     * 分页查询文件信息
     *
     * @param fileVO 查询条件
     * @return 文件信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, FileVO fileVO);

    /**
     * 新增文件信息
     *
     * @param fileVO 文件信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, FileVO fileVO);

    /**
     * 修改文件信息
     *
     * @param fileVO 文件信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, FileVO fileVO);

    /**
     * 根据ID删除文件信息
     *
     * @param id 文件信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 根据文件ID删除文件信息
     *
     * @param fileId 文件ID
     * @return 处理结果
     */
    ResponseEntity<Object> deleteByFileId(HttpServletRequest request, String fileId);

    /**
     * 批量删除文件信息
     *
     * @param ids 文件信息ID列表
     * @return 处理结果
     */
    ResponseEntity<Object> batchDelete(HttpServletRequest request, String ids);

    /**
     * 根据文件类型查询文件信息
     *
     * @param fileType 文件类型
     * @return 文件信息列表
     */
    ResponseEntity<Object> queryByFileType(HttpServletRequest request, String fileType);
}
