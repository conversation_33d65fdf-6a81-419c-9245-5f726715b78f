package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.TeacherVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 教师信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface TeacherService {
    /**
     * 根据ID查询教师信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询教师信息
     *
     * @param teacherVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, TeacherVO teacherVO);

    /**
     * 查询所有教师信息
     *
     * @return
     */
    ResponseEntity<Object> getAllTeacher();

    /**
     * 根据部门编码查询教师信息
     *
     * @param departmentCode
     * @return
     */
    ResponseEntity<Object> getByDepartmentCode(String departmentCode);

    /**
     * 新增教师信息
     *
     * @param teacherVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, TeacherVO teacherVO);

    /**
     * 修改教师信息
     *
     * @param teacherVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, TeacherVO teacherVO);

    /**
     * 根据ID删除教师信息
     *
     * @param request
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 