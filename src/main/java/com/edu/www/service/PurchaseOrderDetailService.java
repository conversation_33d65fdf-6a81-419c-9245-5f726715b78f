package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.PurchaseOrderDetailVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购明细管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface PurchaseOrderDetailService {

    /**
     * 根据ID查询采购明细信息
     *
     * @param id 采购明细信息ID
     * @return 采购明细信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 根据采购单号查询采购详细信息
     *
     * @param request
     * @param purchaseId
     * @return
     */
    ResponseEntity<Object> getDetailByPurchaseId(HttpServletRequest request, String purchaseId);

    /**
     * 分页查询采购明细信息
     *
     * @param purchaseOrderDetailVO 查询条件
     * @return 采购明细信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, PurchaseOrderDetailVO purchaseOrderDetailVO);

    /**
     * 新增采购明细信息
     *
     * @param purchaseOrderDetailVO 采购明细信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, PurchaseOrderDetailVO purchaseOrderDetailVO);

    /**
     * 修改采购明细信息
     *
     * @param purchaseOrderDetailVO 采购明细信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, PurchaseOrderDetailVO purchaseOrderDetailVO);

    /**
     * 删除采购明细信息
     *
     * @param id 采购明细信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
