package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.RescheduleVO;
import com.edu.www.vo.ScheduleToolVO;

import javax.servlet.http.HttpServletRequest;


/**
 * 调课
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface ToolService {

    /**
     * 调课
     *
     * @param rescheduleVO
     * @return
     */
    ResponseEntity<Object> reschedule(RescheduleVO rescheduleVO);

    /**
     * 根据条件查询课表信息
     *
     * @param scheduleToolVO
     * @return
     */
    ResponseEntity<Object> query(HttpServletRequest request, ScheduleToolVO scheduleToolVO);


}
