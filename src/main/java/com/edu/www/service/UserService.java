package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.UserVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 查询用户信息
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface UserService {
    /**
     * 根据ID查询用户信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询用户信息
     *
     * @param userVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, UserVO userVO);

    /**
     * 用户注册
     *
     * @param userVO
     * @return
     */
    ResponseEntity<Object> register(UserVO userVO);

    /**
     * 新增用户
     *
     * @param userVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, UserVO userVO);

    /**
     * 用户修改信息
     *
     * @param userVO
     * @return
     */
    ResponseEntity<Object> modify(UserVO userVO);

    /**
     * 修改用户信息
     *
     * @param request
     * @param userVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, UserVO userVO);

    /**
     * 修改密码
     *
     * @param id
     * @param oldPwd
     * @param newPwd
     * @return
     */
    ResponseEntity<Object> modifyPwd(String id, String oldPwd, String newPwd);

    /**
     * 修改密码
     *
     * @param request
     * @param id
     * @param oldPwd
     * @param newPwd
     * @return
     */
    ResponseEntity<Object> updatePwd(HttpServletRequest request, String id, String oldPwd, String newPwd);

    /**
     * 用户登录
     *
     * @param userVO
     * @return
     */
    ResponseEntity<Object> login(UserVO userVO);

    /**
     * 用户登出
     *
     * @param username
     * @return
     */
    ResponseEntity<Object> logout(String username);

    /**
     * 用户注销
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> logoff(String id);

    /**
     * 删除用户
     *
     * @param request
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     * @return 结果
     */
    ResponseEntity<Object> assignRole(String userId, List<String> roleIds);

    /**
     * 查询用户角色列表
     *
     * @param userId
     * @return
     */
    ResponseEntity<Object> queryUserRole(String userId);
}
