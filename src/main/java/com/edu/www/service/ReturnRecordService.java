package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.ReturnRecordVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 归还记录管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface ReturnRecordService {

    /**
     * 根据ID查询归还记录信息
     *
     * @param id 归还记录信息ID
     * @return 归还记录信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询归还记录信息
     *
     * @param returnRecordVO 查询条件
     * @return 归还记录信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, ReturnRecordVO returnRecordVO);

    /**
     * 新增归还记录信息
     *
     * @param returnRecordVO 归还记录信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, ReturnRecordVO returnRecordVO);

    /**
     * 修改归还记录信息
     *
     * @param returnRecordVO 归还记录信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, ReturnRecordVO returnRecordVO);

    /**
     * 删除归还记录信息
     *
     * @param id 归还记录信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
