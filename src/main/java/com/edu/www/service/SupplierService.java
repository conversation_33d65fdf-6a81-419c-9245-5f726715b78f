package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.SupplierVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 供应商信息管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface SupplierService {

    /**
     * 根据ID查询供应商信息
     *
     * @param id 供应商信息ID
     * @return 供应商信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询供应商信息
     *
     * @param supplierVO 查询条件
     * @return 供应商信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, SupplierVO supplierVO);

    /**
     * 新增供应商信息
     *
     * @param supplierVO 供应商信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, SupplierVO supplierVO);

    /**
     * 修改供应商信息
     *
     * @param supplierVO 供应商信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, SupplierVO supplierVO);

    /**
     * 删除供应商信息
     *
     * @param id 供应商信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);

    /**
     * 生成供应商编码
     *
     * @param request HTTP请求
     * @return 生成的供应商编码
     */
    ResponseEntity<Object> generateCode(HttpServletRequest request);

    /**
     * 查询所有供应商列表
     *
     * @return 所有供应商列表(key=id, value=supplierName)
     */
    ResponseEntity<Object> getAll();
}
