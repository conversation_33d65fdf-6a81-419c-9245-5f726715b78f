package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.InboundRecordVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 入库记录管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface InboundRecordService {

    /**
     * 根据ID查询入库记录信息
     *
     * @param id 入库记录信息ID
     * @return 入库记录信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询入库记录信息
     *
     * @param inboundRecordVO 查询条件
     * @return 入库记录信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, InboundRecordVO inboundRecordVO);

    /**
     * 新增入库记录信息
     *
     * @param inboundRecordVO 入库记录信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, InboundRecordVO inboundRecordVO);

    /**
     * 修改入库记录信息
     *
     * @param inboundRecordVO 入库记录信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, InboundRecordVO inboundRecordVO);

    /**
     * 删除入库记录信息
     *
     * @param id 入库记录信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
