package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.ScheduleVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 课程表信息管理
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public interface ScheduleService {
    /**
     * 根据ID查询课程表信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询课程表信息
     *
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, ScheduleVO scheduleVO);

    /**
     * 个人课表
     *
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> self(ScheduleVO scheduleVO);

    /**
     * 自习课表信息
     *
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> selfStudy(ScheduleVO scheduleVO);

    /**
     * 春季班课表信息
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> spring(ScheduleVO scheduleVO);

    /**
     * 10年级课表信息
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> yearTen(ScheduleVO scheduleVO);

    /**
     * 11年级课表信息
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> yearEleven(ScheduleVO scheduleVO);

    /**
     * 12年级课表信息
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> yearTwelve(ScheduleVO scheduleVO);

    /**
     * 新增课程表信息
     *
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> insert(HttpServletRequest request, ScheduleVO scheduleVO);

    /**
     * 修改课程表信息
     *
     * @param scheduleVO
     * @return
     */
    ResponseEntity<Object> update(HttpServletRequest request, ScheduleVO scheduleVO);

    /**
     * 根据ID删除课程表信息
     *
     * @param id
     * @return
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
} 