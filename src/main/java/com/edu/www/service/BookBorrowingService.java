package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import com.edu.www.vo.BookBorrowingVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 书籍领用管理
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface BookBorrowingService {

    /**
     * 根据ID查询书籍领用信息
     *
     * @param id 书籍领用信息ID
     * @return 书籍领用信息
     */
    ResponseEntity<Object> get(HttpServletRequest request, String id);

    /**
     * 分页查询书籍领用信息
     *
     * @param bookBorrowingVO 查询条件
     * @return 书籍领用信息列表
     */
    ResponseEntity<Object> queryPage(HttpServletRequest request, BookBorrowingVO bookBorrowingVO);

    /**
     * 新增书籍领用信息
     *
     * @param bookBorrowingVO 书籍领用信息
     * @return 处理结果
     */
    ResponseEntity<Object> insert(HttpServletRequest request, BookBorrowingVO bookBorrowingVO);

    /**
     * 修改书籍领用信息
     *
     * @param bookBorrowingVO 书籍领用信息
     * @return 处理结果
     */
    ResponseEntity<Object> update(HttpServletRequest request, BookBorrowingVO bookBorrowingVO);

    /**
     * 删除书籍领用信息
     *
     * @param id 书籍领用信息ID
     * @return 处理结果
     */
    ResponseEntity<Object> delete(HttpServletRequest request, String id);
}
