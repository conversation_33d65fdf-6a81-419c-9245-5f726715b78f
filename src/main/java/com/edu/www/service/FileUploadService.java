package com.edu.www.service;

import com.edu.www.common.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件上传服务接口
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public interface FileUploadService {
    /**
     * 获取文件访问URL
     *
     * @param fileId 文件ID
     * @return 文件访问URL
     */
    ResponseEntity<Object> getFileUrl(String fileId);

    /**
     * 下载文件
     *
     * @param fileId   文件ID
     * @param response HTTP响应
     */
    void download(String fileId, HttpServletResponse response);

    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return 上传结果
     */
    ResponseEntity<Object> uploadFile(MultipartFile file);

    /**
     * 批量上传文件
     *
     * @param files
     * @return
     */
    ResponseEntity<Object> batchUploadFile(MultipartFile[] files);


    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 删除结果
     */
    ResponseEntity<Object> deleteFile(String fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID数组
     * @return 删除结果
     */
    ResponseEntity<Object> batchDeleteFiles(List<String> fileIds);

    /**
     * 预览文件
     * 优先从本地存储查找图片，如果找不到则从GitHub获取
     *
     * @param fileId 文件ID
     * @param response HTTP响应
     */
    void preview(String fileId, HttpServletResponse response);
}
