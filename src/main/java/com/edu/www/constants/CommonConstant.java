package com.edu.www.constants;

import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class CommonConstant {

    // 使用不变映射创建星期对应关系
    public static final Map<String, String> weekdayMap = Map.of(
            "1", "monday",
            "2", "tuesday",
            "3", "wednesday",
            "4", "thursday",
            "5", "friday",
            "saturday", "6",
            "sunday", "0"
    );

    public static final Map<String, String> examTypeMap = Map.of(
            "MONTHLY", "Monthly",
            "MID_TERM", "Mid-Term",
            "MOCK", "Mock",
            "IB_TERM", "IB-Term",
            "FINAL_TERM", "Final-Term"
    );

    public static final Map<String, String> periodMap = Map.of(
            "Period 0", "0",
            "Period 1", "1",
            "Period 2", "2",
            "Period 3", "3",
            "Period 4", "4",
            "Period 5", "5",
            "Period 6", "6",
            "Period 7", "7",
            "Period 8", "8",
            "Period 9", "9"
    );


    public static final Map<String, String> weekMap = Map.of(
            "monday", "1",
            "tuesday", "2",
            "wednesday", "3",
            "thursday", "4",
            "friday", "5",
            "saturday", "6",
            "sunday", "0"
    );

    // 定义有序的星期列表，确保顺序为周一到周五
    public static final List<String> orderedDays = Arrays.asList("monday", "tuesday", "wednesday", "thursday", "friday");

    // 定义Period 0-9的常量列表
    public static final List<String> periodNames = Arrays.asList(
            "Period 0", "Period 1", "Period 2", "Period 3", "Period 4",
            "Period 5", "Period 6", "Period 7", "Period 8", "Period 9"
    );

    /**
     * 特殊学生身份证ID
     */
    public static final List<String> specialStudents = List.of(
            "330411200910066249", // 项思涵
            "330106200910265240", // 韩汶希
            "330105201006283414", // 沈楚宸
            "310104200811210811"  // 王珏恩
    );

    // 定义目标 weekday 范围
    public static final List<String> weekdays = Arrays.asList("1", "2", "3", "4", "5");

    // 定义目标 period 范围
    public static final List<String> periods = Arrays.asList("1", "2", "3", "4", "5", "6", "7", "8", "9");
    public static final Map<String, String> periodTime = Map.of(
            "Period 0", "07:30-07:55",
            "Period 1", "08:00-08:40",
            "Period 2", "08:50-09:30",
            "Period 3", "10:00-10:40",
            "Period 4", "10:55-11:35",
            "Period 5", "12:30-13:10",
            "Period 6", "13:20-14:00",
            "Period 7", "14:10-14:50",
            "Period 8", "15:00-15:40",
            "Period 9", "15:50-16:30"
    );

    public static final Map<String, String> WEEKDAY_MAP = Map.of(
            "一", "1", "二", "2", "三", "3", "四", "4",
            "五", "5", "六", "6", "日", "7"
    );

    public static final Map<String, String> DUTY_TYPE_TIME_MAP = Map.of(
            "值日", "(06:30-21:10)",
            "Year10A+B", "(18:30-21:10)",
            "Year11A+B", "(18:30-21:10)",
            "Year12+春季", "(18:30-21:10)"
    );

    // 前端颜色选项到POI索引颜色的映射
    public static final Map<String, Short> FRONTEND_COLOR_MAPPING = Map.ofEntries(
            // 前端定义的颜色选项映射
            Map.entry("ff5733", IndexedColors.ORANGE.getIndex()),        // 橙红色 -> 橙色
            Map.entry("33a8ff", IndexedColors.LIGHT_BLUE.getIndex()),    // 蓝色 -> 浅蓝色
            Map.entry("33ff57", IndexedColors.GREEN.getIndex()),         // 绿色 -> 绿色
            Map.entry("ff33a8", IndexedColors.PINK.getIndex()),          // 粉红色 -> 粉色
            Map.entry("a833ff", IndexedColors.VIOLET.getIndex()),        // 紫色 -> 紫罗兰色
            Map.entry("ffdd33", IndexedColors.YELLOW.getIndex()),        // 黄色 -> 黄色
            Map.entry("33ffdd", IndexedColors.AQUA.getIndex()),          // 青色 -> 水绿色
            Map.entry("dd33ff", IndexedColors.VIOLET.getIndex()),        // 紫红色 -> 紫罗兰色
            Map.entry("ff8c33", IndexedColors.ORANGE.getIndex()),        // 橙色 -> 橙色
            Map.entry("3366ff", IndexedColors.BLUE.getIndex()),          // 蓝色 -> 蓝色
            Map.entry("ff3366", IndexedColors.RED.getIndex()),           // 红色 -> 红色
            Map.entry("33ff99", IndexedColors.SEA_GREEN.getIndex()),     // 海绿色 -> 海绿色
            Map.entry("9933ff", IndexedColors.VIOLET.getIndex()),        // 紫色 -> 紫罗兰色
            Map.entry("ff9933", IndexedColors.ORANGE.getIndex()),        // 橙色 -> 橙色
            Map.entry("33ffff", IndexedColors.TURQUOISE.getIndex()),     // 青绿色 -> 绿松石色
            Map.entry("ff33ff", IndexedColors.PINK.getIndex())           // 品红色 -> 粉色
    );

    // 周五值日类型0的固定轮换序列（5人）
    public static final List<String> FRIDAY_DUTY_ROTATION = Arrays.asList(
            "牟岚", "杨顺华", "林可可", "刘晓君", "王燕针"
    );
}