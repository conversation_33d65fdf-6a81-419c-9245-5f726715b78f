package com.edu.www.constants;


/**
 * 安全相关常量
 */
public class SecurityConstant {

    /**
     * 无需认证的路径列表
     */
    public static final String[] EXCLUDED_PATHS = {
            // todo
            // "/common/**",           // 测试接口不拦截

            "/user/login",              // 登录接口不拦截
            "/user/logout",             // 登出接口不拦截
            "/user/register",           // 注册接口不拦截
            "/common/getDeptCodeList",   // 部门枚举接口不拦截
            "/error",                    // 错误页面不拦截
            "/swagger-ui/**",            // Swagger UI 不拦截
            "/v3/api-docs/**",           // OpenAPI 文档不拦截
            "/v2/api-docs/**",           // Swagger API 文档
            "/swagger-resources/**",     // Swagger 资源
            "/static/**",                // 静态资源不拦截
            "/*.html",                   // HTML 页面不拦截
            "/*.js",                     // JS 文件不拦截
            "/*.css",                    // CSS 文件不拦截
            "/*.ico",                    // 图标文件不拦截
            "/favicon.ico",
            "/actuator/**"               // Spring Boot Actuator 端点
    };

} 