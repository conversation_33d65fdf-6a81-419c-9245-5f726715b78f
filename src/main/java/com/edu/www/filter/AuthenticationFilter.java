package com.edu.www.filter;

import com.edu.www.common.ResponseEntity;
import com.edu.www.constants.Constant;
import com.edu.www.constants.SecurityConstant;
import com.edu.www.convert.UserConverter;
import com.edu.www.dto.UserDTO;
import com.edu.www.utils.JwtTokenUtil;
import com.edu.www.utils.RedisUtil;
import com.edu.www.vo.UserVO;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Date;

/**
 * 认证过滤器
 */
@Component
public class AuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private RedisUtil redisUtil;
    
    private final PathMatcher pathMatcher = new AntPathMatcher();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 获取请求路径
        String path = request.getRequestURI();
        
        // 检查是否是排除的路径
        for (String excludedPath : SecurityConstant.EXCLUDED_PATHS) {
            if (pathMatcher.match(excludedPath, path)) {
                filterChain.doFilter(request, response);
                return;
            }
        }
        
        // 首先检查session是否存在且包含用户信息
        HttpSession session = request.getSession(false);
        boolean sessionValid = false;
        
        if (session != null) {
            // 检查是否有用户信息 - 同时检查两种可能的键名
            Object userInfo = session.getAttribute(Constant.SESSION_USER);
            Object user = session.getAttribute(Constant.USER);
            
            if (userInfo != null || user != null) {
                sessionValid = true;
            }
        }
        
        // 从请求头中获取 token
        final String authHeader = request.getHeader("Authorization");

        String username = null;
        String jwt = null;
        boolean tokenExpired = false;
        boolean tokenValid = false;

        // 如果请求头中有 token
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            jwt = authHeader.substring(7);
            try {
                username = jwtTokenUtil.extractUsername(jwt);
                // 如果能提取出用户名，说明token基本有效
                if (username != null) {
                    tokenValid = true;
                }
                
                // 添加日志，输出token的到期时间
                Date expiration = jwtTokenUtil.extractExpiration(jwt);
            } catch (io.jsonwebtoken.ExpiredJwtException e) {
                logger.info("Token expired: " + e.getMessage());
                tokenExpired = true;
            } catch (Exception e) {
                logger.error("Token validation failed", e);
            }
        }
        
        // 先检查token是否过期，如果过期直接返回过期信息，不考虑session状态
        if (tokenExpired) {
            // 清理 session
            if (session != null) {
                session.invalidate();
            }
            
            // 添加 CORS 头信息
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
            response.setHeader("Access-Control-Expose-Headers", "New-Token");
            
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_OK); // 返回 200 状态码但附带标记

            ResponseEntity<?> result = new ResponseEntity<>();
            result.setMsg("会话已过期，请重新登录");
            result.setErrorCode(null);
            result.setData(null);
            result.setSuccess(false);
            result.setNeedLogin(true);
            
            ObjectMapper mapper = new ObjectMapper();
            response.getWriter().write(mapper.writeValueAsString(result));
            return; // 直接返回，不继续执行过滤器链
        }
        
        // 如果session无效且token也无效，返回需要重新登录的响应
        if (!sessionValid && !tokenValid) {
            // 清理session
            if (session != null) {
                session.invalidate();
            }
            
            // 添加 CORS 头信息
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
            response.setHeader("Access-Control-Expose-Headers", "New-Token");
            
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_OK); // 返回 200 状态码但附带标记

            ResponseEntity<?> result = new ResponseEntity<>();
            result.setMsg("会话已过期，请重新登录");
            result.setErrorCode(null);
            result.setData(null);
            result.setSuccess(false);
            result.setNeedLogin(true);
            
            ObjectMapper mapper = new ObjectMapper();
            response.getWriter().write(mapper.writeValueAsString(result));
            return; // 直接返回，不继续执行过滤器链
        }

        // 如果 token 验证成功，并且 session 中没有保存用户信息
        if (username != null) {
            // 将用户名设置为请求属性，供拦截器使用
            request.setAttribute(Constant.CURRENT_USER_NAME, username);
            
            // 重用前面获取的session对象
            UserVO user = null;
            Object userObj = null;
            
            if (session != null) {
                userObj = session.getAttribute(Constant.USER);
                // 检查对象类型
                if (userObj instanceof UserVO) {
                    user = (UserVO) userObj;
                } else if (userObj instanceof UserDTO) {
                    // 如果是UserDTO，不需要转换为UserVO，继续使用即可
                    // session中已有用户信息，直接跳过后续处理
                    filterChain.doFilter(request, response);
                    return;
                }
            } else {
                session = request.getSession();
            }

            // 如果 session 中没有用户信息，从 Redis 中获取
            if (user == null && userObj == null) {
                String redisKey = "user:" + username;
                Object redisObj = redisUtil.get(redisKey);
                
                // 检查Redis中返回的对象类型
                if (redisObj instanceof UserVO) {
                    user = (UserVO) redisObj;
                } else if (redisObj instanceof UserDTO) {
                    UserDTO userDTO = (UserDTO) redisObj;
                    // 存入session
                    if (jwtTokenUtil.validateToken(jwt, username)) {
                        session.setAttribute(Constant.USER, userDTO);
                        session.setAttribute(Constant.SESSION_USER, userDTO);
                        session.setAttribute("token", jwt);
                        
                        // 刷新Redis中的用户信息过期时间
                        redisUtil.expire(redisKey, 3600);
                        
                        // 检查token是否接近过期
                        if (jwtTokenUtil.isTokenNearExpiration(jwt)) {
                            String newToken = jwtTokenUtil.refreshToken(jwt);
                            session.setAttribute("token", newToken);
                            response.setHeader("New-Token", newToken);
                            response.setHeader("Access-Control-Expose-Headers", "New-Token");
                        }
                        
                        filterChain.doFilter(request, response);
                        return;
                    }
                }

                // 如果 Redis 中存在UserVO用户信息，将其存入 session
                if (user != null && jwtTokenUtil.validateToken(jwt, username)) {
                    try {
                        // 将 UserVO 转换为 UserDTO
                        UserDTO userDTO = UserConverter.userVO2DTO(user);
                        
                        // 同时存储到两个键名下，确保兼容性
                        session.setAttribute(Constant.USER, userDTO);
                        session.setAttribute(Constant.SESSION_USER, userDTO);
                        session.setAttribute("token", jwt);

                        // 刷新 Redis 中的用户信息过期时间
                        redisUtil.expire(redisKey, 3600);
                        
                        // 检查 token 是否接近过期（如 5 分钟内）
                        if (jwtTokenUtil.isTokenNearExpiration(jwt)) {
                            // 生成新的 token
                            String newToken = jwtTokenUtil.refreshToken(jwt);
                            // 更新 session 中的 token
                            session.setAttribute("token", newToken);
                            // 将新 token 放入响应头，前端可以自动更新
                            response.setHeader("New-Token", newToken);
                            // 添加允许前端访问的头信息
                            response.setHeader("Access-Control-Expose-Headers", "New-Token");
                        }
                    } catch (Exception e) {
                        logger.error("将用户信息存入session时发生错误", e);
                        // 如果转换失败，使用原始UserVO对象
                        session.setAttribute(Constant.USER, user);
                        session.setAttribute(Constant.SESSION_USER, user);
                        session.setAttribute("token", jwt);
                    }
                }
            }
        }

        filterChain.doFilter(request, response);
    }
}
