package com.edu.www.mapper;

import com.edu.www.dto.AdmissionDTO;
import com.edu.www.vo.AdmissionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招生信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface AdmissionMapper {

    /**
     * 根据ID查询招生信息
     *
     * @param id
     * @return
     */
    AdmissionVO get(@Param("id") String id);

    /**
     * 查询招生信息
     *
     * @param admissionDTO
     * @return
     */
    List<AdmissionVO> query(AdmissionDTO admissionDTO);

    /**
     * 新增招生信息
     *
     * @param admissionDTO
     */
    void insert(AdmissionDTO admissionDTO);

    /**
     * 修改招生信息
     *
     * @param admissionDTO
     */
    void update(AdmissionDTO admissionDTO);

    /**
     * 根据ID删除招生信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
} 