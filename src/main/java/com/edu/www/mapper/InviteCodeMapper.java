package com.edu.www.mapper;

import com.edu.www.dto.InviteCodeDTO;
import com.edu.www.vo.InviteCodeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邀请码管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface InviteCodeMapper {

    /**
     * 根据ID查询邀请码
     *
     * @param id
     * @return
     */
    InviteCodeVO get(@Param("id") String id);

    /**
     * 根据邀请码查询未使用的邀请码数量
     *
     * @param code 邀请码
     * @return 未使用的邀请码数量，0 表示不存在或已被使用
     */
    Integer getByCodeCount(String code);

    /**
     * 根据邀请码查询未被使用的邀请码
     *
     * @param code
     * @return
     */
    InviteCodeVO getByCode(@Param("code") String code);

    /**
     * 查询邀请码
     *
     * @param inviteCodeDTO
     * @return
     */
    List<InviteCodeVO> query(InviteCodeDTO inviteCodeDTO);

    /**
     * 新增邀请码
     *
     * @param inviteCodeDTO
     */
    void insert(InviteCodeDTO inviteCodeDTO);

    /**
     * 修改邀请码
     *
     * @param inviteCodeDTO
     */
    void update(InviteCodeDTO inviteCodeDTO);

    /**
     * 根据ID删除邀请码
     *
     * @param id
     */
    void delete(@Param("id") String id);

    /**
     * 批量删除邀请码
     *
     * @param ids
     */
    void deleteByIds(@Param("ids") List<String> ids);
}