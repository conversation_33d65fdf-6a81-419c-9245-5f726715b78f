package com.edu.www.mapper;

import com.edu.www.dto.RolePermissionDTO;
import com.edu.www.dto.UserRoleDTO;
import com.edu.www.vo.UserRoleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Mapper
public interface UserRoleMapper {

    /**
     * 根据ID查询用户角色关联信息
     *
     * @param id 主键ID
     * @return 用户角色关联信息
     */
    UserRoleDTO get(@Param("id") String id);

    /**
     * 查询用户角色关联列表
     *
     * @param userRoleDTO 查询条件
     * @return 用户角色关联列表
     */
    List<UserRoleVO> query(UserRoleDTO userRoleDTO);

    /**
     * 根据用户ID查询用户角色关联列表
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<UserRoleVO> getByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询用户角色关联列表
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<UserRoleVO> queryByRoleId(@Param("roleId") String roleId);

    /**
     * 新增用户角色关联
     *
     * @param userRoleDTO 用户角色关联信息
     * @return 影响行数
     */
    int insert(UserRoleDTO userRoleDTO);

    /**
     * 批量新增用户角色关联
     *
     * @param userRoleDTOList
     * @return
     */
    int batchInsert(@Param("list") List<UserRoleDTO> userRoleDTOList);

    /**
     * 更新用户角色关联
     *
     * @param userRoleDTO 用户角色关联信息
     * @return 影响行数
     */
    int update(UserRoleDTO userRoleDTO);

    /**
     * 删除用户角色关联
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int delete(@Param("id") String id);

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(@Param("roleId") String roleId);

    /**
     * 根据用户ID和角色ID删除用户角色关联
     *
     * @param roleId
     * @return
     */
    int deleteByUserIdAndRoleId(@Param("roleId") String roleId);
}