package com.edu.www.mapper;

import com.edu.www.dto.UserDTO;
import com.edu.www.vo.UserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface UserMapper {

    /**
     * 根据ID查询用户信息
     *
     * @param id
     * @return
     */
    UserVO getById(@Param("id") String id);

    /**
     * 根据多个ID批量查询用户信息
     *
     * @param ids
     * @return
     */
    List<UserVO> getByIds(@Param("ids") List<String> ids);

    /**
     * 根据用户名和密码查询用户信息
     *
     * @param username
     * @param password
     * @return
     */
    UserVO getByUsernameAndPassword(@Param("username") String username, @Param("password") String password);

    /**
     * 查询用户信息
     *
     * @return
     */
    List<UserVO> query(UserDTO userDTO);

    /**
     * 查询邮箱号是否存在
     *
     * @param email
     * @return
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 新增用户信息
     *
     * @param userDTO
     */
    int insert(UserDTO userDTO);

    /**
     * 更新用户信息
     *
     * @param userDTO
     */
    void update(UserDTO userDTO);

    /**
     * 根据ID修改密码
     *
     * @param userDTO
     */
    void updatePwdById(UserDTO userDTO);

    /**
     * 根据ID删除用户信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}