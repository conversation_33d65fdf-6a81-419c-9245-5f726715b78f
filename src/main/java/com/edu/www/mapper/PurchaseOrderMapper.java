package com.edu.www.mapper;

import com.edu.www.dto.PurchaseOrderDTO;
import com.edu.www.vo.PurchaseOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购订单管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface PurchaseOrderMapper {

    /**
     * 根据ID查询采购订单信息
     *
     * @param id
     * @return
     */
    PurchaseOrderVO get(@Param("id") String id);

    /**
     * 根据采购订单号查询数量
     *
     * @param purchaseOrderNo 采购订单号
     * @return 数量
     */
    Integer getByCodeCount(@Param("purchaseOrderNo") String purchaseOrderNo);

    /**
     * 查询采购订单信息
     *
     * @param purchaseOrderDTO
     * @return
     */
    List<PurchaseOrderVO> query(PurchaseOrderDTO purchaseOrderDTO);

    /**
     * 新增采购订单信息
     *
     * @param purchaseOrderDTO
     */
    void insert(PurchaseOrderDTO purchaseOrderDTO);

    /**
     * 修改采购订单信息
     *
     * @param purchaseOrderDTO
     */
    void update(PurchaseOrderDTO purchaseOrderDTO);

    /**
     * 根据ID删除采购订单信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
