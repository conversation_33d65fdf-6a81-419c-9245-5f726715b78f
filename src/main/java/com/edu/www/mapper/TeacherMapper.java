package com.edu.www.mapper;

import com.edu.www.dto.TeacherDTO;
import com.edu.www.vo.TeacherVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教师信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface TeacherMapper {

    /**
     * 根据ID查询教师信息
     *
     * @param id
     * @return
     */
    TeacherVO getById(@Param("id") String id);

    /**
     * 根据IDS批量查询教师信息
     *
     * @param ids
     * @return
     */
    List<TeacherVO> getByIds(@Param("ids") List<String> ids);

    /**
     * 查询教师信息
     *
     * @param teacherDTO
     * @return
     */
    List<TeacherVO> query(TeacherDTO teacherDTO);

    /**
     * 新增教师信息
     *
     * @param teacherDTO
     */
    void insert(TeacherDTO teacherDTO);

    /**
     * 更新教师信息
     *
     * @param teacherDTO
     */
    void update(TeacherDTO teacherDTO);

    /**
     * 根据ID删除教师信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
} 