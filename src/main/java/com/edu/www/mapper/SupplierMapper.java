package com.edu.www.mapper;

import com.edu.www.dto.SupplierDTO;
import com.edu.www.vo.SupplierVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface SupplierMapper {

    /**
     * 根据ID查询供应商信息
     *
     * @param id
     * @return
     */
    SupplierVO get(@Param("id") String id);

    /**
     * 查询供应商信息
     *
     * @param supplierDTO
     * @return
     */
    List<SupplierVO> query(SupplierDTO supplierDTO);

    /**
     * 新增供应商信息
     *
     * @param supplierDTO
     */
    void insert(SupplierDTO supplierDTO);

    /**
     * 修改供应商信息
     *
     * @param supplierDTO
     */
    void update(SupplierDTO supplierDTO);

    /**
     * 根据ID删除供应商信息
     *
     * @param id
     */
    void delete(@Param("id") String id);

    /**
     * 根据供应商编码查询数量
     *
     * @param supplierCode 供应商编码
     * @return 数量
     */
    Integer getByCodeCount(@Param("supplierCode") String supplierCode);

    /**
     * 查询所有供应商列表
     *
     * @return 供应商列表
     */
    List<SupplierVO> getAll();
}
