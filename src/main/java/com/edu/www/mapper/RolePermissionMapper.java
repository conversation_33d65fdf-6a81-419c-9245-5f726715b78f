package com.edu.www.mapper;

import com.edu.www.dto.RolePermissionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色权限关联管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Mapper
public interface RolePermissionMapper {
    
    /**
     * 根据ID查询角色权限关联信息
     *
     * @param id 主键ID
     * @return 角色权限关联信息
     */
    RolePermissionDTO getById(@Param("id") String id);
    
    /**
     * 查询角色权限关联列表
     *
     * @param rolePermissionDTO 查询条件
     * @return 角色权限关联列表
     */
    List<RolePermissionDTO> query(RolePermissionDTO rolePermissionDTO);
    
    /**
     * 根据角色ID查询角色权限关联列表
     *
     * @param roleId 角色ID
     * @return 角色权限关联列表
     */
    List<RolePermissionDTO> queryByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据权限ID查询角色权限关联列表
     *
     * @param permissionId 权限ID
     * @return 角色权限关联列表
     */
    List<RolePermissionDTO> queryByPermissionId(@Param("permissionId") String permissionId);
    
    /**
     * 新增角色权限关联
     *
     * @param rolePermissionDTO 角色权限关联信息
     * @return 影响行数
     */
    int insert(RolePermissionDTO rolePermissionDTO);

    /**
     * 批量新增角色权限关联
     * @param rolePermissionDTOList
     * @return
     */
    int batchInsert(@Param("list") List<RolePermissionDTO> rolePermissionDTOList);

    /**
     * 更新角色权限关联
     *
     * @param rolePermissionDTO 角色权限关联信息
     * @return 影响行数
     */
    int update(RolePermissionDTO rolePermissionDTO);
    
    /**
     * 删除角色权限关联
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int delete(@Param("id") String id);
    
    /**
     * 根据角色ID删除角色权限关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据权限ID删除角色权限关联
     *
     * @param permissionId 权限ID
     * @return 影响行数
     */
    int deleteByPermissionId(@Param("permissionId") String permissionId);
} 