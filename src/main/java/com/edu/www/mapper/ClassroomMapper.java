package com.edu.www.mapper;

import com.edu.www.dto.ClassroomDTO;
import com.edu.www.vo.ClassroomVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教室信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface ClassroomMapper {

    /**
     * 根据ID查询教室信息
     *
     * @param id
     * @return
     */
    ClassroomVO get(@Param("id") String id);

    /**
     * 查询教室信息
     *
     * @param classroomDTO
     * @return
     */
    List<ClassroomVO> query(ClassroomDTO classroomDTO);

    /**
     * 新增教室信息
     *
     * @param classroomDTO
     */
    void insert(ClassroomDTO classroomDTO);

    /**
     * 修改教室信息
     *
     * @param classroomDTO
     */
    void update(ClassroomDTO classroomDTO);

    /**
     * 根据ID删除教室信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
} 