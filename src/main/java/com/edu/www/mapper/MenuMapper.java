package com.edu.www.mapper;

import com.edu.www.dto.MenuDTO;
import com.edu.www.vo.MenuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Mapper
public interface MenuMapper {

    /**
     * 根据ID查询菜单信息
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    MenuVO getById(@Param("id") String id);

    /**
     * 查询父级菜单数量
     *
     * @param menuDTO 查询条件
     * @return 菜单列表
     */
    int queryParentAll(MenuDTO menuDTO);

    /**
     * 查询菜单列表
     *
     * @param menuDTO 查询条件
     * @return 菜单列表
     */
    List<MenuVO> query(MenuDTO menuDTO);

    /**
     * 查询所有菜单（包含权限信息）
     *
     * @return 菜单列表
     */
    List<MenuVO> queryAllWithPermissions();

    /**
     * 根据用户ID查询菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<MenuVO> queryByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询菜单列表
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<MenuVO> queryByRoleId(@Param("roleId") String roleId);

    /**
     * 新增菜单
     *
     * @param menuDTO 菜单信息
     * @return 影响行数
     */
    int insert(MenuDTO menuDTO);

    /**
     * 更新菜单
     *
     * @param menuDTO 菜单信息
     * @return 影响行数
     */
    int update(MenuDTO menuDTO);

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 影响行数
     */
    int delete(@Param("id") String id);
} 