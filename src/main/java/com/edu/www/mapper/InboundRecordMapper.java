package com.edu.www.mapper;

import com.edu.www.dto.InboundRecordDTO;
import com.edu.www.vo.InboundRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 入库记录管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface InboundRecordMapper {

    /**
     * 根据ID查询入库记录信息
     *
     * @param id
     * @return
     */
    InboundRecordVO get(@Param("id") String id);

    /**
     * 查询入库记录信息
     *
     * @param inboundRecordDTO
     * @return
     */
    List<InboundRecordVO> query(InboundRecordDTO inboundRecordDTO);

    /**
     * 新增入库记录信息
     *
     * @param inboundRecordDTO
     */
    void insert(InboundRecordDTO inboundRecordDTO);

    /**
     * 修改入库记录信息
     *
     * @param inboundRecordDTO
     */
    void update(InboundRecordDTO inboundRecordDTO);

    /**
     * 根据ID删除入库记录信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
