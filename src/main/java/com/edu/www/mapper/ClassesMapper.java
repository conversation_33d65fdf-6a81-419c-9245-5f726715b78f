package com.edu.www.mapper;

import com.edu.www.dto.ClassesDTO;
import com.edu.www.vo.ClassesVO;
import com.edu.www.vo.StudentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班级信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface ClassesMapper {

    /**
     * 根据ID查询班级信息
     *
     * @param id
     * @return
     */
    ClassesVO getById(@Param("id") String id);

    /**
     * 根据多个ID批量查询班级信息
     *
     * @param ids ID列表
     * @return 班级信息列表
     */
    List<ClassesVO> getByIds(@Param("ids") List<String> ids);

    /**
     * 查询班级信息
     *
     * @param classesDTO
     * @return
     */
    List<ClassesVO> query(ClassesDTO classesDTO);

    /**
     * 新增班级信息
     *
     * @param classesDTO
     */
    void insert(ClassesDTO classesDTO);

    /**
     * 更新班级信息
     *
     * @param classesDTO
     */
    void update(ClassesDTO classesDTO);

    /**
     * 根据ID删除班级信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
} 