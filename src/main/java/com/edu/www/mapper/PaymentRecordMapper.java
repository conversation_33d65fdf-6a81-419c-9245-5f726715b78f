package com.edu.www.mapper;

import com.edu.www.dto.PaymentRecordDTO;
import com.edu.www.vo.PaymentRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付记录管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface PaymentRecordMapper {

    /**
     * 根据ID查询支付记录信息
     *
     * @param id
     * @return
     */
    PaymentRecordVO get(@Param("id") String id);

    /**
     * 查询支付记录信息
     *
     * @param paymentRecordDTO
     * @return
     */
    List<PaymentRecordVO> query(PaymentRecordDTO paymentRecordDTO);

    /**
     * 新增支付记录信息
     *
     * @param paymentRecordDTO
     */
    void insert(PaymentRecordDTO paymentRecordDTO);

    /**
     * 修改支付记录信息
     *
     * @param paymentRecordDTO
     */
    void update(PaymentRecordDTO paymentRecordDTO);

    /**
     * 根据ID删除支付记录信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
