package com.edu.www.mapper;

import com.edu.www.dto.ExamDTO;
import com.edu.www.vo.ExamVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考试信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface ExamMapper {

    /**
     * 根据ID查询考试信息
     *
     * @param id
     * @return
     */
    ExamVO get(@Param("id") String id);

    /**
     * 根据多个ID批量查询考试信息
     *
     * @param ids
     * @return
     */
    List<ExamVO> getByIds(@Param("ids") List<String> ids);

    /**
     * 查询考试信息
     *
     * @param examDTO
     * @return
     */
    List<ExamVO> query(ExamDTO examDTO);

    /**
     * 新增考试信息
     *
     * @param examDTO
     */
    void insert(ExamDTO examDTO);

    /**
     * 修改考试信息
     *
     * @param examDTO
     */
    void update(ExamDTO examDTO);

    /**
     * 根据ID删除考试信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
