package com.edu.www.mapper;

import com.edu.www.dto.DutyDTO;
import com.edu.www.vo.DutyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 值日信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface DutyMapper {

    /**
     * 根据ID查询值日信息
     *
     * @param id
     * @return
     */
    DutyVO get(@Param("id") String id);

    /**
     * 查询值日信息
     *
     * @param dutyDTO
     * @return
     */
    List<DutyVO> query(DutyDTO dutyDTO);

    /**
     * 新增值日信息
     *
     * @param dutyDTO
     */
    void insert(DutyDTO dutyDTO);

    /**
     * 修改值日信息
     *
     * @param dutyDTO
     */
    int update(DutyDTO dutyDTO);

    /**
     * 根据ID删除值日信息
     *
     * @param id
     */
    void delete(@Param("id") String id);

    /**
     * 批量删除值日信息
     *
     * @param ids ID列表
     * @return 删除的记录数
     */
    int batchDelete(@Param("ids") List<String> ids);

    /**
     * 查询值日信息并按DUTY_DATE升序排序
     *
     * @param dutyDTO
     * @return
     */
    List<DutyVO> queryOrderByDutyDateAsc(DutyDTO dutyDTO);

    /**
     * 批量新增值日信息
     *
     * @param dutyDTOList 值日信息列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<DutyDTO> dutyDTOList);

    /**
     * 根据日期范围查询值日信息
     *
     * @param dutyDTO 查询条件
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 值日信息列表
     */
    List<DutyVO> queryByDateRange(@Param("dutyDTO") DutyDTO dutyDTO,
                                  @Param("startDate") Date startDate,
                                  @Param("endDate") Date endDate);
}
