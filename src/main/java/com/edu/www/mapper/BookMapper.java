package com.edu.www.mapper;

import com.edu.www.dto.BookDTO;
import com.edu.www.vo.BookVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 书籍信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface BookMapper {

    /**
     * 根据ID查询书籍信息
     *
     * @param id
     * @return
     */
    BookVO get(@Param("id") String id);

    /**
     * 查询书籍信息
     *
     * @param bookDTO
     * @return
     */
    List<BookVO> query(BookDTO bookDTO);

    /**
     * 新增书籍信息
     *
     * @param bookDTO
     */
    void insert(BookDTO bookDTO);

    /**
     * 修改书籍信息
     *
     * @param bookDTO
     */
    void update(BookDTO bookDTO);

    /**
     * 根据ID删除书籍信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
