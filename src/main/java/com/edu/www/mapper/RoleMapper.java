package com.edu.www.mapper;

import com.edu.www.dto.RoleDTO;
import com.edu.www.vo.RoleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Mapper
public interface RoleMapper {
    
    /**
     * 根据ID查询角色信息
     *
     * @param id 角色ID
     * @return 角色信息
     */
    RoleVO getById(@Param("id") String id);

    /**
     * 查询角色编码是否存在
     * @param code
     * @return
     */
    boolean existsByCode(@Param("code") String code);

    /**
     * 查询角色列表
     *
     * @param roleDTO 查询条件
     * @return 角色列表
     */
    List<RoleVO> query(RoleDTO roleDTO);
    
    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleVO> queryByUserId(@Param("userId") String userId);
    
    /**
     * 根据权限ID查询拥有该权限的角色列表
     *
     * @param permissionId 权限ID
     * @return 角色列表
     */
    List<RoleVO> queryByPermissionId(@Param("permissionId") String permissionId);
    
    /**
     * 新增角色
     *
     * @param roleDTO 角色信息
     * @return 影响行数
     */
    int insert(RoleDTO roleDTO);
    
    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 影响行数
     */
    int update(RoleDTO roleDTO);
    
    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 影响行数
     */
    int delete(@Param("id") String id);
} 