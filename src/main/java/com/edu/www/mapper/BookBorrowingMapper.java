package com.edu.www.mapper;

import com.edu.www.dto.BookBorrowingDTO;
import com.edu.www.vo.BookBorrowingVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 书籍领用管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface BookBorrowingMapper {

    /**
     * 根据ID查询书籍领用信息
     *
     * @param id
     * @return
     */
    BookBorrowingVO get(@Param("id") String id);

    /**
     * 查询书籍领用信息
     *
     * @param bookBorrowingDTO
     * @return
     */
    List<BookBorrowingVO> query(BookBorrowingDTO bookBorrowingDTO);

    /**
     * 新增书籍领用信息
     *
     * @param bookBorrowingDTO
     */
    void insert(BookBorrowingDTO bookBorrowingDTO);

    /**
     * 修改书籍领用信息
     *
     * @param bookBorrowingDTO
     */
    void update(BookBorrowingDTO bookBorrowingDTO);

    /**
     * 根据ID删除书籍领用信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
