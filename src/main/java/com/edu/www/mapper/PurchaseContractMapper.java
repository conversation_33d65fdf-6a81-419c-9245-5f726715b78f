package com.edu.www.mapper;

import com.edu.www.dto.PurchaseContractDTO;
import com.edu.www.vo.PurchaseContractVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购合同管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface PurchaseContractMapper {

    /**
     * 根据ID查询采购合同信息
     *
     * @param id
     * @return
     */
    PurchaseContractVO get(@Param("id") String id);

    /**
     * 查询采购合同信息
     *
     * @param purchaseContractDTO
     * @return
     */
    List<PurchaseContractVO> query(PurchaseContractDTO purchaseContractDTO);

    /**
     * 新增采购合同信息
     *
     * @param purchaseContractDTO
     */
    void insert(PurchaseContractDTO purchaseContractDTO);

    /**
     * 修改采购合同信息
     *
     * @param purchaseContractDTO
     */
    void update(PurchaseContractDTO purchaseContractDTO);

    /**
     * 根据ID删除采购合同信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
