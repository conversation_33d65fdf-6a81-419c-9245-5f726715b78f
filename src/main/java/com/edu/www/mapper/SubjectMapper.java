package com.edu.www.mapper;

import com.edu.www.dto.SubjectDTO;
import com.edu.www.vo.SubjectVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学科信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface SubjectMapper {

    /**
     * 根据ID查询学科信息
     *
     * @param id
     * @return
     */
    SubjectVO getById(@Param("id") String id);

    /**
     * 查询学科信息
     *
     * @param subjectDTO
     * @return
     */
    List<SubjectVO> query(SubjectDTO subjectDTO);

    /**
     * 新增学科信息
     *
     * @param subjectDTO
     */
    void insert(SubjectDTO subjectDTO);

    /**
     * 更新学科信息
     *
     * @param subjectDTO
     */
    void update(SubjectDTO subjectDTO);

    /**
     * 根据ID删除学科信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
} 