package com.edu.www.mapper;

import com.edu.www.dto.ReturnRecordDTO;
import com.edu.www.vo.ReturnRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 归还记录管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface ReturnRecordMapper {

    /**
     * 根据ID查询归还记录信息
     *
     * @param id
     * @return
     */
    ReturnRecordVO get(@Param("id") String id);

    /**
     * 查询归还记录信息
     *
     * @param returnRecordDTO
     * @return
     */
    List<ReturnRecordVO> query(ReturnRecordDTO returnRecordDTO);

    /**
     * 新增归还记录信息
     *
     * @param returnRecordDTO
     */
    void insert(ReturnRecordDTO returnRecordDTO);

    /**
     * 修改归还记录信息
     *
     * @param returnRecordDTO
     */
    void update(ReturnRecordDTO returnRecordDTO);

    /**
     * 根据ID删除归还记录信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
