package com.edu.www.mapper;

import com.edu.www.dto.TmpScheduleDTO;
import com.edu.www.vo.TmpScheduleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 临时课程表Mapper接口
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Mapper
public interface TmpScheduleMapper {

    /**
     * 根据ID查询临时课程表信息
     *
     * @param id 临时课程表ID
     * @return 临时课程表信息
     */
    TmpScheduleVO getById(@Param("id") String id);

    /**
     * 查询临时课程表信息列表
     *
     * @param tmpScheduleDTO 查询条件
     * @return 临时课程表信息列表
     */
    List<TmpScheduleVO> query(TmpScheduleDTO tmpScheduleDTO);

    /**
     * 根据条件查询临时课程表信息数量
     *
     * @param tmpScheduleDTO 查询条件
     * @return 符合条件的记录数
     */
    int count(TmpScheduleDTO tmpScheduleDTO);

    /**
     * 新增临时课程表信息
     *
     * @param tmpScheduleDTO 临时课程表信息
     * @return 影响行数
     */
    int insert(TmpScheduleDTO tmpScheduleDTO);

    /**
     * 批量新增临时课程表信息
     *
     * @param tmpScheduleDTOList 临时课程表信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TmpScheduleDTO> tmpScheduleDTOList);

    /**
     * 修改临时课程表信息
     *
     * @param tmpScheduleDTO 临时课程表信息
     * @return 影响行数
     */
    int update(TmpScheduleDTO tmpScheduleDTO);

    /**
     * 根据ID删除临时课程表信息
     *
     * @param id 临时课程表ID
     * @return 影响行数
     */
    int delete(@Param("id") String id);
} 