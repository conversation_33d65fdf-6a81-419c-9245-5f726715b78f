package com.edu.www.mapper;

import com.edu.www.dto.FileDTO;
import com.edu.www.vo.FileVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Mapper
public interface FileMapper {

    /**
     * 根据ID查询文件信息
     *
     * @param id
     * @return
     */
    FileVO get(@Param("id") String id);

    /**
     * 根据文件ID查询文件信息
     *
     * @param fileId
     * @return
     */
    FileVO getByFileId(@Param("fileId") String fileId);

    /**
     * 根据多个文件ID查询文件信息
     *
     * @param fileIds
     * @return
     */
    List<FileVO> getByFileIds(@Param("fileIds") List<String> fileIds);

    /**
     * 查询文件信息
     *
     * @param fileDTO
     * @return
     */
    List<FileVO> query(FileDTO fileDTO);

    /**
     * 新增文件信息
     *
     * @param fileDTO
     */
    void insert(FileDTO fileDTO);

    /**
     * 修改文件信息
     *
     * @param fileDTO
     */
    void update(FileDTO fileDTO);

    /**
     * 根据ID删除文件信息
     *
     * @param id
     */
    void delete(@Param("id") String id);

    /**
     * 根据文件ID删除文件信息
     *
     * @param fileId
     */
    void deleteByFileId(@Param("fileId") String fileId);

    /**
     * 批量删除文件信息
     *
     * @param ids
     */
    void batchDelete(@Param("ids") List<String> ids);

    /**
     * 根据文件类型查询文件信息
     *
     * @param fileType
     * @return
     */
    List<FileVO> queryByFileType(@Param("fileType") String fileType);

    /**
     * 根据状态查询文件信息
     *
     * @param status
     * @return
     */
    List<FileVO> queryByStatus(@Param("status") String status);

    /**
     * 统计文件总数
     *
     * @return
     */
    int countFiles();

    /**
     * 统计文件总大小
     *
     * @return
     */
    Long sumFileSize();
}
