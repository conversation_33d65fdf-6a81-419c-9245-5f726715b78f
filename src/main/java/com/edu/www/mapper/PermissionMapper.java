package com.edu.www.mapper;

import com.edu.www.dto.PermissionDTO;
import com.edu.www.vo.PermissionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
@Mapper
public interface PermissionMapper {
    
    /**
     * 根据ID查询权限信息
     *
     * @param id 权限ID
     * @return 权限信息
     */
    PermissionVO getById(@Param("id") String id);

    /**
     * 查询权限编码是否存在
     * @param code
     * @return
     */
    boolean existsByCode(@Param("code") String code);

    /**
     * 查询权限列表
     *
     * @param permissionDTO 查询条件
     * @return 权限列表
     */
    List<PermissionVO> query(PermissionDTO permissionDTO);
    
    /**
     * 根据菜单ID查询权限列表
     *
     * @param menuId 菜单ID
     * @return 权限列表
     */
    List<PermissionVO> queryByMenuId(@Param("menuId") String menuId);
    
    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<PermissionVO> queryByUserId(@Param("userId") String userId);
    
    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<PermissionVO> queryByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据用户ID查询按钮权限列表
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<PermissionVO> queryButtonPermissionsByUserId(@Param("userId") String userId);
    
    /**
     * 新增权限
     *
     * @param permissionDTO 权限信息
     * @return 影响行数
     */
    int insert(PermissionDTO permissionDTO);
    
    /**
     * 更新权限
     *
     * @param permissionDTO 权限信息
     * @return 影响行数
     */
    int update(PermissionDTO permissionDTO);
    
    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 影响行数
     */
    int delete(@Param("id") String id);
} 