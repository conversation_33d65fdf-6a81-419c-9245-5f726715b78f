package com.edu.www.mapper;

import com.edu.www.dto.PurchaseOrderDetailDTO;
import com.edu.www.vo.PurchaseOrderDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购明细管理 Mapper
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Mapper
public interface PurchaseOrderDetailMapper {

    /**
     * 根据ID查询采购明细信息
     *
     * @param id
     * @return
     */
    PurchaseOrderDetailVO get(@Param("id") String id);

    /**
     * 查询采购明细信息
     *
     * @param purchaseOrderDetailDTO
     * @return
     */
    List<PurchaseOrderDetailVO> query(PurchaseOrderDetailDTO purchaseOrderDetailDTO);

    /**
     * 新增采购明细信息
     *
     * @param purchaseOrderDetailDTO
     */
    void insert(PurchaseOrderDetailDTO purchaseOrderDetailDTO);

    /**
     * 修改采购明细信息
     *
     * @param purchaseOrderDetailDTO
     */
    void update(PurchaseOrderDetailDTO purchaseOrderDetailDTO);

    /**
     * 根据ID删除采购明细信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}
