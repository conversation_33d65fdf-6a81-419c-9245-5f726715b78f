package com.edu.www.mapper;

import com.edu.www.dto.StudentDTO;
import com.edu.www.vo.StudentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface StudentMapper {

    /**
     * 根据ID查询学生信息
     *
     * @param id
     * @return
     */
    StudentVO getById(@Param("id") String id);

    /**
     * 根据多个ID批量查询学生信息
     *
     * @param ids ID列表
     * @return 学生信息列表
     */
    List<StudentVO> getByIds(@Param("ids") List<String> ids);

    /**
     * 根据条件查询学生信息数量
     *
     * @param studentDTO
     * @return
     */
    StudentVO queryOne(StudentDTO studentDTO);

    /**
     * 查询学生信息
     *
     * @param studentDTO
     * @return
     */
    List<StudentVO> query(StudentDTO studentDTO);

    /**
     * 新增学生信息
     *
     * @param studentDTO
     */
    void insert(StudentDTO studentDTO);

    /**
     * 更新学生信息
     *
     * @param studentDTO
     */
    void update(StudentDTO studentDTO);


    /**
     * 根据ID删除学生信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
}