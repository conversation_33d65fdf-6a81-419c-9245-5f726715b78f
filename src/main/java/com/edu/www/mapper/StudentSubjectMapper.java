package com.edu.www.mapper;

import com.edu.www.dto.StudentSubjectDTO;
import com.edu.www.vo.SeatVO;
import com.edu.www.vo.StudentSubjectVO;
import com.edu.www.vo.StudentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生学科管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface StudentSubjectMapper {

    /**
     * 根据ID查询学生学科信息
     *
     * @param id
     * @return
     */
    StudentSubjectVO get(@Param("id") String id);

    /**
     * 查询学生学科信息
     *
     * @param studentSubjectDTO
     * @return
     */
    List<StudentSubjectVO> query(StudentSubjectDTO studentSubjectDTO);

    /**
     * 新增学生学科信息
     *
     * @param studentSubjectDTO
     */
    void insert(StudentSubjectDTO studentSubjectDTO);

    /**
     * 修改学生学科信息
     *
     * @param studentSubjectDTO
     */
    void update(StudentSubjectDTO studentSubjectDTO);

    /**
     * 根据ID删除学生学科信息
     *
     * @param id
     */
    void delete(@Param("id") String id);

    /**
     * 根据科目选择条件查询学生信息
     * 通过t_edu_student_subject关联t_edu_student表返回学生信息
     *
     * @param seatVO 座位安排VO，包含科目选择条件
     * @return 符合条件的学生信息列表
     */
    List<StudentVO> getStudentBySubjectSelection(@Param("seatVO") SeatVO seatVO);
}