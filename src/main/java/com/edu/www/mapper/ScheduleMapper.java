package com.edu.www.mapper;

import com.edu.www.dto.ScheduleDTO;
import com.edu.www.vo.ScheduleToolVO;
import com.edu.www.vo.ScheduleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程表信息管理 Mapper
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@Mapper
public interface ScheduleMapper {

    /**
     * 根据ID查询课程表信息
     *
     * @param id
     * @return
     */
    ScheduleVO getById(@Param("id") String id);

    /**
     * 查询课程表信息
     *
     * @param scheduleDTO
     * @return
     */
    List<ScheduleVO> query(ScheduleDTO scheduleDTO);

    /**
     * 查询课表工具
     *
     * @param scheduleToolVO
     * @return
     */
    List<ScheduleVO> queryList(ScheduleToolVO scheduleToolVO);

    /**
     * 新增课程表信息
     *
     * @param scheduleDTO
     */
    void insert(ScheduleDTO scheduleDTO);

    /**
     * 更新课程表信息
     *
     * @param scheduleDTO
     */
    void update(ScheduleDTO scheduleDTO);

    /**
     * 根据ID删除课程表信息
     *
     * @param id
     */
    void delete(@Param("id") String id);
} 