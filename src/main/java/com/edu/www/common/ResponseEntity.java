package com.edu.www.common;

import com.edu.www.enums.ErrorCode;

import java.io.Serializable;

/**
 * 公共返回封装对象对象
 *
 * @param <T>
 */
public class ResponseEntity<T> implements Serializable {
    private static final long serialVersionUID = 258937826067810831L;

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 错误码
     */
    private ErrorCode errorCode;

    /**
     * 是否需要登录
     */
    private Boolean isNeedLogin = Boolean.FALSE;

    private T data;

    public ResponseEntity() {
    }

    public static <T> ResponseEntity<T> unauthorized() {
        ResponseEntity<T> response = new ResponseEntity<>();
        response.setMsg("用户未登录，请先登录");
        response.setSuccess(false);
        response.setNeedLogin(true);
        return response;
    }

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public boolean isNeedLogin() {
        return isNeedLogin;
    }

    public void setNeedLogin(boolean needLogin) {
        isNeedLogin = needLogin;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
