package com.edu.www.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.edu.www.dto.UserDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

@Component
@ServerEndpoint("/edu/active/class") // 定义 WebSocket 连接的 URL
public class WebSocketEntity {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEntity.class);
    private static CopyOnWriteArraySet<WebSocketEntity> webSocketSet = new CopyOnWriteArraySet<>();
    private Session session;
    
    // 用于存储WebSocket会话与用户信息的映射关系
    private static Map<String, UserDTO> sessionUserMap = new ConcurrentHashMap<>();

    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        webSocketSet.add(this);

        try {
            // 发送欢迎消息
            this.session.getBasicRemote().sendText("{\"type\":\"welcome\",\"message\":\"连接成功\"}");
        } catch (IOException e) {
            logger.error("发送欢迎消息失败: {}", e.getMessage(), e);
        }
    }

    @OnClose
    public void onClose() {
        // 移除会话与用户信息的映射
        if (this.session != null) {
            sessionUserMap.remove(this.session.getId());
        }
        webSocketSet.remove(this);
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            // 处理客户端发送的初始化消息
            if (message.contains("\"type\":\"init\"")) {
                // 尝试解析用户信息
                JSONObject jsonObject = JSON.parseObject(message);
                if (jsonObject.containsKey("userInfo")) {
                    String userInfoStr = jsonObject.getString("userInfo");
                    UserDTO userDTO = JSON.parseObject(userInfoStr, UserDTO.class);
                    // 存储用户信息与会话的关联
                    sessionUserMap.put(session.getId(), userDTO);
                }
                
                // 发送确认消息
                this.session.getBasicRemote().sendText("{\"type\":\"init_ack\",\"message\":\"初始化成功\"}");
            }
        } catch (Exception e) {
            logger.error("处理WebSocket消息失败: {}", e.getMessage(), e);
            try {
                this.session.getBasicRemote().sendText("{\"type\":\"error\",\"message\":\"处理消息失败\"}");
            } catch (IOException ex) {
                logger.error("发送错误消息失败: {}", ex.getMessage(), ex);
            }
        }
    }

    @OnError
    public void onError(Session session, Throwable error) {
        String sessionId = session != null ? session.getId() : "unknown";
        logger.error("WebSocket发生错误, SessionID: {}, 错误信息: {}",
                sessionId, error.getMessage(), error);
        
        // 清除错误会话的用户信息
        if (session != null) {
            sessionUserMap.remove(session.getId());
        }
    }

    // 后端主动推送消息的方法
    public void sendMessage(String message) throws IOException {
        if (this.session != null && this.session.isOpen()) {
            this.session.getBasicRemote().sendText(message);
        } else {
            logger.warn("无法发送消息，Session为null或已关闭");
        }
    }

    // 静态方法，用于向所有连接的客户端推送消息
    public static void send(String message) {
        if (webSocketSet.isEmpty()) {
            return;
        }

        for (WebSocketEntity item : webSocketSet) {
            try {
                item.sendMessage(message);
            } catch (IOException e) {
                logger.error("消息发送失败, 错误: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 获取与会话关联的用户信息
     * @param sessionId 会话ID
     * @return 用户信息，若不存在则返回null
     */
    public static UserDTO getUserBySessionId(String sessionId) {
        return sessionUserMap.get(sessionId);
    }
    
    /**
     * 获取所有已连接WebSocket客户端的用户部门信息
     * @return 部门代码集合
     */
    public static Map<String, UserDTO> getAllConnectedUsers() {
        return sessionUserMap;
    }

    /**
     * 静态方法，用于向所有连接的客户端推送实时课程状态
     * @param deptName
     * @param activeClassesJson
     * @param upcomingClassesJson
     */
    public static void sendClassStatus(String deptName, String activeClassesJson, String upcomingClassesJson) {
        StringBuilder message = new StringBuilder();
        message.append("{");
        message.append("\"deptName\":\"");
        message.append(deptName);
        message.append("\",");
        message.append("\"activeClasses\":");
        message.append(activeClassesJson);
        message.append(",");
        message.append("\"upcomingClasses\":");
        message.append(upcomingClassesJson);
        message.append("}");
        send(message.toString());
    }
    
    /**
     * 静态方法，根据部门向特定用户推送实时课程状态
     * @param targetDeptCode 目标部门代码
     * @param deptName 部门名称
     * @param activeClassesJson 当前活跃课程JSON
     * @param upcomingClassesJson 即将开始课程JSON
     */
    public static void sendClassStatusByDept(String targetDeptCode, String deptName, 
                                            String activeClassesJson, String upcomingClassesJson) {
        if (webSocketSet.isEmpty()) {
            return;
        }
        
        StringBuilder message = new StringBuilder();
        message.append("{");
        message.append("\"deptName\":\"");
        message.append(deptName);
        message.append("\",");
        message.append("\"activeClasses\":");
        message.append(activeClassesJson);
        message.append(",");
        message.append("\"upcomingClasses\":");
        message.append(upcomingClassesJson);
        message.append("}");
        
        String finalMessage = message.toString();
        
        // 遍历所有WebSocket连接
        for (WebSocketEntity item : webSocketSet) {
            try {
                // 获取会话ID
                String sessionId = item.session.getId();
                // 获取用户信息
                UserDTO userDTO = sessionUserMap.get(sessionId);
                
                // 如果用户信息存在且部门匹配，则发送消息
                if (userDTO != null && targetDeptCode.equals(userDTO.getDepartmentCode())) {
                    item.sendMessage(finalMessage);
                }
            } catch (IOException e) {
                logger.error("向特定部门用户发送消息失败, 错误: {}", e.getMessage(), e);
            }
        }
    }
}
