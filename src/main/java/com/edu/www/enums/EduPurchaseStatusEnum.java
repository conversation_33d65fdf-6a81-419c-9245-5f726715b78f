package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 采购状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduPurchaseStatusEnum {
    /**
     * 采购状态枚举
     */
    CANCELLED("0", "已取消"),
    PENDING_QUOTE("1", "待报价"),
    QUOTED("2", "已报价"),
    PENDING_APPROVAL("3", "待审批"),
    APPROVED("4", "已审批"),
    ORDERED("5", "已下单"),
    SHIPPED("6", "已发货"),
    RECEIVED("7", "已收货"),
    COMPLETED("8", "已完成"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPurchaseStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPurchaseStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPurchaseStatusEnum purchaseStatusEnum : EduPurchaseStatusEnum.values()) {
                if (key.equals(purchaseStatusEnum.getKey())) {
                    return purchaseStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPurchaseStatusEnum purchaseStatusEnum : EduPurchaseStatusEnum.values()) {
            map.put(purchaseStatusEnum.getKey(), purchaseStatusEnum.getDesc());
        }
        return map;
    }
}
