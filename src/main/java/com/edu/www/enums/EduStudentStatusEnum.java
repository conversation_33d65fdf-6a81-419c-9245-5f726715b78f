package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 学生状态枚举值
 */
public enum EduStudentStatusEnum {
    /**
     * 学生状态枚举
     */
    WITHDRAWN("0", "退学"),
    AUDITING("1", "旁听"),
    ENROLLED("2", "在读"),
    GRADUATED("3", "毕业"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduStudentStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduStudentStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduStudentStatusEnum eduStudentStatusEnum : EduStudentStatusEnum.values()) {
                if (key.equals(eduStudentStatusEnum.getKey())) {
                    return eduStudentStatusEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduStudentStatusEnum eduStudentStatusEnum : EduStudentStatusEnum.values()) {
            map.put(eduStudentStatusEnum.getKey(), eduStudentStatusEnum.getDesc());
        }
        return map;
    }
} 