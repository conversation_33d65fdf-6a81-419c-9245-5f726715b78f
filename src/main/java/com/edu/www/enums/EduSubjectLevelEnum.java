package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 学科层级枚举值
 */
public enum EduSubjectLevelEnum {
    /**
     * 普通课程
     */
    SL("SL", "SL(普通)"),

    /**
     * 高级课程
     */
    HL("HL", "HL(高阶)"),

    /**
     * (英语)B1
     */
    B_1("B1", "B1"),

    /**
     * (英语)B2
     */
    B_2("B2", "B2"),

    /**
     * (英语)A1
     */
    A_1("A1", "A1"),

    /**
     * (英语)A2
     */
    A_2("A2", "A2"),

    /**
     * A
     */
    A("A", "A"),

    /**
     * B
     */
    B("B", "B"),

    /**
     * C
     */
    C("C", "C"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduSubjectLevelEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduSubjectLevelEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSubjectLevelEnum eduSubjectLevelEnum : EduSubjectLevelEnum.values()) {
                if (key.equals(eduSubjectLevelEnum.getKey())) {
                    return eduSubjectLevelEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSubjectLevelEnum eduSubjectLevelEnum : EduSubjectLevelEnum.values()) {
            map.put(eduSubjectLevelEnum.getKey(), eduSubjectLevelEnum.getDesc());
        }
        return map;
    }
} 