package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 教学楼枚举值
 */
public enum EduBuildingEnum {
    /**
     * 行政楼(A栋)
     */
    A("A", "行政楼(A栋)"),

    /**
     * 双语教学楼(B栋)
     */
    B("B", "双语教学楼(B栋)"),

    /**
     * 高中&融合教学楼(C栋)
     */
    C("C", "高中&融合教学楼(C栋)"),

    /**
     * 综合楼(D栋)
     */
    D("D", "综合楼(D栋)");

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBuildingEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBuildingEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBuildingEnum eduBuildingEnum : EduBuildingEnum.values()) {
                if (key.equals(eduBuildingEnum.getKey())) {
                    return eduBuildingEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBuildingEnum eduBuildingEnum : EduBuildingEnum.values()) {
            map.put(eduBuildingEnum.getKey(), eduBuildingEnum.getDesc());
        }
        return map;
    }
} 