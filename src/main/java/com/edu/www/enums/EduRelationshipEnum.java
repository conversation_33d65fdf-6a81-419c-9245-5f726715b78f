package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 家庭关系枚举值
 */
public enum EduRelationshipEnum {
    /**
     * 家庭关系枚举
     */
    FATHER("父亲", "FATH<PERSON>"),
    MOTHER("母亲", "<PERSON><PERSON><PERSON><PERSON>"),
    GRANDFATHER("爷爷", "GRANDFATHER"),
    GRANDMOTHER("奶奶", "GRANDMOTHER"),
    MATERNAL_GRANDFATHER("外公", "MATERNAL_GRANDFATHER"),
    MATERNAL_GRANDMOTHER("外婆", "MATERNAL_GRANDMOTHER"),
    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduRelationshipEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduRelationshipEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduRelationshipEnum eduRelationshipEnum : EduRelationshipEnum.values()) {
                if (key.equals(eduRelationshipEnum.getKey())) {
                    return eduRelationshipEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduRelationshipEnum eduRelationshipEnum : EduRelationshipEnum.values()) {
            map.put(eduRelationshipEnum.getKey(), eduRelationshipEnum.getDesc());
        }
        return map;
    }
} 