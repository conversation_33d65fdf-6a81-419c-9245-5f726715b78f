package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 货币类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduCurrencyEnum {
    /**
     * 货币类型枚举
     */
    CNY("CNY", "人民币"),
    JPY("JPY", "日元"),
    USD("USD", "美元"),
    EUR("EUR", "欧元"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduCurrencyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduCurrencyEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduCurrencyEnum currencyEnum : EduCurrencyEnum.values()) {
                if (key.equals(currencyEnum.getKey())) {
                    return currencyEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduCurrencyEnum currencyEnum : EduCurrencyEnum.values()) {
            map.put(currencyEnum.getKey(), currencyEnum.getDesc());
        }
        return map;
    }
}
