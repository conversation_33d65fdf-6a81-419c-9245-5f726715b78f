package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 信用等级枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduCreditRatingEnum {
    /**
     * 信用等级枚举
     */
    EXCELLENT("A", "极好"),
    GOOD("B", "好"),
    AVERAGE("C", "一般"),
    POOR("D", "差"),
    VERY_POOR("E", "极差"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduCreditRatingEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduCreditRatingEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduCreditRatingEnum eduCreditRatingEnum : EduCreditRatingEnum.values()) {
                if (key.equals(eduCreditRatingEnum.getKey())) {
                    return eduCreditRatingEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduCreditRatingEnum eduCreditRatingEnum : EduCreditRatingEnum.values()) {
            map.put(eduCreditRatingEnum.getKey(), eduCreditRatingEnum.getDesc());
        }
        return map;
    }
}
