package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 年级编号枚举值
 */
public enum EduAdmissionGradeEnum {
    /**
     * 年级编号枚举
     */
    GRADE_1("1", "1年级"),
    GRADE_2("2", "2年级"),
    GRADE_3("3", "3年级"),
    GRADE_4("4", "4年级"),
    GRADE_5("5", "5年级"),
    GRADE_6("6", "6年级"),
    GRADE_7("7", "7年级"),
    GRADE_8("8", "8年级"),
    GRADE_9("9", "9年级"),
    GRADE_10("10", "10年级"),
    GRADE_11("11", "11年级"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduAdmissionGradeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduAdmissionGradeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduAdmissionGradeEnum eduAdmissionGradeEnum : EduAdmissionGradeEnum.values()) {
                if (key.equals(eduAdmissionGradeEnum.getKey())) {
                    return eduAdmissionGradeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduAdmissionGradeEnum eduAdmissionGradeEnum : EduAdmissionGradeEnum.values()) {
            map.put(eduAdmissionGradeEnum.getKey(), eduAdmissionGradeEnum.getDesc());
        }
        return map;
    }
} 