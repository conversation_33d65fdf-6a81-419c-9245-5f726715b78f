package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * Demo枚举值
 */
public enum EduEnum {
    /**
     * 测试枚举
     */
    DEMO_FLAG("demo_flag", "测试枚举"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }


    public String getKey() {
        return key;
    }

    public static EduEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduEnum indEnum : EduEnum.values()) {
                if (key.equals(indEnum.getKey())) {
                    return indEnum;
                }
            }
        }
        return null;
    }
}
