package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 比较运算符枚举
 */
public enum EduCmpSymEnum {
    /**
     * 比较运算符枚举
     */

    /**
     * 等于
     */
    EQUAL("equal", "="),

    /**
     * 不等于
     */
    NOT_EQUAL("notEqual", "!="),

    /**
     * 大于
     */
    GREATER_THAN("greaterThan", ">"),

    /**
     * 小于
     */
    LESS_THAN("lessThan", "<"),

    /**
     * 大于等于
     */
    GREATER_THAN_OR_EQUAL("greaterThanOrEqual", ">="),

    /**
     * 小于等于
     */
    LESS_THAN_OR_EQUAL("lessThanOrEqual", "<="),

    /**
     * 包含
     */
    LIKE("like", "LIKE"),

    /**
     * 不包含
     */
    NOT_LIKE("notLike", "NOT LIKE"),

    /**
     * 在列表中
     */
    IN("in", "IN"),

    /**
     * 不在列表中
     */
    NOT_IN("notIn", "NOT IN"),

    /**
     * 在范围内
     */
    BETWEEN("between", "BETWEEN"),

    /**
     * 不在范围内
     */
    NOT_BETWEEN("notBetween", "NOT BETWEEN"),

    /**
     * 为空
     */
    IS_NULL("isNull", "IS NULL"),

    /**
     * 不为空
     */
    IS_NOT_NULL("isNotNull", "IS NOT NULL");

    /**
     * 符号
     */
    private String symbol;
    /**
     * 描述
     */
    private String desc;

    EduCmpSymEnum(String symbol, String desc) {
        this.symbol = symbol;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getSymbol() {
        return symbol;
    }

    /**
     * 根据符号获取枚举
     * @param symbol 符号
     * @return 枚举值
     */
    public static EduCmpSymEnum getBySymbol(String symbol) {
        if (StringUtils.isNotBlank(symbol)) {
            for (EduCmpSymEnum eduCmpSymEnum : EduCmpSymEnum.values()) {
                if (symbol.equals(eduCmpSymEnum.getSymbol())) {
                    return eduCmpSymEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有比较运算符枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的符号，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduCmpSymEnum eduCmpSymEnum : EduCmpSymEnum.values()) {
            map.put(eduCmpSymEnum.getSymbol(), eduCmpSymEnum.getDesc());
        }
        return map;
    }
} 