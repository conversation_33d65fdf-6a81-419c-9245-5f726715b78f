package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 是否枚举值
 */
public enum EduYesOrNoEnum {
    /**
     * 是否枚举
     */
    NO("0", "否"),
    YES("1", "是"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduYesOrNoEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduYesOrNoEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduYesOrNoEnum eduYesOrNoEnum : EduYesOrNoEnum.values()) {
                if (key.equals(eduYesOrNoEnum.getKey())) {
                    return eduYesOrNoEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduYesOrNoEnum eduYesOrNoEnum : EduYesOrNoEnum.values()) {
            map.put(eduYesOrNoEnum.getKey(), eduYesOrNoEnum.getDesc());
        }
        return map;
    }
} 