package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 文件类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public enum EduFileTypeEnum {

    // 图片类型
    IMAGE_JPG("jpg", "image", "图片文件"),
    IMAGE_JPEG("jpeg", "image", "图片文件"),
    IMAGE_PNG("png", "image", "图片文件"),
    IMAGE_GIF("gif", "image", "图片文件"),
    IMAGE_BMP("bmp", "image", "图片文件"),
    IMAGE_WEBP("webp", "image", "图片文件"),

    // Excel文件
    FILE_XLS("xls", "file/excel", "Excel文件"),
    FILE_XLSX("xlsx", "file/excel", "Excel文件"),

    // Word文件
    FILE_DOC("doc", "file/word", "Word文件"),
    FILE_DOCX("docx", "file/word", "Word文件"),

    // PPT文件
    FILE_PPT("ppt", "file/ppt", "PPT文件"),
    FILE_PPTX("pptx", "file/ppt", "PPT文件"),

    // 文本文件
    FILE_TXT("txt", "file/txt", "文本文件"),
    FILE_PDF("pdf", "file/pdf", "PDF文件"),

    // 视频文件
    VIDEO_MP4("mp4", "video", "视频文件"),
    VIDEO_AVI("avi", "video", "视频文件"),
    VIDEO_MOV("mov", "video", "视频文件"),
    VIDEO_WMV("wmv", "video", "视频文件"),
    VIDEO_FLV("flv", "video", "视频文件"),
    VIDEO_MKV("mkv", "video", "视频文件"),

    // 音频文件
    MUSIC_MP3("mp3", "music", "音频文件"),
    MUSIC_WAV("wav", "music", "音频文件"),
    MUSIC_AAC("aac", "music", "音频文件"),
    MUSIC_FLAC("flac", "music", "音频文件"),
    MUSIC_OGG("ogg", "music", "音频文件"),

    // 其他文件
    FILE_OTHER("other", "file/other", "其他文件");

    private final String extension;
    private final String folder;
    private final String description;

    EduFileTypeEnum(String extension, String folder, String description) {
        this.extension = extension;
        this.folder = folder;
        this.description = description;
    }

    public String getExtension() {
        return extension;
    }

    public String getFolder() {
        return folder;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据文件扩展名获取文件类型
     *
     * @param fileName 文件名
     * @return 文件类型枚举
     */
    public static EduFileTypeEnum getByFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return FILE_OTHER;
        }

        String extension = getFileExtension(fileName).toLowerCase();
        for (EduFileTypeEnum fileType : EduFileTypeEnum.values()) {
            if (fileType.getExtension().equals(extension)) {
                return fileType;
            }
        }
        return FILE_OTHER;
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private static String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 判断是否为图片文件
     *
     * @param fileName 文件名
     * @return 是否为图片
     */
    public static boolean isImage(String fileName) {
        EduFileTypeEnum fileType = getByFileName(fileName);
        return fileType.getFolder().equals("image");
    }

    /**
     * 判断是否为视频文件
     *
     * @param fileName 文件名
     * @return 是否为视频
     */
    public static boolean isVideo(String fileName) {
        EduFileTypeEnum fileType = getByFileName(fileName);
        return fileType.getFolder().equals("video");
    }

    /**
     * 判断是否为音频文件
     *
     * @param fileName 文件名
     * @return 是否为音频
     */
    public static boolean isMusic(String fileName) {
        EduFileTypeEnum fileType = getByFileName(fileName);
        return fileType.getFolder().equals("music");
    }
}
