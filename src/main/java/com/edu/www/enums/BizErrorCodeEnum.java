package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 错误码定义抽象类
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum BizErrorCodeEnum implements ErrorCode {
    /**
     * 系统异常
     */
    SYSTEM_ERROR("999999", "系统异常"),

    /**
     * 用户登录失败
     */
    USER_LOGIN_FAILED("USER_LOGIN_FAILED","用户登录失败"),
    /**
     * 操作成功
     */
    SUCCESS("000000", "操作成功"),

    /**
     * 操作不合法, 不允许操作
     */
    OPERATION_NOT_ALLOWED("OPERATION_NOT_ALLOWED", "操作不合法, 不允许操作"),

    /**
     * 不支持的参数设置
     */
    CONFIG_NOT_SUPPORT("CONFIG_NOT_SUPPORT", "不支持的参数设置"),

    /**
     * 不支持的取值
     */
    VALUE_NOT_SUPPORT("VALUE_NOT_SUPPORT", "不支持的取值"),

    /**
     * 参数为空（含null）是非法的
     */
    BLANK_IS_ILLEGAL_PARAM("BLANK_IS_ILLEGAL_PARAM", "参数为空（含null）是非法的"),

    /**
     * 这种情况一般是捕获了没有单独进行catch处理的异常然后设定的错误码
     */
    UNEXPECTED_ERROR("UNEXPECTED_ERROR", "非预期的系统错误"),


    /**
     * 数据未找到
     */
    DATA_NOT_FOUND("DATA_NOT_FOUND", "数据未找到"),

    TYPE_CONVERT_EXCEPTION("TYPE_CONVERT_EXCEPTION", "类型转换异常"),

    ;


    /**
     * 处理结果码
     */
    private final String code;

    /**
     * 处理结果描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code
     * @param desc
     */
    BizErrorCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code 如果为null，返回null
     * @return 不匹配返回<code>null</code>
     */
    public static BizErrorCodeEnum getByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (BizErrorCodeEnum result : values()) {
                if (result.getCode().equals(code)) {
                    return result;
                }
            }
        }
        return UNEXPECTED_ERROR;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
