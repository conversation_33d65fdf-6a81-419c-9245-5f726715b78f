package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 课段枚举值
 */
public enum EduPeriodEnum {
    /**
     * 课段枚举
     */
    PERIOD_0("0", "早自修"),
    PERIOD_1("1", "第1节课"),
    PERIOD_2("2", "第2节课"),
    PERIOD_3("3", "第3节课"),
    PERIOD_4("4", "第4节课"),
    PERIOD_5("5", "第5节课"),
    PERIOD_6("6", "第6节课"),
    PERIOD_7("7", "第7节课"),
    PERIOD_8("8", "第8节课"),
    PERIOD_9("9", "第9节课"),
    PERIOD_10("10", "第10节课"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPeriodEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPeriodEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPeriodEnum eduPeriodEnum : EduPeriodEnum.values()) {
                if (key.equals(eduPeriodEnum.getKey())) {
                    return eduPeriodEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPeriodEnum eduPeriodEnum : EduPeriodEnum.values()) {
            map.put(eduPeriodEnum.getKey(), eduPeriodEnum.getDesc());
        }
        return map;
    }
} 