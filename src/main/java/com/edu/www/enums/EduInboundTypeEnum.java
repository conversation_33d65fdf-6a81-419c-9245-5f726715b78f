package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 入库类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduInboundTypeEnum {
    /**
     * 入库类型枚举
     */
    PURCHASE_INBOUND("1", "采购入库"),
    TRANSFER_INBOUND("2", "调拨入库"),
    RETURN_INBOUND("3", "退回入库"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduInboundTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduInboundTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduInboundTypeEnum inboundTypeEnum : EduInboundTypeEnum.values()) {
                if (key.equals(inboundTypeEnum.getKey())) {
                    return inboundTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduInboundTypeEnum inboundTypeEnum : EduInboundTypeEnum.values()) {
            map.put(inboundTypeEnum.getKey(), inboundTypeEnum.getDesc());
        }
        return map;
    }
}
