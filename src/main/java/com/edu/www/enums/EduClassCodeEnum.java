package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 班级编号枚举值
 */
public enum EduClassCodeEnum {
    /**
     * 班级编号枚举
     */
    CLASS_A("A", "A班"),
    CLASS_B("B", "B班"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduClassCodeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduClassCodeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduClassCodeEnum eduClassCodeEnum : EduClassCodeEnum.values()) {
                if (key.equals(eduClassCodeEnum.getKey())) {
                    return eduClassCodeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduClassCodeEnum eduClassCodeEnum : EduClassCodeEnum.values()) {
            map.put(eduClassCodeEnum.getKey(), eduClassCodeEnum.getDesc());
        }
        return map;
    }
} 