package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 归还状况枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduReturnConditionEnum {
    /**
     * 归还状况枚举
     */
    LOST("0", "遗失"),
    GOOD("1", "完好"),
    MINOR_DAMAGE("2", "轻微损坏"),
    SEVERE_DAMAGE("3", "严重损坏"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduReturnConditionEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduReturnConditionEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduReturnConditionEnum returnConditionEnum : EduReturnConditionEnum.values()) {
                if (key.equals(returnConditionEnum.getKey())) {
                    return returnConditionEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduReturnConditionEnum returnConditionEnum : EduReturnConditionEnum.values()) {
            map.put(returnConditionEnum.getKey(), returnConditionEnum.getDesc());
        }
        return map;
    }
}
