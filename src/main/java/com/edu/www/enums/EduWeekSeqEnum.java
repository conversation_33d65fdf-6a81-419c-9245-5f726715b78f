package com.edu.www.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 周序枚举
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public enum EduWeekSeqEnum {
    THIS_WEEK("this_week", "本周"),
    NEXT_WEEK("next_week", "下周"),
    THE_WEEK_AFTER_NEXT("the_week_after_next", "下下周"),
    THE_WEEK_AFTER_THAT("the_week_after_that", "大大下周");

    private final String key;
    private final String desc;

    EduWeekSeqEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据key获取枚举
     *
     * @param key 枚举key
     * @return 枚举值
     */
    public static EduWeekSeqEnum getByKey(String key) {
        if (key == null) {
            return null;
        }
        for (EduWeekSeqEnum enumItem : EduWeekSeqEnum.values()) {
            if (enumItem.getKey().equals(key)) {
                return enumItem;
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduWeekSeqEnum enumItem : EduWeekSeqEnum.values()) {
            map.put(enumItem.getKey(), enumItem.getDesc());
        }
        return map;
    }
} 