package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduPaymentStatusEnum {
    /**
     * 支付状态枚举
     */
    PAYMENT_FAILED("0", "支付失败"),
    PENDING_APPROVAL("1", "待审批"),
    APPROVED("2", "已审批"),
    PAID("3", "已支付"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPaymentStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPaymentStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPaymentStatusEnum paymentStatusEnum : EduPaymentStatusEnum.values()) {
                if (key.equals(paymentStatusEnum.getKey())) {
                    return paymentStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPaymentStatusEnum paymentStatusEnum : EduPaymentStatusEnum.values()) {
            map.put(paymentStatusEnum.getKey(), paymentStatusEnum.getDesc());
        }
        return map;
    }
}
