package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 支付类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduPaymentTypeEnum {
    /**
     * 支付类型枚举
     */
    PREPAYMENT("1", "预付款"),
    FINAL_PAYMENT("2", "尾款"),
    OTHER("3", "其他"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPaymentTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPaymentTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPaymentTypeEnum paymentTypeEnum : EduPaymentTypeEnum.values()) {
                if (key.equals(paymentTypeEnum.getKey())) {
                    return paymentTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPaymentTypeEnum paymentTypeEnum : EduPaymentTypeEnum.values()) {
            map.put(paymentTypeEnum.getKey(), paymentTypeEnum.getDesc());
        }
        return map;
    }
}
