package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 星期枚举值
 */
public enum EduWeekdayEnum {
    /**
     * 星期枚举
     */
    SUNDAY("0", "周日", "日"),
    MONDAY("1", "周一", "一"),
    TUESDAY("2", "周二", "二"),
    WEDNESDAY("3", "周三", "三"),
    THURSDAY("4", "周四", "四"),
    FRIDAY("5", "周五", "五"),
    SATURDAY("6", "周六", "六"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;
    private String abbr;

    EduWeekdayEnum(String key, String desc, String abbr) {
        this.key = key;
        this.desc = desc;
        this.abbr = abbr;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public String getAbbr() {
        return abbr;
    }

    public static EduWeekdayEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduWeekdayEnum eduWeekdayEnum : EduWeekdayEnum.values()) {
                if (key.equals(eduWeekdayEnum.getKey())) {
                    return eduWeekdayEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAllDesc() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduWeekdayEnum eduWeekdayEnum : EduWeekdayEnum.values()) {
            map.put(eduWeekdayEnum.getKey(), eduWeekdayEnum.getDesc());
        }
        return map;
    }

    public static Map<String, Map<String, String>> getAll() {
        Map<String, Map<String, String>> map = new LinkedHashMap<>();
        for (EduWeekdayEnum eduWeekdayEnum : EduWeekdayEnum.values()) {
            Map<String, String> detail = new LinkedHashMap<>();
            detail.put("key", eduWeekdayEnum.getKey());
            detail.put("desc", eduWeekdayEnum.getDesc());
            detail.put("abbr", eduWeekdayEnum.getAbbr());
            map.put(eduWeekdayEnum.getKey(), detail);
        }
        return map;
    }
} 