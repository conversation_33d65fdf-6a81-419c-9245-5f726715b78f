package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 学科编码枚举
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduSubjectCodeEnum {
    //****************************************高中***********************************************//

    /**
     * 中文
     */
    CHINESE("Chinese", "中文", "G1", "Chinese"),

    /**
     * 文学与表演艺术
     */
    L_AND_P("L&P", "文学与表演艺术(LP)", "G1", "L&P"),

    /**
     * 语言与文学
     */
    L_AND_L("L&L", "语言与文学(LL)", "G1", "L&L"),

    /**
     * 英语B
     */
    ENGLISH_B("English B", "英语B", "G2", "English B"),

    /**
     * 初级西班牙语
     */
    SPANISH_AB_INITIO("Spanish ab initio.", "初级西班牙语", "G2", "Spanish ab initio"),

    /**
     * 初级日语
     */
    JAPANESE_AB_INITIO("Japanese ab initio.", "初级日语", "G2", "Japanese ab initio."),

    /**
     * 经商
     */
    EBM("EBM", "经商", "G3", "EBM"),

    /**
     * 经济
     */
    ECONOMICS("Economics", "经济", "G3", "Economics"),

    /**
     * 商业管理
     */
    BUSINESS_MANAGEMENT("BM", "商业管理", "G3", "BM"),

    /**
     * 科学
     */
    SCIENCE("Science", "科学", "G4", "Science"),

    /**
     * 物理
     */
    PHYSICS("Physics", "物理", "G4", "Physics"),

    /**
     * 化学
     */
    CHEMISTRY("Chemistry", "化学", "G4", "Chemistry"),

    /**
     * 生物
     */
    BIOLOGY("Biology", "生物", "G4", "Biology"),

    /**
     * 数学
     */
    MATHEMATICS("Math", "数学", "G5", "Math"),

    /**
     * 分析方法
     */
    A_AND_A("A&A", "分析方法(AA)", "G5", "A&A"),

    /**
     * 应用与解释
     */
    A_AND_I("A&I", "应用与解释(AI)", "G5", "A&I"),

    /**
     * 视觉艺术
     */
    VISUAL_ARTS("Visual arts", "视觉艺术(VA)", "G6", "Visual arts"),

    /**
     * 专题论文
     */
    EE("EE", "专题论文(EE)", "Core", "EE"),

    /**
     * 认识论
     */
    TOK("TOK", "认识论(TOK)", "Core", "TOK"),

    /**
     * 创造行动与服务
     */
    CAS("CAS", "创造行动与服务(CAS)", "Core", "CAS"),

    /**
     * 中文
     */
    CHINESE_IC("Chinese", "中文", "LanguageLiterature", "Chinese"),

    //****************************************融合***********************************************//

    /**
     * 英语
     */
    ENGLISH_IC("English", "英语", "LanguageAcquisition", "English"),

    /**
     * 数学
     */
    MATH_IC("Math", "数学", "Mathematics", "Math"),

    /**
     * 综合科学
     */
    GENERAL_SCIENCE_IC("GeneralScience", "综合科学", "Science", "GeneralScience"),

    /**
     * 物理
     */
    PHYSICS_IC("Physics", "物理", "Science", "Physics"),

    /**
     * 化学
     */
    CHEMISTRY_IC("Chemistry", "化学", "Science", "Chemistry"),

    /**
     * 生物
     */
    BIOLOGY_IC("Biology", "生物", "Science", "Biology"),

    /**
     * 历史
     */
    HISTORY_IC("History", "历史", "IndividualSocieties", "History"),

    /**
     * 地理
     */
    GEOGRAPHY_IC("Geography", "地理", "IndividualSocieties", "Geography"),

    /**
     * 道法与心理
     */
    PSYCHOLOGY_IC("Psychology", "道法与心理", "IndividualSocieties", "Psychology"),

    /**
     * 体育
     */
    PE_IC("PE", "体育", "PhysicalHealthEducation", "PE"),

    /**
     * 音乐
     */
    MUSIC_IC("Music", "音乐", "Arts", "Music"),

    /**
     * 视觉艺术
     */
    VISUAL_ARTS_IC("VisualArts", "视觉艺术", "Arts", "VisualArts"),

    /**
     * 戏剧
     */
    DRAMA_IC("Drama", "戏剧", "Arts", "Drama"),

    /**
     * 电子设计
     */
    DIGITAL_DESIGN_IC("DigitalDesign", "电子设计", "Design", "DigitalDesign"),

    /**
     * 产品设计
     */
    PRODUCT_DESIGN_IC("ProductDesign", "产品设计", "Design", "ProductDesign"),

    //****************************************校本***********************************************//

    /**
     * 体育
     */
    PE("PE", "体育", "SBC", "PE"),

    /**
     * 计算机科学
     */
    COMPUTER_SCIENCE("Computer Science", "计算机科学", "SBC", "Computer Science"),

    /**
     * 心理
     */
    PSYCHOLOGY("Psychology", "心理", "SBC", "Psychology");

    /**
     * 缓存键值
     */
    private String key;

    /**
     * 描述
     */
    private String desc;

    /**
     * 学科组编码
     */
    private String groupCode;

    /**
     * 学科英文名称
     */
    private String enName;

    EduSubjectCodeEnum(String key, String desc, String groupCode, String enName) {
        this.key = key;
        this.desc = desc;
        this.groupCode = groupCode;
        this.enName = enName;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public String getEnName() {
        return enName;
    }

    /**
     * 根据key获取枚举值
     *
     * @param key
     * @return
     */
    public static EduSubjectCodeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSubjectCodeEnum eduSubjectCodeEnum : EduSubjectCodeEnum.values()) {
                if (key.equals(eduSubjectCodeEnum.getKey())) {
                    return eduSubjectCodeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据key获取描述
     *
     * @param key
     * @return
     */
    public static String getDescByKey(String key) {
        EduSubjectCodeEnum eduSubjectCodeEnum = getByKey(key);
        return eduSubjectCodeEnum == null ? null : eduSubjectCodeEnum.getDesc();
    }

    /**
     * 获取所有学科编码
     *
     * @return 返回包含所有学科编码的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSubjectCodeEnum eduSubjectCodeEnum : EduSubjectCodeEnum.values()) {
            map.put(eduSubjectCodeEnum.getKey(), eduSubjectCodeEnum.getDesc());
        }
        return map;
    }

    /**
     * 根据学科组编码获取对应的学科列表
     *
     * @param groupCode 学科组编码
     * @return 返回包含筛选后学科枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getByGroupCode(String groupCode) {
        Map<String, String> map = new HashMap<>();

        if (StringUtils.isBlank(groupCode)) {
            return getAll();
        }

        for (EduSubjectCodeEnum eduSubjectCodeEnum : EduSubjectCodeEnum.values()) {
            if (groupCode.equals(eduSubjectCodeEnum.getGroupCode())) {
                map.put(eduSubjectCodeEnum.getKey(), eduSubjectCodeEnum.getDesc());
            }
        }

        return map;
    }

    /**
     * 获取所有学科组（不重复）
     *
     * @return 返回所有不重复的学科组编码和描述Map
     */
    public static Map<String, String> getAllGroups() {
        Map<String, String> map = new HashMap<>();
        Map<String, String> groupDescMap = new HashMap<>();

        // 配置学科组描述
        groupDescMap.put("G1", "第一学科组");
        groupDescMap.put("G2", "第二学科组");
        groupDescMap.put("G3", "第三学科组");
        groupDescMap.put("G4", "第四学科组");
        groupDescMap.put("G5", "第五学科组");
        groupDescMap.put("G6", "第六学科组");
        groupDescMap.put("Core", "核心课程");
        groupDescMap.put("SBC", "校本课程");

        for (EduSubjectCodeEnum eduSubjectCodeEnum : EduSubjectCodeEnum.values()) {
            String groupCode = eduSubjectCodeEnum.getGroupCode();
            String groupDesc = groupDescMap.getOrDefault(groupCode, groupCode);
            if (!map.containsKey(groupCode)) {
                map.put(groupCode, groupDesc);
            }
        }

        return map;
    }

    /**
     * 获取所有英文名称类型
     *
     * @return 返回所有英文名称的Map
     */
    public static Map<String, String> getAllEnNameTypes() {
        Map<String, String> map = new HashMap<>();
        for (EduSubjectCodeEnum eduSubjectCodeEnum : EduSubjectCodeEnum.values()) {
            map.put(eduSubjectCodeEnum.getKey(), eduSubjectCodeEnum.getEnName());
        }
        return map;
    }

    /**
     * 获取所有枚举项
     *
     * @return
     */
    public static List<Map<String, Object>> getAllItems() {
        List<Map<String, Object>> items = new ArrayList<>();
        for (EduSubjectCodeEnum item : EduSubjectCodeEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.getKey());
            map.put("value", item.getDesc());
            map.put("groupCode", item.getGroupCode());
            map.put("enName", item.getEnName());
            items.add(map);
        }
        return items;
    }

    /**
     * 按学科组分组获取学科列表
     *
     * @return 返回按学科组分组的学科列表
     */
    public static Map<String, List<Map<String, Object>>> getSubjectsByGroup() {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        Map<String, String> groupMap = getAllGroups();

        // 初始化所有学科组
        for (String groupCode : groupMap.keySet()) {
            result.put(groupCode, new ArrayList<>());
        }

        // 将学科分配到对应的学科组
        for (EduSubjectCodeEnum subjectEnum : EduSubjectCodeEnum.values()) {
            Map<String, Object> subjectInfo = new HashMap<>();
            subjectInfo.put("key", subjectEnum.getKey());
            subjectInfo.put("value", subjectEnum.getDesc());
            subjectInfo.put("enName", subjectEnum.getEnName());

            String groupCode = subjectEnum.getGroupCode();
            if (result.containsKey(groupCode)) {
                result.get(groupCode).add(subjectInfo);
            }
        }

        return result;
    }

    /**
     * 获取学科组及其包含的学科信息
     *
     * @return 返回学科组及其学科列表信息
     */
    public static List<Map<String, Object>> getGroupsWithSubjects() {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, List<Map<String, Object>>> subjectsByGroup = getSubjectsByGroup();
        Map<String, String> groupMap = getAllGroups();

        for (Map.Entry<String, String> entry : groupMap.entrySet()) {
            String groupCode = entry.getKey();
            String groupName = entry.getValue();

            Map<String, Object> groupInfo = new HashMap<>();
            groupInfo.put("groupCode", groupCode);
            groupInfo.put("groupName", groupName);
            groupInfo.put("subjects", subjectsByGroup.getOrDefault(groupCode, new ArrayList<>()));
            result.add(groupInfo);
        }

        return result;
    }
} 