package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 是否枚举值
 */
public enum EduSwitchEnum {
    /**
     * 是否枚举
     */
    DISABLE("0", "禁用"),
    ENABLE("1", "启用"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduSwitchEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduSwitchEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSwitchEnum eduSwitchEnum : EduSwitchEnum.values()) {
                if (key.equals(eduSwitchEnum.getKey())) {
                    return eduSwitchEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSwitchEnum eduSwitchEnum : EduSwitchEnum.values()) {
            map.put(eduSwitchEnum.getKey(), eduSwitchEnum.getDesc());
        }
        return map;
    }
} 