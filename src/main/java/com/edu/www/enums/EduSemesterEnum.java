package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 学期枚举值
 */
public enum EduSemesterEnum {
    /**
     * 学期枚举
     */
    FIRST("S1", "第一学期(S1)"),
    SECOND("S2", "第二学期(S2)"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduSemesterEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduSemesterEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSemesterEnum eduSemesterEnum : EduSemesterEnum.values()) {
                if (key.equals(eduSemesterEnum.getKey())) {
                    return eduSemesterEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSemesterEnum eduSemesterEnum : EduSemesterEnum.values()) {
            map.put(eduSemesterEnum.getKey(), eduSemesterEnum.getDesc());
        }
        return map;
    }
} 