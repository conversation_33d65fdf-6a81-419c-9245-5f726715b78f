package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 合同状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduContractStatusEnum {
    /**
     * 合同状态枚举
     */
    TERMINATED("0", "已终止"),
    DRAFT("1", "草稿"),
    PENDING_APPROVAL("2", "待审批"),
    APPROVING("3", "审批中"),
    APPROVED("4", "已审批"),
    SIGNED("5", "已签订"),
    EXECUTED("6", "已执行"),
    COMPLETED("7", "已完成"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduContractStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduContractStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduContractStatusEnum contractStatusEnum : EduContractStatusEnum.values()) {
                if (key.equals(contractStatusEnum.getKey())) {
                    return contractStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduContractStatusEnum contractStatusEnum : EduContractStatusEnum.values()) {
            map.put(contractStatusEnum.getKey(), contractStatusEnum.getDesc());
        }
        return map;
    }
}
