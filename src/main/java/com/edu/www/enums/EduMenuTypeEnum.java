package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 菜单类型枚举
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public enum EduMenuTypeEnum {
    /**
     * 菜单类型枚举
     */
    DIRECTORY("0", "目录菜单"),
    PAGE("1", "页面菜单"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduMenuTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduMenuTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduMenuTypeEnum eduMenuTypeEnum : EduMenuTypeEnum.values()) {
                if (key.equals(eduMenuTypeEnum.getKey())) {
                    return eduMenuTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduMenuTypeEnum eduMenuTypeEnum : EduMenuTypeEnum.values()) {
            map.put(eduMenuTypeEnum.getKey(), eduMenuTypeEnum.getDesc());
        }
        return map;
    }
} 