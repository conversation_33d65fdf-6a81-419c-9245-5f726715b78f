package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 证件类型枚举值
 */
public enum EduCardTypeEnum {
    /**
     * 身份证类型枚举
     */
    IDCARD("IDCARD", "身份证"),
    HKIDCARD("HKMIDCARD", "香港居民证"),
    MACAOMIDCARD("MACAOMIDCARD", "澳门居民证"),
    TWIDCARD("TWIDCARD", "台湾居民证"),
    PASSPORT("PASSPORT", "护照"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduCardTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }


    public String getKey() {
        return key;
    }

    public static EduCardTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduCardTypeEnum idCardTypeEnum : EduCardTypeEnum.values()) {
                if (key.equals(idCardTypeEnum.getKey())) {
                    return idCardTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduCardTypeEnum idCardTypeEnum : EduCardTypeEnum.values()) {
            map.put(idCardTypeEnum.getKey(), idCardTypeEnum.getDesc());
        }
        return map;
    }
}
