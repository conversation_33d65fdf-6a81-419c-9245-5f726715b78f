package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 基组编码枚举值
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduBaseGroupEnum {
    /**
     * 基组编码枚举
     */
    GROUP_A("A", "A组"),
    GROUP_B("B", "B组")

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBaseGroupEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBaseGroupEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBaseGroupEnum eduBaseGroupEnum : EduBaseGroupEnum.values()) {
                if (key.equals(eduBaseGroupEnum.getKey())) {
                    return eduBaseGroupEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBaseGroupEnum eduBaseGroupEnum : EduBaseGroupEnum.values()) {
            map.put(eduBaseGroupEnum.getKey(), eduBaseGroupEnum.getDesc());
        }
        return map;
    }
}
