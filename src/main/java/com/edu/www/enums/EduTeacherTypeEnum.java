package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 教师类型枚举值
 */
public enum EduTeacherTypeEnum {
    /**
     * 教师类型枚举
     */
    NO("0", "外教"),
    YES("1", "中教"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduTeacherTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduTeacherTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduTeacherTypeEnum eduTeacherTypeEnum : EduTeacherTypeEnum.values()) {
                if (key.equals(eduTeacherTypeEnum.getKey())) {
                    return eduTeacherTypeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduTeacherTypeEnum eduTeacherTypeEnum : EduTeacherTypeEnum.values()) {
            map.put(eduTeacherTypeEnum.getKey(), eduTeacherTypeEnum.getDesc());
        }
        return map;
    }
} 