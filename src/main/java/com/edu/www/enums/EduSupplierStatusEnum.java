package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 供应商合作状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduSupplierStatusEnum {
    /**
     * 供应商合作状态枚举
     */
    TERMINATED("0", "终止"),
    NORMAL("1", "正常"),
    SUSPENDED("2", "暂停"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduSupplierStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduSupplierStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSupplierStatusEnum eduSupplierStatusEnum : EduSupplierStatusEnum.values()) {
                if (key.equals(eduSupplierStatusEnum.getKey())) {
                    return eduSupplierStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSupplierStatusEnum eduSupplierStatusEnum : EduSupplierStatusEnum.values()) {
            map.put(eduSupplierStatusEnum.getKey(), eduSupplierStatusEnum.getDesc());
        }
        return map;
    }
}
