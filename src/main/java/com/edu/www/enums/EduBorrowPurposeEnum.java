package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 领用用途枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduBorrowPurposeEnum {
    /**
     * 领用用途枚举
     */
    TEACHING("1", "教学用"),
    LEARNING("2", "学习用"),
    RESEARCH("3", "研究用"),
    OTHER("4", "其他"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBorrowPurposeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBorrowPurposeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBorrowPurposeEnum borrowPurposeEnum : EduBorrowPurposeEnum.values()) {
                if (key.equals(borrowPurposeEnum.getKey())) {
                    return borrowPurposeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBorrowPurposeEnum borrowPurposeEnum : EduBorrowPurposeEnum.values()) {
            map.put(borrowPurposeEnum.getKey(), borrowPurposeEnum.getDesc());
        }
        return map;
    }
}
