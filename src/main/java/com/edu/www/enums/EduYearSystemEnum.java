package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 申请学制枚举值
 */
public enum EduYearSystemEnum {
    /**
     * 申请学制枚举
     */
    THREE_YEARS("3", "3 年"),
    THREE_HALF_YEARS("3.5", "3.5 年"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduYearSystemEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduYearSystemEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduYearSystemEnum eduYearSystemCodeEnum : EduYearSystemEnum.values()) {
                if (key.equals(eduYearSystemCodeEnum.getKey())) {
                    return eduYearSystemCodeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduYearSystemEnum eduYearSystemCodeEnum : EduYearSystemEnum.values()) {
            map.put(eduYearSystemCodeEnum.getKey(), eduYearSystemCodeEnum.getDesc());
        }
        return map;
    }
} 