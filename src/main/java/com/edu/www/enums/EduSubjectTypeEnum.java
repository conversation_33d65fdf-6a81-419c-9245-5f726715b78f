package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 学科分类枚举值
 */
public enum EduSubjectTypeEnum {
    /**
     * (数学)分析方法
     */
    AA("分析方法(AA)", "A&A"),

    /**
     * (数学)应用与解释
     */
    AI("应用与解释(AI)", "A&I"),

    /**
     * (中文)语言与文学
     */
    LL("语言与文学(LL)", "L&L"),

    /**
     * (中文)文学与表演艺术
     */
    LP("文学与表演艺术(LP)", "L&P"),

    /**
     * (英语)B1
     */
    // B_1("B1", "B1"),

    /**
     * (英语)B2
     */
    // B_2("B2", "B2"),

    /**
     * (英语)A1
     */
    // A_1("A1", "A1"),

    /**
     * (英语)A2
     */
    // A_2("A2", "A2"),


    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduSubjectTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduSubjectTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSubjectTypeEnum eduSubjectTypeEnum : EduSubjectTypeEnum.values()) {
                if (key.equals(eduSubjectTypeEnum.getKey())) {
                    return eduSubjectTypeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSubjectTypeEnum eduSubjectTypeEnum : EduSubjectTypeEnum.values()) {
            map.put(eduSubjectTypeEnum.getKey(), eduSubjectTypeEnum.getDesc());
        }
        return map;
    }
} 