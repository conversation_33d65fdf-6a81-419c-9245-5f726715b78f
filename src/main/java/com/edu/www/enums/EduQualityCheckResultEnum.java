package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 质量检查结果枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduQualityCheckResultEnum {
    /**
     * 质量检查结果枚举
     */
    UNQUALIFIED("0", "不合格"),
    QUALIFIED("1", "合格"),
    PENDING("2", "待检查"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduQualityCheckResultEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduQualityCheckResultEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduQualityCheckResultEnum qualityCheckResultEnum : EduQualityCheckResultEnum.values()) {
                if (key.equals(qualityCheckResultEnum.getKey())) {
                    return qualityCheckResultEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduQualityCheckResultEnum qualityCheckResultEnum : EduQualityCheckResultEnum.values()) {
            map.put(qualityCheckResultEnum.getKey(), qualityCheckResultEnum.getDesc());
        }
        return map;
    }
}
