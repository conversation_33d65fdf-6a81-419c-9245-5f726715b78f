package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 楼层枚举值
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduFloorEnum {
    /**
     * 楼层枚举
     */
    FLOOR_1("1", "1楼"),
    FLOOR_2("2", "2楼"),
    FLOOR_3("3", "3楼"),
    FLOOR_4("4", "4楼"),
    FLOOR_5("5", "5楼"),
    FLOOR_6("6", "6楼"),
    FLOOR_7("7", "7楼"),
    FLOOR_8("8", "8楼"),
    FLOOR_9("9", "9楼"),
    FLOOR_10("10", "10楼"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduFloorEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduFloorEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduFloorEnum eduFloorEnum : EduFloorEnum.values()) {
                if (key.equals(eduFloorEnum.getKey())) {
                    return eduFloorEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduFloorEnum eduFloorEnum : EduFloorEnum.values()) {
            map.put(eduFloorEnum.getKey(), eduFloorEnum.getDesc());
        }
        return map;
    }
}
