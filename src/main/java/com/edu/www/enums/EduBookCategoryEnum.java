package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 书籍分类枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduBookCategoryEnum {
    /**
     * 书籍分类枚举
     */
    CHINESE_TEXTBOOK("1", "中文教材"),
    ENGLISH_TEXTBOOK("2", "英文教材"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBookCategoryEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBookCategoryEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBookCategoryEnum eduBookCategoryEnum : EduBookCategoryEnum.values()) {
                if (key.equals(eduBookCategoryEnum.getKey())) {
                    return eduBookCategoryEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBookCategoryEnum eduBookCategoryEnum : EduBookCategoryEnum.values()) {
            map.put(eduBookCategoryEnum.getKey(), eduBookCategoryEnum.getDesc());
        }
        return map;
    }
}
