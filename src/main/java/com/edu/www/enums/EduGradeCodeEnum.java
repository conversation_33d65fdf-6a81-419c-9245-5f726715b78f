package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 年级编号枚举值
 */
public enum EduGradeCodeEnum {
    /**
     * 年级编号枚举
     */
    GRADE_6("6", "6年级"),
    GRADE_7("7", "7年级"),
    GRADE_8("8", "8年级"),
    GRADE_9("9", "9年级"),
    SPRING("spring", "春季班"),
    GRADE_10("10", "10年级"),
    GRADE_11("11", "11年级"),
    GRADE_12("12", "12年级"),
    // SELF_STUDY_ROOM("self_study_room", "自习室"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduGradeCodeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduGradeCodeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduGradeCodeEnum eduGradeCodeEnum : EduGradeCodeEnum.values()) {
                if (key.equals(eduGradeCodeEnum.getKey())) {
                    return eduGradeCodeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduGradeCodeEnum eduGradeCodeEnum : EduGradeCodeEnum.values()) {
            map.put(eduGradeCodeEnum.getKey(), eduGradeCodeEnum.getDesc());
        }
        return map;
    }

    /**
     * 根据部门编码获取对应的年级编号列表
     * @param deptCode 部门编码(IC：融合部、DP：高中部)
     * @return 返回包含筛选后年级枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getByDeptCode(String deptCode) {
        Map<String, String> map = new HashMap<>();

        if (StringUtils.isBlank(deptCode)) {
            return getAll();
        }

        if ("IC".equals(deptCode)) {
            // 融合部: 6年级、7年级、8年级、9年级
            map.put(GRADE_6.getKey(), GRADE_6.getDesc());
            map.put(GRADE_7.getKey(), GRADE_7.getDesc());
            map.put(GRADE_8.getKey(), GRADE_8.getDesc());
            map.put(GRADE_9.getKey(), GRADE_9.getDesc());
        } else if ("DP".equals(deptCode)) {
            // 高中部: 春季班、10年级、11年级、12年级
            map.put(SPRING.getKey(), SPRING.getDesc());
            map.put(GRADE_10.getKey(), GRADE_10.getDesc());
            map.put(GRADE_11.getKey(), GRADE_11.getDesc());
            map.put(GRADE_12.getKey(), GRADE_12.getDesc());
            // map.put(SELF_STUDY_ROOM.getKey(), SELF_STUDY_ROOM.getDesc());
        } else {
            // 如果是其他部门编码或无效编码，则返回所有年级
            return getAll();
        }

        return map;
    }
} 