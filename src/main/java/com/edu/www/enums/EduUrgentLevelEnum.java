package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 紧急程度枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduUrgentLevelEnum {
    /**
     * 紧急程度枚举
     */
    NORMAL("1", "普通"),
    URGENT("2", "紧急"),
    VERY_URGENT("3", "特急"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduUrgentLevelEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduUrgentLevelEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduUrgentLevelEnum urgentLevelEnum : EduUrgentLevelEnum.values()) {
                if (key.equals(urgentLevelEnum.getKey())) {
                    return urgentLevelEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduUrgentLevelEnum urgentLevelEnum : EduUrgentLevelEnum.values()) {
            map.put(urgentLevelEnum.getKey(), urgentLevelEnum.getDesc());
        }
        return map;
    }
}
