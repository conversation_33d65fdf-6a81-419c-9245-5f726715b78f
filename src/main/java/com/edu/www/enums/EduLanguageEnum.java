package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 语言枚举值
 */
public enum EduLanguageEnum {
    CHINESE("zh", "中文"),
    ENGLISH("en", "English"),
    SPANISH("es", "Español"),
    FRENCH("fr", "Français"),
    GERMAN("de", "Deutsch"),
    JAPANESE("ja", "日本語"),
    KOREAN("ko", "한국어"),
    ARABIC("ar", "العربية"),
    HINDI("hi", "हिन्दी"),
    PORTUGUESE("pt", "Português");

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduLanguageEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduLanguageEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduLanguageEnum eduLanguageEnum : EduLanguageEnum.values()) {
                if (key.equals(eduLanguageEnum.getKey())) {
                    return eduLanguageEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduLanguageEnum eduLanguageEnum : EduLanguageEnum.values()) {
            map.put(eduLanguageEnum.getKey(), eduLanguageEnum.getDesc());
        }
        return map;
    }
} 