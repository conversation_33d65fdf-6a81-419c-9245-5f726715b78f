package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 权限类型枚举
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public enum EduPermissionTypeEnum {
    /**
     * 权限类型枚举
     */
    MENU("1", "菜单权限"),
    BUTTON("2", "按钮权限"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPermissionTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPermissionTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPermissionTypeEnum eduPermissionTypeEnum : EduPermissionTypeEnum.values()) {
                if (key.equals(eduPermissionTypeEnum.getKey())) {
                    return eduPermissionTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPermissionTypeEnum eduPermissionTypeEnum : EduPermissionTypeEnum.values()) {
            map.put(eduPermissionTypeEnum.getKey(), eduPermissionTypeEnum.getDesc());
        }
        return map;
    }
} 