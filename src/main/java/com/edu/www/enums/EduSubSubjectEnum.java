package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 子学科枚举值
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduSubSubjectEnum {
    /**
     * 中文
     */
    CHINESE("Chinese", "中文"),

    /**
     * (中文)语言与文学
     */
    LL("L&L", "语言与文学(LL)"),

    /**
     * (中文)文学与表演艺术
     */
    LP("L&P", "文学与表演艺术(LP)"),

    /**
     * 日语
     */
    JAPANESE("Japanese", "日语"),

    /**
     * 体育
     */
    PE("PE", "体育"),

    /**
     * 计算机科学
     */
    COMPUTER_SCIENCE("computer_science", "计算机科学"),

    /**
     * 视觉艺术
     */
    VISUAL_ARTS("visual_arts", "视觉艺术(VA)"),

    /**
     * 音乐
     */
    MUSIC("Music", "音乐"),

    /**
     * 戏剧
     */
    DRAMA("Drama", "戏剧"),

    /**
     * 电子设计
     */
    DIGITAL_DESIGN("DigitalDesign", "电子设计"),

    /**
     * 产品设计
     */
    PRODUCT_DESIGN("ProductDesign", "产品设计"),

    /**
     * 科学
     */
    SCIENCE("Science", "科学"),

    /**
     * 综合科学
     */
    GENERAL_SCIENCE("GeneralScience", "综合科学"),

    /**
     * 生物
     */
    BIOLOGY("Biology", "生物"),

    /**
     * 化学
     */
    CHEMISTRY("Chemistry", "化学"),

    /**
     * 物理
     */
    PHYSICS("Physics", "物理"),

    /**
     * 西语
     */
    SPANISH("Spanish", "西语"),

    /**
     * 数学
     */
    MATHEMATICS("Math", "数学"),

    /**
     * (数学)分析方法
     */
    AA("A&A", "分析方法(AA)"),

    /**
     * (数学)应用与解释
     */
    AI("A&I", "应用与解释(AI)"),

    /**
     * 心理
     */
    PSYCHOLOGY("Psychology", "心理"),

    /**
     * 英语
     */
    ENGLISH("English", "英语"),

    /**
     * 词汇
     */
    VOCABULARY("Vocabulary", "词汇"),

    /**
     * 语法
     */
    GRAMMAR("Grammar", "语法"),

    /**
     * 口语
     */
    SPEAKING("Speaking", "口语"),

    /**
     * 阅读
     */
    READING("Reading", "阅读"),

    /**
     * 数字素养(Digital Literacy)
     */
    DIGITAL_LITERACY("digital_literacy", "数字素养(DL)"),

    /**
     * 综合听力和阅读(Integrated Listening and Reading)
     */
    ILR("ILR", "综合听力和阅读(ILR)"),

    /**
     * 经商
     */
    EBM("EBM", "经商(EBM)"),

    /**
     * 商管
     */
    BUSINESS_MANAGEMENT("BM", "商管"),

    /**
     * 经济
     */
    ECONOMICS("Economics", "经济"),

    /**
     * 历史
     */
    HISTORY("History", "历史"),

    /**
     * 地理
     */
    GEOGRAPHY("Geography", "地理"),

    /**
     * 认识论
     */
    TOK("TOK", "认识论(TOK)"),

    /**
     * 批判性思维
     */
    CRITICAL_THINKING("critical_thinking", "批判性思维"),

    /**
     * 创造行动与服务
     */
    CAS("CAS", "创造行动与服务(CAS)"),

    /**
     * 专题论文
     */
    EE("EE", "专题论文(EE)"),

    /**
     * 班会
     */
    HOMEROOM_MEETING("homeroom_meeting", "班会"),

    /**
     * 课间操
     */
    SPORTS_TIME("sports_time", "课间操"),

    /**
     * 自习
     */
    SELF_STUDY("self_study", "自习"),

    /**
     * 电影
     */
    MOVIES("Movies", "电影"),

    /**
     * 俱乐部
     */
    CLUBS("Clubs", "俱乐部"),

    /**
     * 社区项目
     */
    COMMUNITY_PROJECT("CommunityProject", "社区项目"),

    /**
     * 跨学科/集会
     */
    IDU_ASSEMBLY("IDUAssembly", "跨学科/集会"),

    /**
     * 咨询辅导课(counseling and guidance)
     */
    CAG("CAG", "咨询辅导"),


    ;

    /**
     * 缓存键值
     */
    private String key;

    /**
     * 描述
     */
    private String desc;

    EduSubSubjectEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    /**
     * 根据key获取枚举值
     *
     * @param key
     * @return
     */
    public static EduSubSubjectEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSubSubjectEnum eduSubSubjectEnum : EduSubSubjectEnum.values()) {
                if (key.equals(eduSubSubjectEnum.getKey())) {
                    return eduSubSubjectEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据key获取描述
     *
     * @param key
     * @return
     */
    public static String getDescByKey(String key) {
        EduSubSubjectEnum eduSubSubjectEnum = getByKey(key);
        return eduSubSubjectEnum == null ? null : eduSubSubjectEnum.getDesc();
    }

    /**
     * 获取所有子学科枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSubSubjectEnum eduSubSubjectEnum : EduSubSubjectEnum.values()) {
            map.put(eduSubSubjectEnum.getKey(), eduSubSubjectEnum.getDesc());
        }
        return map;
    }
} 