package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 民族枚举值
 */
public enum EduEthnicityEnum {
    /**
     * 民族枚举
     */
    HAN("HAN", "汉族"),
    MONGOL("MONGOL", "蒙古族"),
    TIBETAN("TIBETAN", "藏族"),
    UYGHUR("UYGHUR", "维吾尔族"),
    MIAO("MIAO", "苗族"),
    YI("YI", "彝族"),
    ZHUANG("ZHUANG", "壮族"),
    BOUYEI("BOUYEI", "布依族"),
    KOREAN("KOREAN", "朝鲜族"),
    MANCHU("MANCHU", "满族"),
    HUI("HUI", "回族"),
    OTHER("OTHER", "其他");

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduEthnicityEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduEthnicityEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduEthnicityEnum ethnicityEnum : EduEthnicityEnum.values()) {
                if (key.equals(ethnicityEnum.getKey())) {
                    return ethnicityEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduEthnicityEnum ethnicityEnum : EduEthnicityEnum.values()) {
            map.put(ethnicityEnum.getKey(), ethnicityEnum.getDesc());
        }
        return map;
    }
} 