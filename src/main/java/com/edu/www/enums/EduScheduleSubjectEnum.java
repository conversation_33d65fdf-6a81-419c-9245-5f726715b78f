package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 课程科目枚举值
 */
public enum EduScheduleSubjectEnum {
    /**
     * (中文)语言与文学
     */
    LL("L&L", "(Chinese)语言与文学(LL)"),

    /**
     * (中文)文学与表演艺术
     */
    LP("L&P", "(Chinese)文学与表演艺术(LP)"),

    /**
     * (数学)分析方法
     */
    AA("A&A", "(Math)分析方法(AA)"),

    /**
     * (数学)应用与解释
     */
    AI("A&I", "(Math)应用与解释(AI)"),

    /**
     * 商管
     */
    BUSINESS_MANAGEMENT("BM", "BM"),

    /**
     * 批判性思维
     */
    CRITICAL_THINKING("critical_thinking", "Critical Thinking"),

    /**
     * 自习
     */
    SELF_STUDY("self_study", "Self-study"),

    /**
     * 经商
     */
    EBM("EBM", "Econ&Business"),

    /**
     * 综合听力和阅读(Integrated Listening and Reading)
     */
    ILR("ILR", "Integrated Listening and Reading"),

    /**
     * 视觉艺术
     */
    VISUAL_ARTS("visual_arts", "VA"),

    /**
     * 数字素养(Digital Literacy)
     */
    DIGITAL_LITERACY("digital_literacy", "Digital Literacy"),

    /**
     * 计算机科学
     */
    COMPUTER_SCIENCE("computer_science", "Computer Science"),

    /**
     * 班会
     */
    HOMEROOM_MEETING("homeroom_meeting", "Homeroom Class"),

    /**
     * 咨询辅导课(counseling and guidance)
     */
    CAG("CAG", "咨询辅导"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduScheduleSubjectEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduScheduleSubjectEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduScheduleSubjectEnum eduScheduleSubjectEnum : EduScheduleSubjectEnum.values()) {
                if (key.equals(eduScheduleSubjectEnum.getKey())) {
                    return eduScheduleSubjectEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduScheduleSubjectEnum eduScheduleSubjectEnum : EduScheduleSubjectEnum.values()) {
            map.put(eduScheduleSubjectEnum.getKey(), eduScheduleSubjectEnum.getDesc());
        }
        return map;
    }
} 