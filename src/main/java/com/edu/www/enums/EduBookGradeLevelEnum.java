package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 书籍年级编号枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduBookGradeLevelEnum {
    /**
     * 书籍年级编号枚举
     */
    GRADE_6("6", "6年级"),
    GRADE_7("7", "7年级"),
    GRADE_8("8", "8年级"),
    GRADE_9("9", "9年级"),
    SPRING_CLASS("spring", "春季班"),
    GRADE_10("10", "10年级"),
    GRADE_11("11", "11年级"),
    GRADE_12("12", "12年级"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBookGradeLevelEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBookGradeLevelEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBookGradeLevelEnum eduBookGradeLevelEnum : EduBookGradeLevelEnum.values()) {
                if (key.equals(eduBookGradeLevelEnum.getKey())) {
                    return eduBookGradeLevelEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBookGradeLevelEnum eduBookGradeLevelEnum : EduBookGradeLevelEnum.values()) {
            map.put(eduBookGradeLevelEnum.getKey(), eduBookGradeLevelEnum.getDesc());
        }
        return map;
    }
}
