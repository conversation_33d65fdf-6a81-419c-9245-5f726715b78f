package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Stream;

/**
 * 学科组编码枚举
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduGroupCodeEnum {
    //****************************************高中***********************************************//

    /**
     * 第一学科组
     */
    G1("G1", "第一学科组"),
    
    /**
     * 第二学科组
     */
    G2("G2", "第二学科组"),
    
    /**
     * 第三学科组
     */
    G3("G3", "第三学科组"),
    
    /**
     * 第四学科组
     */
    G4("G4", "第四学科组"),
    
    /**
     * 第五学科组
     */
    G5("G5", "第五学科组"),
    
    /**
     * 第六学科组
     */
    G6("G6", "第六学科组"),
    
    /**
     * 核心课程
     */
    CORE("Core", "核心课程"),

    //****************************************融合***********************************************//

    /**
     * 语言与文学
     */
    LANGUAGE_LITERATURE("LanguageLiterature", "语言与文学"),

    /**
     * 语言习得
     */
    LANGUAGE_ACQUISITION("LanguageAcquisition", "语言习得"),

    /**
     * 数学
     */
    MATHEMATICS("Mathematics", "数学"),

    /**
     * 科学
     */
    SCIENCE("Science", "科学"),

    /**
     * 个人与社会
     */
    INDIVIDUAL_SOCIETIES("IndividualSocieties", "个人与社会"),

    /**
     * 体育与健康教育
     */
    PHYSICAL_HEALTH_EDUCATION("PhysicalHealthEducation", "体育与健康教育"),

    /**
     * 艺术
     */
    ARTS("Arts", "艺术"),

    /**
     * 设计
     */
    DESIGN("Design", "设计"),

    //****************************************校本***********************************************//

    /**
     * 校本课程
     */
    SBC("SBC", "校本课程");


    /**
     * 缓存键值
     */
    private String key;
    
    /**
     * 描述
     */
    private String desc;

    EduGroupCodeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    /**
     * 根据key获取枚举值
     *
     * @param key
     * @return
     */
    public static EduGroupCodeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduGroupCodeEnum eduGroupCodeEnum : EduGroupCodeEnum.values()) {
                if (key.equals(eduGroupCodeEnum.getKey())) {
                    return eduGroupCodeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据key获取描述
     *
     * @param key
     * @return
     */
    public static String getDescByKey(String key) {
        EduGroupCodeEnum eduGroupCodeEnum = getByKey(key);
        return eduGroupCodeEnum == null ? null : eduGroupCodeEnum.getDesc();
    }

    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduGroupCodeEnum eduGroupCodeEnum : EduGroupCodeEnum.values()) {
            map.put(eduGroupCodeEnum.getKey(), eduGroupCodeEnum.getDesc());
        }
        return map;
    }

    /**
     * 获取所有枚举项
     *
     * @return
     */
    public static List<Map<String, String>> getAllItems() {
        List<Map<String, String>> items = new ArrayList<>();
        for (EduGroupCodeEnum item : EduGroupCodeEnum.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("key", item.getKey());
            map.put("value", item.getDesc());
            items.add(map);
        }
        return items;
    }
} 