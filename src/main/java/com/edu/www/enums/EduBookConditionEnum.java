package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 书籍状况枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduBookConditionEnum {
    /**
     * 书籍状况枚举
     */
    BRAND_NEW("1", "全新"),
    GOOD("2", "良好"),
    AVERAGE("3", "一般"),
    DAMAGED("4", "损坏"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBookConditionEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBookConditionEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBookConditionEnum eduBookConditionEnum : EduBookConditionEnum.values()) {
                if (key.equals(eduBookConditionEnum.getKey())) {
                    return eduBookConditionEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBookConditionEnum eduBookConditionEnum : EduBookConditionEnum.values()) {
            map.put(eduBookConditionEnum.getKey(), eduBookConditionEnum.getDesc());
        }
        return map;
    }
}
