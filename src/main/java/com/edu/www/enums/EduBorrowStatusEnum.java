package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 领用状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduBorrowStatusEnum {
    /**
     * 领用状态枚举
     */
    LOST("0", "遗失"),
    BORROWED("1", "已借出"),
    RETURNED("2", "已归还"),
    OVERDUE("3", "逾期未还"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBorrowStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBorrowStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBorrowStatusEnum borrowStatusEnum : EduBorrowStatusEnum.values()) {
                if (key.equals(borrowStatusEnum.getKey())) {
                    return borrowStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBorrowStatusEnum borrowStatusEnum : EduBorrowStatusEnum.values()) {
            map.put(borrowStatusEnum.getKey(), borrowStatusEnum.getDesc());
        }
        return map;
    }
}
