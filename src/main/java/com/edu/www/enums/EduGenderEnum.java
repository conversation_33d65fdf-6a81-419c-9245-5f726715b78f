package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 性别枚举值
 */
public enum EduGenderEnum {
    /**
     * 性别枚举
     */
    FEMALE("0", "女"),
    MALE("1", "男"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduGenderEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduGenderEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduGenderEnum eduGenderEnum : EduGenderEnum.values()) {
                if (key.equals(eduGenderEnum.getKey())) {
                    return eduGenderEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduGenderEnum eduGenderEnum : EduGenderEnum.values()) {
            map.put(eduGenderEnum.getKey(), eduGenderEnum.getDesc());
        }
        return map;
    }
} 