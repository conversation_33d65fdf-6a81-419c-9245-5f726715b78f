package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 时段枚举值
 */
public enum EduDayPartEnum {
    /**
     * 时段枚举
     */
    MORNING("0", "上午"),
    NOON("1", "中午"),
    AFTERNOON("2", "下午"),
    EVENING("3", "晚上"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduDayPartEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduDayPartEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduDayPartEnum eduDayPartEnum : EduDayPartEnum.values()) {
                if (key.equals(eduDayPartEnum.getKey())) {
                    return eduDayPartEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduDayPartEnum eduDayPartEnum : EduDayPartEnum.values()) {
            map.put(eduDayPartEnum.getKey(), eduDayPartEnum.getDesc());
        }
        return map;
    }
} 