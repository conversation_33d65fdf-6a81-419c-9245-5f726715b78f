package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 血型枚举值
 */
public enum EduBloodTypeEnum {
    /**
     * 血型枚举
     */
    A_POSITIVE("A", "A 型"),
    B_POSITIVE("B", "B 型"),
    AB_POSITIVE("AB", "AB 型"),
    O_POSITIVE("O", "O 型"),
    UNKNOWN("UNKNOWN", "未知");

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBloodTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBloodTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBloodTypeEnum bloodTypeEnum : EduBloodTypeEnum.values()) {
                if (key.equals(bloodTypeEnum.getKey())) {
                    return bloodTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBloodTypeEnum bloodTypeEnum : EduBloodTypeEnum.values()) {
            map.put(bloodTypeEnum.getKey(), bloodTypeEnum.getDesc());
        }
        return map;
    }
} 