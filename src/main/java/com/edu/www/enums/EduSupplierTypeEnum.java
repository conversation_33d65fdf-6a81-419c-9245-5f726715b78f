package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 供应商类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduSupplierTypeEnum {
    /**
     * 供应商类型枚举
     */
    CHINESE_BOOK("1", "中文图书"),
    FOREIGN_BOOK("2", "外文图书"),
    COMPREHENSIVE("3", "综合"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduSupplierTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduSupplierTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduSupplierTypeEnum eduSupplierTypeEnum : EduSupplierTypeEnum.values()) {
                if (key.equals(eduSupplierTypeEnum.getKey())) {
                    return eduSupplierTypeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduSupplierTypeEnum eduSupplierTypeEnum : EduSupplierTypeEnum.values()) {
            map.put(eduSupplierTypeEnum.getKey(), eduSupplierTypeEnum.getDesc());
        }
        return map;
    }
}
