package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 采购付款方式枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduPurchasePaymentMethodEnum {
    /**
     * 采购付款方式枚举
     */
    PAY_FIRST("1", "先付款后发货"),
    DELIVER_FIRST("2", "先发货后付款"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPurchasePaymentMethodEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPurchasePaymentMethodEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPurchasePaymentMethodEnum purchasePaymentMethodEnum : EduPurchasePaymentMethodEnum.values()) {
                if (key.equals(purchasePaymentMethodEnum.getKey())) {
                    return purchasePaymentMethodEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPurchasePaymentMethodEnum purchasePaymentMethodEnum : EduPurchasePaymentMethodEnum.values()) {
            map.put(purchasePaymentMethodEnum.getKey(), purchasePaymentMethodEnum.getDesc());
        }
        return map;
    }
}
