package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduPaymentMethodEnum {
    /**
     * 支付方式枚举
     */
    BANK_TRANSFER("1", "银行转账"),
    CHECK("2", "支票"),
    CASH("3", "现金"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduPaymentMethodEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduPaymentMethodEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPaymentMethodEnum paymentMethodEnum : EduPaymentMethodEnum.values()) {
                if (key.equals(paymentMethodEnum.getKey())) {
                    return paymentMethodEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPaymentMethodEnum paymentMethodEnum : EduPaymentMethodEnum.values()) {
            map.put(paymentMethodEnum.getKey(), paymentMethodEnum.getDesc());
        }
        return map;
    }
}
