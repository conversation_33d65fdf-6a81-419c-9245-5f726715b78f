package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工作状态枚举值
 */
public enum EduWorkStatusEnum {
    /**
     * 工作状态枚举
     */
    RESIGNED("0", "离职"),
    ON_DUTY("1", "在职"),
    BUSINESS_TRIP("2", "出差"),
    LEAVE("3", "请假"),
    VACATION("4", "休假"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduWorkStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduWorkStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduWorkStatusEnum eduWorkStatusEnum : EduWorkStatusEnum.values()) {
                if (key.equals(eduWorkStatusEnum.getKey())) {
                    return eduWorkStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduWorkStatusEnum eduWorkStatusEnum : EduWorkStatusEnum.values()) {
            map.put(eduWorkStatusEnum.getKey(), eduWorkStatusEnum.getDesc());
        }
        return map;
    }
} 