package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 考试类型枚举
 */
public enum EduPaperTypeEnum {
    PAPER("PAPER", "Paper"),
    PAPER_1("PAPER_1", "P1"),
    PAPER_2("PAPER_2", "P2"),
    PAPER_3("PAPER_3", "P3"),
    PAPER_1_1A_1B("PAPER_1_1A_1B", "P1 (1a & 1b)"),
    PAPER_2_1A_1B("PAPER_2_1A_1B", "P2 (1a & 1b)"),
    PAPER_1_LISTENING("PAPER_1_LISTENING", "P1 Listening"),
    PAPER_2_LISTENING("PAPER_2_LISTENING", "P2 Listening"),
    PAPER_1_READING("PAPER_1_READING", "P1 Reading"),
    PAPER_2_READING("PAPER_2_READING", "P2 Reading");

    private String key;
    private String desc;

    EduPaperTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static EduPaperTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduPaperTypeEnum type : EduPaperTypeEnum.values()) {
                if (key.equals(type.getKey())) {
                    return type;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduPaperTypeEnum type : EduPaperTypeEnum.values()) {
            map.put(type.getKey(), type.getDesc());
        }
        return map;
    }
} 