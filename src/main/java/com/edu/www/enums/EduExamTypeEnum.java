package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 考试类型枚举
 */
public enum EduExamTypeEnum {
    /**
     * 考试类型
     */
    MONTHLY_EXAM("MONTHLY", "月考"),
    MID_TERM_EXAM("MID_TERM", "期中"),
    MOCK_EXAM("MOCK", "模考"),
    IB_TERM_EXAM("IB_TERM", "IB大考"),
    FINAL_TERM_EXAM("FINAL_TERM", "期末");

    /**
     * 键
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduExamTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduExamTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduExamTypeEnum eduExamTypeEnum : EduExamTypeEnum.values()) {
                if (key.equals(eduExamTypeEnum.getKey())) {
                    return eduExamTypeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduExamTypeEnum eduExamTypeEnum : EduExamTypeEnum.values()) {
            map.put(eduExamTypeEnum.getKey(), eduExamTypeEnum.getDesc());
        }
        return map;
    }
} 