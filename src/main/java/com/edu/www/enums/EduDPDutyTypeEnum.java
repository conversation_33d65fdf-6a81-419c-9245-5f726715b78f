package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * DP部门值日类型枚举值
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduDPDutyTypeEnum {
    /**
     * 值日类型枚举
     */
    DUTY("0", "值日", "(06:30-21:10)"),
    YEAR_10_AB("1", "Year10A+B", "(18:30-21:10)"),
    YEAR_11_AB("2", "Year11A+B", "(18:30-21:10)"),
    YEAR_12_SPRING("3", "Year12+春季", "(18:30-21:10)"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;
    /**
     * 时间段
     */
    private String timeDesc;

    EduDPDutyTypeEnum(String key, String desc, String timeDesc) {
        this.key = key;
        this.desc = desc;
        this.timeDesc = timeDesc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public String getTimeDesc() {
        return timeDesc;
    }

    public static EduDPDutyTypeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduDPDutyTypeEnum eduDPDutyTypeEnum : EduDPDutyTypeEnum.values()) {
                if (key.equals(eduDPDutyTypeEnum.getKey())) {
                    return eduDPDutyTypeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值（仅描述）
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAllDesc() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduDPDutyTypeEnum eduDPDutyTypeEnum : EduDPDutyTypeEnum.values()) {
            map.put(eduDPDutyTypeEnum.getKey(), eduDPDutyTypeEnum.getDesc());
        }
        return map;
    }

    /**
     * 获取所有枚举值的详细信息
     *
     * @return 返回包含所有枚举值详细信息的Map，key为枚举的键，value为包含描述、时间段等信息的Map
     */
    public static Map<String, Map<String, String>> getAll() {
        Map<String, Map<String, String>> map = new LinkedHashMap<>();
        for (EduDPDutyTypeEnum eduDPDutyTypeEnum : EduDPDutyTypeEnum.values()) {
            Map<String, String> detail = new LinkedHashMap<>();
            detail.put("key", eduDPDutyTypeEnum.getKey());
            detail.put("desc", eduDPDutyTypeEnum.getDesc());
            detail.put("timeDesc", eduDPDutyTypeEnum.getTimeDesc());
            map.put(eduDPDutyTypeEnum.getKey(), detail);
        }
        return map;
    }
}
