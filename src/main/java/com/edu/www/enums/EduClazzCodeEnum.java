package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 班级编号枚举（整合年级和班级信息）
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public enum EduClazzCodeEnum {
    /**
     * 6年级A班
     */
    GRADE_6A("6A", "6年级 A班", "6", "A", true),
    
    /**
     * 6年级B班
     */
    GRADE_6B("6B", "6年级 B班", "6", "B", true),
    
    /**
     * 7年级A班
     */
    GRADE_7A("7A", "7年级 A班", "7", "A", true),
    
    /**
     * 7年级B班
     */
    GRADE_7B("7B", "7年级 B班", "7", "B", true),
    
    /**
     * 8年级A班
     */
    GRADE_8A("8A", "8年级 A班", "8", "A", true),
    
    /**
     * 8年级B班
     */
    GRADE_8B("8B", "8年级 B班", "8", "B", true),
    
    /**
     * 9年级（不分班）
     */
    GRADE_9("9", "9年级", "9", "", false),
    
    /**
     * 春季班（不分班）
     */
    SPRING("SPRING", "春季班", "SPRING", "", false),
    
    /**
     * 10年级A班
     */
    GRADE_10A("10A", "10年级 A班", "10", "A", true),
    
    /**
     * 10年级B班
     */
    GRADE_10B("10B", "10年级 B班", "10", "B", true),
    
    /**
     * 11年级A班
     */
    GRADE_11A("11A", "11年级 A班", "11", "A", true),
    
    /**
     * 11年级B班
     */
    GRADE_11B("11B", "11年级 B班", "11", "B", true),
    
    /**
     * 12年级（不分班）
     */
    GRADE_12("12", "12年级", "12", "", false);

    /**
     * 缓存键值
     */
    private String key;
    
    /**
     * 描述
     */
    private String desc;
    
    /**
     * 年级编码
     */
    private String gradeCode;
    
    /**
     * 班级编码
     */
    private String classCode;
    
    /**
     * 是否分A、B班
     */
    private boolean hasClassDivision;

    EduClazzCodeEnum(String key, String desc, String gradeCode, String classCode, boolean hasClassDivision) {
        this.key = key;
        this.desc = desc;
        this.gradeCode = gradeCode;
        this.classCode = classCode;
        this.hasClassDivision = hasClassDivision;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }
    
    public String getGradeCode() {
        return gradeCode;
    }
    
    public String getClassCode() {
        return classCode;
    }
    
    public boolean isHasClassDivision() {
        return hasClassDivision;
    }

    /**
     * 根据key获取枚举值
     *
     * @param key
     * @return
     */
    public static EduClazzCodeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
                if (key.equals(eduClazzCodeEnum.getKey())) {
                    return eduClazzCodeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据key获取描述
     *
     * @param key
     * @return
     */
    public static String getDescByKey(String key) {
        EduClazzCodeEnum eduClazzCodeEnum = getByKey(key);
        return eduClazzCodeEnum == null ? null : eduClazzCodeEnum.getDesc();
    }

    /**
     * 获取所有班级编码
     * @return 返回包含所有班级编码的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
            map.put(eduClazzCodeEnum.getKey(), eduClazzCodeEnum.getDesc());
        }
        return map;
    }
    
    /**
     * 根据年级编码获取对应的班级列表
     * @param gradeCode 年级编码
     * @return 返回包含筛选后班级枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getByGradeCode(String gradeCode) {
        Map<String, String> map = new HashMap<>();
        
        if (StringUtils.isBlank(gradeCode)) {
            return getAll();
        }
        
        for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
            if (gradeCode.equals(eduClazzCodeEnum.getGradeCode())) {
                map.put(eduClazzCodeEnum.getKey(), eduClazzCodeEnum.getDesc());
            }
        }
        
        return map;
    }
    
    /**
     * 根据部门编码获取对应的班级列表
     * @param deptCode 部门编码(IC：融合部、DP：高中部)
     * @return 返回包含筛选后班级枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getByDeptCode(String deptCode) {
        Map<String, String> map = new HashMap<>();
        
        if (StringUtils.isBlank(deptCode)) {
            return getAll();
        }
        
        if ("IC".equals(deptCode)) {
            // 融合部: 6年级、7年级、8年级、9年级
            for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
                String gradeCode = eduClazzCodeEnum.getGradeCode();
                if ("6".equals(gradeCode) || "7".equals(gradeCode) || "8".equals(gradeCode) || "9".equals(gradeCode)) {
                    map.put(eduClazzCodeEnum.getKey(), eduClazzCodeEnum.getDesc());
                }
            }
        } else if ("DP".equals(deptCode)) {
            // 高中部: 春季班、10年级、11年级、12年级
            for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
                String gradeCode = eduClazzCodeEnum.getGradeCode();
                if ("spring".equals(gradeCode) || "10".equals(gradeCode) || "11".equals(gradeCode) || "12".equals(gradeCode)) {
                    map.put(eduClazzCodeEnum.getKey(), eduClazzCodeEnum.getDesc());
                }
            }
        } else {
            // 如果是其他部门编码或无效编码，则返回所有班级
            return getAll();
        }
        
        return map;
    }
    
    /**
     * 获取所有年级编码（不重复）
     * @return 返回所有不重复的年级编码和描述Map
     */
    public static Map<String, String> getAllGrades() {
        Map<String, String> map = new HashMap<>();
        Map<String, String> gradeDescMap = new HashMap<>();
        
        // 配置年级描述
        gradeDescMap.put("6", "6年级");
        gradeDescMap.put("7", "7年级");
        gradeDescMap.put("8", "8年级");
        gradeDescMap.put("9", "9年级");
        gradeDescMap.put("spring", "春季班");
        gradeDescMap.put("10", "10年级");
        gradeDescMap.put("11", "11年级");
        gradeDescMap.put("12", "12年级");
        
        for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
            String gradeCode = eduClazzCodeEnum.getGradeCode();
            String gradeDesc = gradeDescMap.getOrDefault(gradeCode, gradeCode);
            if (!map.containsKey(gradeCode)) {
                map.put(gradeCode, gradeDesc);
            }
        }
        
        return map;
    }
    
    /**
     * 获取所有班级类型（A、B）
     * @return 返回所有班级类型的Map
     */
    public static Map<String, String> getAllClassTypes() {
        Map<String, String> map = new HashMap<>();
        map.put("A", "A班");
        map.put("B", "B班");
        return map;
    }
    
    /**
     * 判断某个年级是否分A、B班
     * @param gradeCode 年级编码
     * @return 是否分班
     */
    public static boolean hasClassDivision(String gradeCode) {
        if (StringUtils.isBlank(gradeCode)) {
            return false;
        }
        
        for (EduClazzCodeEnum eduClazzCodeEnum : EduClazzCodeEnum.values()) {
            if (gradeCode.equals(eduClazzCodeEnum.getGradeCode())) {
                return eduClazzCodeEnum.isHasClassDivision();
            }
        }
        
        return false;
    }
    
    /**
     * 获取所有枚举项
     *
     * @return
     */
    public static List<Map<String, Object>> getAllItems() {
        List<Map<String, Object>> items = new ArrayList<>();
        for (EduClazzCodeEnum item : EduClazzCodeEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.getKey());
            map.put("value", item.getDesc());
            map.put("gradeCode", item.getGradeCode());
            map.put("classCode", item.getClassCode());
            map.put("hasClassDivision", item.isHasClassDivision());
            items.add(map);
        }
        return items;
    }
} 