package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 请求方法枚举
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public enum EduMethodEnum {
    /**
     * 请求方法枚举
     */
    GET("GET", "GET 请求"),
    POST("POST", "POST 请求"),
    PUT("PUT", "PUT 请求"),
    DELETE("DELETE", "DELETE 请求"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduMethodEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduMethodEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduMethodEnum eduMethodEnum : EduMethodEnum.values()) {
                if (key.equals(eduMethodEnum.getKey())) {
                    return eduMethodEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduMethodEnum eduMethodEnum : EduMethodEnum.values()) {
            map.put(eduMethodEnum.getKey(), eduMethodEnum.getDesc());
        }
        return map;
    }
} 