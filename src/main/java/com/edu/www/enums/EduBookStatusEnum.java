package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 书籍状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum EduBookStatusEnum {
    /**
     * 书籍状态枚举
     */
    OFFLINE("0", "下架"),
    NORMAL("1", "正常"),
    DAMAGED("2", "损坏"),
    LOST("3", "遗失"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduBookStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduBookStatusEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduBookStatusEnum eduBookStatusEnum : EduBookStatusEnum.values()) {
                if (key.equals(eduBookStatusEnum.getKey())) {
                    return eduBookStatusEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduBookStatusEnum eduBookStatusEnum : EduBookStatusEnum.values()) {
            map.put(eduBookStatusEnum.getKey(), eduBookStatusEnum.getDesc());
        }
        return map;
    }
}
