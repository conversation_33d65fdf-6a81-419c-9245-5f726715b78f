package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 衣服尺码枚举值
 */
public enum EduClothingSizeEnum {
    /**
     * 衣服尺码枚举
     */
    XXS("XXS", "XXS"),
    XS("XS", "XS"),
    S("S", "S"),
    M("M", "M"),
    L("L", "L"),
    XL("XL", "XL"),
    XXL("XXL", "XXL"),
    XXXL("XXXL", "XXXL"),
    OTHER("OTHER", "其他");

    /**
     * 缓存键值
     */
    private String key;

    /**
     * 描述
     */
    private String desc;

    EduClothingSizeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduClothingSizeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduClothingSizeEnum clothingSizeEnum : EduClothingSizeEnum.values()) {
                if (key.equals(clothingSizeEnum.getKey())) {
                    return clothingSizeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值
     *
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduClothingSizeEnum clothingSizeEnum : EduClothingSizeEnum.values()) {
            map.put(clothingSizeEnum.getKey(), clothingSizeEnum.getDesc());
        }
        return map;
    }
} 