package com.edu.www.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 部门编号枚举值
 */
public enum EduDeptCodeEnum {
    /**
     * 部门编号枚举
     */
    XB("XB", "校办"),
    SY("SY", "双语部"),
    IC("IC", "融合部"),
    DP("DP", "高中部"),

    ;

    /**
     * 缓存键值
     */
    private String key;
    /**
     * 描述
     */
    private String desc;

    EduDeptCodeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static EduDeptCodeEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (EduDeptCodeEnum eduDeptCodeEnum : EduDeptCodeEnum.values()) {
                if (key.equals(eduDeptCodeEnum.getKey())) {
                    return eduDeptCodeEnum;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有枚举值
     * @return 返回包含所有枚举值的Map，key为枚举的键，value为枚举的描述
     */
    public static Map<String, String> getAll() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EduDeptCodeEnum eduDeptCodeEnum : EduDeptCodeEnum.values()) {
            map.put(eduDeptCodeEnum.getKey(), eduDeptCodeEnum.getDesc());
        }
        return map;
    }
} 