package com.edu.www.dto;
/**
 * 学生学科数据传输对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class StudentSubjectDTO extends BaseDTO {

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 所选科目
     */
    private String subject;

    /**
     * 所选科目编号
     */
    private String subjectCode;

    /**
     * 学科分类(AA：A&A、AI：A&I、LL：L&L、LP：L&P)
     */
    private String subjectType;

    /**
     * 科目层级(SL：SL、HL：HL)
     */
    private String subjectLevel;

    /**
     * 说明
     */
    private String description;
    
    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    public String getSubjectLevel() {
        return subjectLevel;
    }

    public void setSubjectLevel(String subjectLevel) {
        this.subjectLevel = subjectLevel;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
} 