package com.edu.www.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

/**
 * 值日导出数据模型
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
@ContentRowHeight(25)
@HeadRowHeight(30)
public class DutyExportDTO {

    @ExcelProperty(value = "星期日期\n时间", index = 0)
    @ColumnWidth(15)
    private String timeLabel;

    @ExcelProperty(value = "一", index = 1)
    @ColumnWidth(12)
    private String monday;

    @ExcelProperty(value = "二", index = 2)
    @ColumnWidth(12)
    private String tuesday;

    @ExcelProperty(value = "三", index = 3)
    @ColumnWidth(12)
    private String wednesday;

    @ExcelProperty(value = "四", index = 4)
    @ColumnWidth(12)
    private String thursday;

    @ExcelProperty(value = "五", index = 5)
    @ColumnWidth(12)
    private String friday;

    @ExcelProperty(value = "六", index = 6)
    @ColumnWidth(12)
    private String saturday;

    @ExcelProperty(value = "日", index = 7)
    @ColumnWidth(12)
    private String sunday;

    // 构造函数
    public DutyExportDTO() {}

    public DutyExportDTO(String timeLabel, String monday, String tuesday, String wednesday, 
                        String thursday, String friday, String saturday, String sunday) {
        this.timeLabel = timeLabel;
        this.monday = monday;
        this.tuesday = tuesday;
        this.wednesday = wednesday;
        this.thursday = thursday;
        this.friday = friday;
        this.saturday = saturday;
        this.sunday = sunday;
    }

    // Getter和Setter方法
    public String getTimeLabel() {
        return timeLabel;
    }

    public void setTimeLabel(String timeLabel) {
        this.timeLabel = timeLabel;
    }

    public String getMonday() {
        return monday;
    }

    public void setMonday(String monday) {
        this.monday = monday;
    }

    public String getTuesday() {
        return tuesday;
    }

    public void setTuesday(String tuesday) {
        this.tuesday = tuesday;
    }

    public String getWednesday() {
        return wednesday;
    }

    public void setWednesday(String wednesday) {
        this.wednesday = wednesday;
    }

    public String getThursday() {
        return thursday;
    }

    public void setThursday(String thursday) {
        this.thursday = thursday;
    }

    public String getFriday() {
        return friday;
    }

    public void setFriday(String friday) {
        this.friday = friday;
    }

    public String getSaturday() {
        return saturday;
    }

    public void setSaturday(String saturday) {
        this.saturday = saturday;
    }

    public String getSunday() {
        return sunday;
    }

    public void setSunday(String sunday) {
        this.sunday = sunday;
    }
}
