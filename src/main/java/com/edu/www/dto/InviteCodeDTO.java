package com.edu.www.dto;

import java.util.Date;

/**
 * 邀请码数据传输对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class InviteCodeDTO extends BaseDTO {

    /**
     * 邀请码
     */
    private String code;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 使用时间
     */
    private Date usedTime;

    /**
     * 是否使用(0：否、1：是)
     */
    private String isUsed;

    /**
     * 过期时间
     */
    private Date expiryTime;

    /**
     * 说明
     */
    private String description;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getUsedTime() {
        return usedTime;
    }

    public void setUsedTime(Date usedTime) {
        this.usedTime = usedTime;
    }

    public String getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(String isUsed) {
        this.isUsed = isUsed;
    }

    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
} 