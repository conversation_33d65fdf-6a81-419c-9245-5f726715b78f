package com.edu.www.dto;

/**
 * 招生信息数据传输对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class AdmissionDTO extends BaseDTO {

    /**
     * 招生名称
     */
    private String name;

    /**
     * 联系电话
     */
    private String phoneNum;

    /**
     * 招生年级编码
     */
    private String admissionGradeCode;

    /**
     * 学制编码
     */
    private String yearSystemCode;

    /**
     * 是否入读(0：否、1：是)
     */
    private String isEnrolled;

    /**
     * 邀请说明
     */
    private String inviteDescription;

    /**
     * 考试信息
     */
    private String examInfo;

    /**
     * 说明
     */
    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getAdmissionGradeCode() {
        return admissionGradeCode;
    }

    public void setAdmissionGradeCode(String admissionGradeCode) {
        this.admissionGradeCode = admissionGradeCode;
    }

    public String getYearSystemCode() {
        return yearSystemCode;
    }

    public void setYearSystemCode(String yearSystemCode) {
        this.yearSystemCode = yearSystemCode;
    }

    public String getIsEnrolled() {
        return isEnrolled;
    }

    public void setIsEnrolled(String isEnrolled) {
        this.isEnrolled = isEnrolled;
    }

    public String getInviteDescription() {
        return inviteDescription;
    }

    public void setInviteDescription(String inviteDescription) {
        this.inviteDescription = inviteDescription;
    }

    public String getExamInfo() {
        return examInfo;
    }

    public void setExamInfo(String examInfo) {
        this.examInfo = examInfo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}