package com.edu.www.dto;

/**
 * 文件信息数据传输对象
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
public class FileDTO extends BaseDTO {

    /**
     * 文件唯一标识符，格式：yyyyMMdd_xxxxxxxx
     */
    private String fileId;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 存储目录
     */
    private String storageDir;

    /**
     * 预览URL
     */
    private String previewUrl;

    /**
     * 是否可预览(0: 否, 1: 是)
     */
    private String isPreview;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 完整文件路径
     */
    private String fullPath;

    /**
     * 状态(0：禁用、1：启用)
     */
    private String status;

    /**
     * 备注
     */
    private String description;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getStorageDir() {
        return storageDir;
    }

    public void setStorageDir(String storageDir) {
        this.storageDir = storageDir;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getIsPreview() {
        return isPreview;
    }

    public void setIsPreview(String isPreview) {
        this.isPreview = isPreview;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "FileDTO{" +
                "fileId='" + fileId + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileType='" + fileType + '\'' +
                ", fileSize=" + fileSize +
                ", storageDir='" + storageDir + '\'' +
                ", previewUrl='" + previewUrl + '\'' +
                ", isPreview='" + isPreview + '\'' +
                ", extension='" + extension + '\'' +
                ", fullPath='" + fullPath + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
