package com.edu.www.dto;

import java.math.BigDecimal;
import java.util.Date;

public class StudentDTO extends BaseDTO {

    /**
     * 中文名
     */
    private String nameZh;
    
    /**
     * 英文名
     */
    private String nameEn;
    
    /**
     * 学生编号
     */
    private String studentCode;
    
    /**
     * 性别(0：女、1：男)
     */
    private Byte gender;
    
    /**
     * 出生日期(真实)
     */
    private Date birthDate;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 部门(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;
    
    /**
     * 班级ID
     */
    private String classId;
    
    /**
     * 入校时间
     */
    private Date admissionDate;
    
    /**
     * 证件类型
     */
    private String cardType;
    
    /**
     * 证件号
     */
    private String cardNum;
    
    /**
     * 证件中文名
     */
    private String cardNameZh;
    
    /**
     * 证件英文名
     */
    private String cardNameEn;
    
    /**
     * 手机号
     */
    private String phoneNum;
    
    /**
     * 是否缴费(0：否、1：是)
     */
    private String isPaid;
    
    /**
     * 状态(0:退学、1:旁听、2:在读)
     */
    private String status;
    
    /**
     * 是否参加考试(0：否、1：是)
     */
    private String isExam;
    
    /**
     * 是否为转入生(0：否、1：是)
     */
    private String isTransferred;
    
    /**
     * 转入时间
     */
    private Date transferInDate;
    
    /**
     * 毕业中学名称
     */
    private String middleSchoolName;
    
    /**
     * 是否住宿(0：否、1：是)
     */
    private String isBoarding;
    
    /**
     * 联系信息
     */
    private String contactInfo;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 家长邮箱
     */
    private String parentEmail;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 民族
     */
    private String ethnicity;
    
    /**
     * 身高
     */
    private BigDecimal height;
    
    /**
     * 体重
     */
    private BigDecimal weight;
    
    /**
     * 血型
     */
    private String bloodType;
    
    /**
     * 籍贯
     */
    private String birthPlace;
    
    /**
     * 衣服尺码
     */
    private String clothingSize;

    /**
     * 尺码情况
     */
    private String sizeSituation;

    /**
     * 出生地址
     */
    private String birthAddress;
    
    /**
     * 户籍地址
     */
    private String householdAddress;
    
    /**
     * 家庭住址
     */
    private String homeAddress;
    
    /**
     * 是否是独生子女(0：否、1：是)
     */
    private String isOnlyChild;
    
    /**
     * 情况
     */
    private String situation;

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getStudentCode() {
        return studentCode;
    }

    public void setStudentCode(String studentCode) {
        this.studentCode = studentCode;
    }

    public Byte getGender() {
        return gender;
    }

    public void setGender(Byte gender) {
        this.gender = gender;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public Date getAdmissionDate() {
        return admissionDate;
    }

    public void setAdmissionDate(Date admissionDate) {
        this.admissionDate = admissionDate;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public String getCardNameZh() {
        return cardNameZh;
    }

    public void setCardNameZh(String cardNameZh) {
        this.cardNameZh = cardNameZh;
    }

    public String getCardNameEn() {
        return cardNameEn;
    }

    public void setCardNameEn(String cardNameEn) {
        this.cardNameEn = cardNameEn;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getIsPaid() {
        return isPaid;
    }

    public void setIsPaid(String isPaid) {
        this.isPaid = isPaid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsExam() {
        return isExam;
    }

    public void setIsExam(String isExam) {
        this.isExam = isExam;
    }

    public String getIsTransferred() {
        return isTransferred;
    }

    public void setIsTransferred(String isTransferred) {
        this.isTransferred = isTransferred;
    }

    public Date getTransferInDate() {
        return transferInDate;
    }

    public void setTransferInDate(Date transferInDate) {
        this.transferInDate = transferInDate;
    }

    public String getMiddleSchoolName() {
        return middleSchoolName;
    }

    public void setMiddleSchoolName(String middleSchoolName) {
        this.middleSchoolName = middleSchoolName;
    }

    public String getIsBoarding() {
        return isBoarding;
    }

    public void setIsBoarding(String isBoarding) {
        this.isBoarding = isBoarding;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getParentEmail() {
        return parentEmail;
    }

    public void setParentEmail(String parentEmail) {
        this.parentEmail = parentEmail;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEthnicity() {
        return ethnicity;
    }

    public void setEthnicity(String ethnicity) {
        this.ethnicity = ethnicity;
    }

    public java.math.BigDecimal getHeight() {
        return height;
    }

    public void setHeight(java.math.BigDecimal height) {
        this.height = height;
    }

    public java.math.BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(java.math.BigDecimal weight) {
        this.weight = weight;
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getClothingSize() {
        return clothingSize;
    }

    public void setClothingSize(String clothingSize) {
        this.clothingSize = clothingSize;
    }

    public String getSizeSituation() {
        return sizeSituation;
    }

    public void setSizeSituation(String sizeSituation) {
        this.sizeSituation = sizeSituation;
    }

    public String getBirthAddress() {
        return birthAddress;
    }

    public void setBirthAddress(String birthAddress) {
        this.birthAddress = birthAddress;
    }

    public String getHouseholdAddress() {
        return householdAddress;
    }

    public void setHouseholdAddress(String householdAddress) {
        this.householdAddress = householdAddress;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getIsOnlyChild() {
        return isOnlyChild;
    }

    public void setIsOnlyChild(String isOnlyChild) {
        this.isOnlyChild = isOnlyChild;
    }

    public String getSituation() {
        return situation;
    }

    public void setSituation(String situation) {
        this.situation = situation;
    }
}
