package com.edu.www.dto;

import java.util.List;
import java.util.Map;

/**
 * 座位分组DTO
 * 返回格式：Map<String, List<StudentInfo>>
 * 其中key为分组名称（如"Math AA HL"），value为该分组的学生列表
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class SeatDTO {

    /**
     * 学生分组数据
     * key: 分组名称（如"Math AA HL", "Physics SL"等）
     * value: 该分组的学生列表
     */
    private Map<String, List<StudentInfo>> data;

    /**
     * 学生信息
     */
    public static class StudentInfo {
        /**
         * 中文名
         */
        private String nameZh;

        /**
         * 英文名
         */
        private String nameEn;

        public String getNameZh() {
            return nameZh;
        }

        public void setNameZh(String nameZh) {
            this.nameZh = nameZh;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }
    }

    // Getter and Setter for SeatDTO
    public Map<String, List<StudentInfo>> getData() {
        return data;
    }

    public void setData(Map<String, List<StudentInfo>> data) {
        this.data = data;
    }
}
