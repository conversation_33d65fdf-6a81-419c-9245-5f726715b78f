package com.edu.www.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础信息 DTO
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class BaseDTO implements Serializable {

    private static final long serialVersionUID = -8562955009288950L;
    /**
     * id
     */
    private String id;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 修改时间
     */
    private Date updatedAt;
    /**
     * 修改人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
