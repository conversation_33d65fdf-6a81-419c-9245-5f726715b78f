package com.edu.www.dto;

import java.util.Date;
import java.util.List;

/**
 * 考试信息数据传输对象
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class ExamDTO extends BaseDTO {

    /**
     * 年度
     */
    private String year;

    /**
     * 学期(S1：第一学期、S2：第二学期)
     */
    private String semester;

    /**
     * 部门编号(SY：双语部、IC：融合部、DP：高中部)
     */
    private String departmentCode;

    /**
     * 考试类型(MONTHLY: 月考、MID_TERM: 期中、MOCK: 模考、IB: IB大考、FINAL_TERM: 期末)
     */
    private String type;

    /**
     * 考试标题
     */
    private String title;

    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeCode;

    /**
     * 班级编号(A：A班、B：B班)
     */
    private String classCode;

    /**
     * 教室编号
     */
    private String classroomCode;

    /**
     * 考试组成信息
     */
    private String compositionInfo;

    /**
     * 天数
     */
    private Integer seqDay;

    /**
     * 考试日期
     */
    private Date examDate;

    /**
     * 星期(0：周日、1：周一、2：周二、3：周三、4：周四、5：周五、6：周六)
     */
    private String weekday;

    /**
     * 监考人
     */
    private String invigilator;

    /**
     * 巡考人（多个巡考人）
     */
    private List<String> inspectors;

    /**
     * 巡考人（存储用，以逗号分隔）
     */
    private String inspector;

    /**
     * 考生人数
     */
    private Integer examCandidate;

    /**
     * 总时长
     */
    private Integer totalDuration;

    /**
     * 状态(0：禁用、1：启用)
     */
    private String status;

    /**
     * 备注
     */
    private String description;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGradeCode() {
        return gradeCode;
    }

    public void setGradeCode(String gradeCode) {
        this.gradeCode = gradeCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getClassroomCode() {
        return classroomCode;
    }

    public void setClassroomCode(String classroomCode) {
        this.classroomCode = classroomCode;
    }

    public String getCompositionInfo() {
        return compositionInfo;
    }

    public void setCompositionInfo(String compositionInfo) {
        this.compositionInfo = compositionInfo;
    }

    public Integer getSeqDay() {
        return seqDay;
    }

    public void setSeqDay(Integer seqDay) {
        this.seqDay = seqDay;
    }

    public Date getExamDate() {
        return examDate;
    }

    public void setExamDate(Date examDate) {
        this.examDate = examDate;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public String getInvigilator() {
        return invigilator;
    }

    public void setInvigilator(String invigilator) {
        this.invigilator = invigilator;
    }

    public List<String> getInspectors() {
        return inspectors;
    }

    public void setInspectors(List<String> inspectors) {
        this.inspectors = inspectors;
    }

    public String getInspector() {
        return inspector;
    }

    public void setInspector(String inspector) {
        this.inspector = inspector;
    }

    public Integer getExamCandidate() {
        return examCandidate;
    }

    public void setExamCandidate(Integer examCandidate) {
        this.examCandidate = examCandidate;
    }

    public Integer getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Integer totalDuration) {
        this.totalDuration = totalDuration;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}