package com.edu.www.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 书籍信息数据传输对象
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
public class BookDTO extends BaseDTO {

    /**
     * 书名
     */
    private String bookName;

    /**
     * 作者
     */
    private String author;

    /**
     * ISBN编号
     */
    private String isbn;

    /**
     * 出版社
     */
    private String publisher;

    /**
     * 出版日期
     */
    private Date publicationDate;

    /**
     * 书籍分类(1:中文教材、2:英文教材)
     */
    private String bookCategory;

    /**
     * 学科编号
     */
    private String subjectCode;

    /**
     * 年级编号(6：6年级、7：7年级、8：8年级、9：9年级、spring：春季班、10：10年级、11：11年级、12：12年级)
     */
    private String gradeLevel;

    /**
     * 年级编号列表（多个年级）
     */
    private List<String> gradeLevels;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 当前库存
     */
    private Integer currentStock;

    /**
     * 总入库数
     */
    private Integer totalInStock;

    /**
     * 总出库数
     */
    private Integer totalOutStock;

    /**
     * 最低库存预警
     */
    private Integer minStockAlert;

    /**
     * 最高库存限制
     */
    private Integer maxStockLimit;

    /**
     * 存放位置
     */
    private String storageLocation;

    /**
     * 书籍状况(1：全新、2：良好、3：一般、4：损坏)
     */
    private String bookCondition;

    /**
     * 封面图片
     */
    private String frontCoverImage;

    /**
     * 封面图片原始文件名
     */
    private String frontCoverFileName;

    /**
     * 封底图片
     */
    private String backCoverImage;

    /**
     * 封底图片原始文件名
     */
    private String backCoverFileName;

    /**
     * 扫描入库日期
     */
    private Date scanDate;

    /**
     * 扫描人
     */
    private String scannedBy;

    /**
     * 书籍状态(0：下架、1：正常、2：损坏、3：遗失)
     */
    private String bookStatus;

    /**
     * 备注描述
     */
    private String description;

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public Date getPublicationDate() {
        return publicationDate;
    }

    public void setPublicationDate(Date publicationDate) {
        this.publicationDate = publicationDate;
    }

    public String getBookCategory() {
        return bookCategory;
    }

    public void setBookCategory(String bookCategory) {
        this.bookCategory = bookCategory;
    }

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public List<String> getGradeLevels() {
        return gradeLevels;
    }

    public void setGradeLevels(List<String> gradeLevels) {
        this.gradeLevels = gradeLevels;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(Integer currentStock) {
        this.currentStock = currentStock;
    }

    public Integer getTotalInStock() {
        return totalInStock;
    }

    public void setTotalInStock(Integer totalInStock) {
        this.totalInStock = totalInStock;
    }

    public Integer getTotalOutStock() {
        return totalOutStock;
    }

    public void setTotalOutStock(Integer totalOutStock) {
        this.totalOutStock = totalOutStock;
    }

    public Integer getMinStockAlert() {
        return minStockAlert;
    }

    public void setMinStockAlert(Integer minStockAlert) {
        this.minStockAlert = minStockAlert;
    }

    public Integer getMaxStockLimit() {
        return maxStockLimit;
    }

    public void setMaxStockLimit(Integer maxStockLimit) {
        this.maxStockLimit = maxStockLimit;
    }

    public String getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(String storageLocation) {
        this.storageLocation = storageLocation;
    }

    public String getBookCondition() {
        return bookCondition;
    }

    public void setBookCondition(String bookCondition) {
        this.bookCondition = bookCondition;
    }

    public String getFrontCoverImage() {
        return frontCoverImage;
    }

    public void setFrontCoverImage(String frontCoverImage) {
        this.frontCoverImage = frontCoverImage;
    }

    public String getFrontCoverFileName() {
        return frontCoverFileName;
    }

    public void setFrontCoverFileName(String frontCoverFileName) {
        this.frontCoverFileName = frontCoverFileName;
    }

    public String getBackCoverImage() {
        return backCoverImage;
    }

    public void setBackCoverImage(String backCoverImage) {
        this.backCoverImage = backCoverImage;
    }

    public String getBackCoverFileName() {
        return backCoverFileName;
    }

    public void setBackCoverFileName(String backCoverFileName) {
        this.backCoverFileName = backCoverFileName;
    }

    public Date getScanDate() {
        return scanDate;
    }

    public void setScanDate(Date scanDate) {
        this.scanDate = scanDate;
    }

    public String getScannedBy() {
        return scannedBy;
    }

    public void setScannedBy(String scannedBy) {
        this.scannedBy = scannedBy;
    }

    public String getBookStatus() {
        return bookStatus;
    }

    public void setBookStatus(String bookStatus) {
        this.bookStatus = bookStatus;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
