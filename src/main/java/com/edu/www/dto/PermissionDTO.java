package com.edu.www.dto;

/**
 * 权限数据传输对象
 *
 * <AUTHOR>
 * @date 2024/09/11
 */
public class PermissionDTO extends BaseDTO {

    /**
     * 菜单ID
     */
    private String menuId;

    /**
     * 菜单父级ID
     */
    private String menuParentId;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限编码
     */
    private String code;

    /**
     * 权限类型(1: 菜单权限, 2: 按钮权限)
     */
    private String type;

    /**
     * 权限URL
     */
    private String url;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 状态(0：禁用、1：启用)
     */
    private String status;

    /**
     * 权限描述
     */
    private String description;

    // Getters and Setters
    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getMenuParentId() {
        return menuParentId;
    }

    public void setMenuParentId(String menuParentId) {
        this.menuParentId = menuParentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
} 