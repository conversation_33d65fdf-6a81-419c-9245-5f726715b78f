package com.edu.www.task;

import com.edu.www.dto.TmpScheduleDTO;
import com.edu.www.mapper.TmpScheduleMapper;
import com.edu.www.service.CommonService;
import com.edu.www.utils.CommonUtil;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.TmpScheduleVO;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 临时课表定时任务
 *
 * <AUTHOR>
 * @date 2024/09/10
 */
@Component
public class TmpScheduleTask {
    private static final Logger logger = LoggerFactory.getLogger(InviteCodeTask.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private TmpScheduleMapper tmpScheduleMapper;

    /**
     * 每周六晚上23:59执行删除过期的数据
     */
    @Scheduled(cron = "0 59 23 ? * 6")
    public void deleteExpiredTmpSchedule() {
        TmpScheduleDTO tmpScheduleDTO = new TmpScheduleDTO();
        tmpScheduleDTO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        tmpScheduleDTO.setSemester(CommonUtil.getCurrentSemester());

        List<TmpScheduleVO> tmpScheduleVOList = tmpScheduleMapper.query(tmpScheduleDTO);
        if (CollectionUtils.isEmpty(tmpScheduleVOList)) {
            return;
        }

        for (TmpScheduleVO tmpScheduleVO : tmpScheduleVOList) {
            Date endDate = tmpScheduleVO.getEndDate();
            if (DateUtil.isDateExpired(endDate)) {
                tmpScheduleMapper.delete(tmpScheduleVO.getId());
            }
        }
    }
}
