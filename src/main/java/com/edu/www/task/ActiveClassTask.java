package com.edu.www.task;

import com.alibaba.fastjson.JSON;
import com.edu.www.common.WebSocketEntity;
import com.edu.www.constants.CommonConstant;
import com.edu.www.convert.ScheduleConverter;
import com.edu.www.dto.ScheduleDTO;
import com.edu.www.dto.TmpScheduleDTO;
import com.edu.www.dto.UserDTO;
import com.edu.www.enums.EduDeptCodeEnum;
import com.edu.www.enums.EduGradeCodeEnum;
import com.edu.www.enums.EduWeekSeqEnum;
import com.edu.www.mapper.ScheduleMapper;
import com.edu.www.mapper.TmpScheduleMapper;
import com.edu.www.service.CommonService;
import com.edu.www.utils.CommonUtil;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.ActiveClassVO;
import com.edu.www.vo.ScheduleVO;
import com.edu.www.vo.TmpScheduleVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalTime;
import java.util.*;

/**
 * 实时课程状态定时任务
 *
 * <AUTHOR>
 * @date 2024/09/10
 */
@Component
public class ActiveClassTask {
    private static final Logger logger = LoggerFactory.getLogger(ActiveClassTask.class);
    @Autowired
    private CommonService commonService;

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private TmpScheduleMapper tmpScheduleMapper;

    /**
     * 查询当前时间(星期)临时课表信息
     *
     * @param deptCode
     * @param gradeCode
     * @param classCode
     * @return
     */
    private List<TmpScheduleVO> getTmpScheduleVOList(String deptCode, String gradeCode, String classCode) {
        // todo 测试
        // String currentDayWeekName = "monday";
        // 例如: saturday
        String currentDayWeekName = DateUtil.getCurrentDayWeekName();
        TmpScheduleDTO tmpScheduleDTO = new TmpScheduleDTO();
        tmpScheduleDTO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        tmpScheduleDTO.setSemester(CommonUtil.getCurrentSemester());
        tmpScheduleDTO.setDepartmentCode(deptCode);
        tmpScheduleDTO.setGradeCode(gradeCode);
        tmpScheduleDTO.setClassCode(classCode);
        tmpScheduleDTO.setStartDate(DateUtil.getStartDateByWeekSeq(EduWeekSeqEnum.THIS_WEEK.getKey()));
        tmpScheduleDTO.setEndDate(DateUtil.getEndDateByWeekSeq(EduWeekSeqEnum.THIS_WEEK.getKey()));
        List<TmpScheduleVO> tmpScheduleVOList = tmpScheduleMapper.query(tmpScheduleDTO);

        List<TmpScheduleVO> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tmpScheduleVOList)) {
            for (TmpScheduleVO tmpScheduleVO : tmpScheduleVOList) {
                String position = tmpScheduleVO.getPosition();
                if (StringUtils.isNotBlank(position) && position.startsWith(currentDayWeekName)) {
                    resultList.add(tmpScheduleVO);
                }
            }
        }
        return resultList;
    }

    /**
     * 查询当前时间(星期)课表信息
     *
     * @param deptCode
     * @param gradeCode
     * @param classCode
     * @return
     */
    private List<ScheduleVO> getScheduleVOList(String deptCode, String gradeCode, String classCode) {
        Map<String, String> weekMap = CommonConstant.weekMap;
        Map<String, String> teacherNameMap = commonService.getTeacherName();
        ScheduleDTO scheduleDTO = new ScheduleDTO();
        scheduleDTO.setYear(commonService.getAcademicYearList().get(NumberUtils.INTEGER_ZERO));
        scheduleDTO.setSemester(CommonUtil.getCurrentSemester());
        scheduleDTO.setDepartmentCode(deptCode);
        scheduleDTO.setGradeCode(gradeCode);
        scheduleDTO.setClassCode(classCode);
        scheduleDTO.setWeekday(weekMap.get(DateUtil.getCurrentDayWeekName()));
        // scheduleDTO.setWeekday("1"); // todo 测试
        List<ScheduleVO> scheduleVOList = scheduleMapper.query(scheduleDTO);
        scheduleVOList.forEach(e -> {
            e.setTeacherName(teacherNameMap.get(e.getTeacherId()));
        });
        return scheduleVOList;
    }

    /**
     * 获取融合实时课程信息
     *
     * @return
     */
    public List<ActiveClassVO> getMYPActiveClassVOList() {
        List<ActiveClassVO> activeClassList = new ArrayList<>();
        // 春季班
        List<TmpScheduleVO> springTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.IC.getKey(), EduGradeCodeEnum.GRADE_6.getKey(), null);
        if (!CollectionUtils.isEmpty(springTmpScheduleList)) {
            List<ActiveClassVO> activeClassVOList = ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(springTmpScheduleList);
            activeClassList.addAll(activeClassVOList);
        } else {
            List<ScheduleVO> scheduleVOList = getScheduleVOList(EduDeptCodeEnum.IC.getKey(), EduGradeCodeEnum.GRADE_6.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(scheduleVOList));
        }

        // 10年级
        List<TmpScheduleVO> yearTenTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.IC.getKey(), EduGradeCodeEnum.GRADE_7.getKey(), null);
        if (!CollectionUtils.isEmpty(yearTenTmpScheduleList)) {
            List<ActiveClassVO> activeClassVOList = ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(yearTenTmpScheduleList);
            activeClassList.addAll(activeClassVOList);
        } else {
            List<ScheduleVO> yearTenScheduleVOList = getScheduleVOList(EduDeptCodeEnum.IC.getKey(), EduGradeCodeEnum.GRADE_7.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(yearTenScheduleVOList));
        }

        // 11年级
        List<TmpScheduleVO> yearElevenTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.IC.getKey(), EduGradeCodeEnum.GRADE_8.getKey(), null);
        if (!CollectionUtils.isEmpty(yearElevenTmpScheduleList)) {
            activeClassList.addAll(ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(yearElevenTmpScheduleList));
        } else {
            List<ScheduleVO> yearElevenScheduleVOList = getScheduleVOList(EduDeptCodeEnum.IC.getKey(), EduGradeCodeEnum.GRADE_8.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(yearElevenScheduleVOList));
        }

        // 12年级
        List<TmpScheduleVO> yearTwelveTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_9.getKey(), null);
        if (!CollectionUtils.isEmpty(yearTwelveTmpScheduleList)) {
            activeClassList.addAll(ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(yearTwelveTmpScheduleList));
        } else {
            List<ScheduleVO> yearTwelveScheduleVOList = getScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_9.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(yearTwelveScheduleVOList));
        }
        return activeClassList;

    }

    /**
     * 获取高中实时课程信息
     *
     * @return
     */
    public List<ActiveClassVO> getDPActiveClassVOList() {
        List<ActiveClassVO> activeClassList = new ArrayList<>();
        // 春季班
        List<TmpScheduleVO> springTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.SPRING.getKey(), null);
        if (!CollectionUtils.isEmpty(springTmpScheduleList)) {
            List<ActiveClassVO> activeClassVOList = ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(springTmpScheduleList);
            activeClassList.addAll(activeClassVOList);
        } else {
            List<ScheduleVO> scheduleVOList = getScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.SPRING.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(scheduleVOList));
        }

        // 10年级
        List<TmpScheduleVO> yearTenTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_10.getKey(), null);
        if (!CollectionUtils.isEmpty(yearTenTmpScheduleList)) {
            List<ActiveClassVO> activeClassVOList = ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(yearTenTmpScheduleList);
            activeClassList.addAll(activeClassVOList);
        } else {
            List<ScheduleVO> yearTenScheduleVOList = getScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_10.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(yearTenScheduleVOList));
        }

        // 11年级
        List<TmpScheduleVO> yearElevenTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_11.getKey(), null);
        if (!CollectionUtils.isEmpty(yearElevenTmpScheduleList)) {
            activeClassList.addAll(ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(yearElevenTmpScheduleList));
        } else {
            List<ScheduleVO> yearElevenScheduleVOList = getScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_11.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(yearElevenScheduleVOList));
        }

        // 12年级
        List<TmpScheduleVO> yearTwelveTmpScheduleList = getTmpScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_12.getKey(), null);
        if (!CollectionUtils.isEmpty(yearTwelveTmpScheduleList)) {
            activeClassList.addAll(ScheduleConverter.tmpScheduleVOList2ActiveClassVOList(yearTwelveTmpScheduleList));
        } else {
            List<ScheduleVO> yearTwelveScheduleVOList = getScheduleVOList(EduDeptCodeEnum.DP.getKey(), EduGradeCodeEnum.GRADE_12.getKey(), null);
            activeClassList.addAll(ScheduleConverter.scheduleVOList2ActiveClassVOList(yearTwelveScheduleVOList));
        }
        return activeClassList;

    }


    /**
     * 每 1 秒执行一次，获取当前正在进行的课程和即将开始的课程
     */
    @Scheduled(fixedRate = 1000)
    public void getActiveClass() {
        // 获取当前时间
        // LocalTime now = DateUtil.getLocalTimeFromString("09:58"); // todo
        LocalTime now = LocalTime.now();

        // 检查WebSocket连接和用户信息
        Map<String, UserDTO> connectedUsers = WebSocketEntity.getAllConnectedUsers();
        if (connectedUsers.isEmpty()) {
            // 如果没有连接的用户，则不需要推送
            return;
        }

        // 获取高中实时课程信息
        List<ActiveClassVO> dpActiveClassVOList = getDPActiveClassVOList();
        // 获取融合实时课程信息
        List<ActiveClassVO> mypActiveClassVOList = getMYPActiveClassVOList();

        // 高中部门课程处理
        processDeptCourses(EduDeptCodeEnum.DP.getKey(), dpActiveClassVOList, now);

        // 融合部门课程处理
        processDeptCourses(EduDeptCodeEnum.IC.getKey(), mypActiveClassVOList, now);
    }

    /**
     * 处理部门课程并推送给相应部门的用户
     *
     * @param deptCode   部门代码
     * @param courseList 课程列表
     * @param now        当前时间
     */
    private void processDeptCourses(String deptCode, List<ActiveClassVO> courseList, LocalTime now) {
        if (courseList.isEmpty()) {
            return;
        }

        // 分离当前正在进行的课程和即将开始的课程
        List<ActiveClassVO> activeClassVOList = new ArrayList<>();
        List<ActiveClassVO> upcomingClassVOList = new ArrayList<>();

        for (ActiveClassVO vo : courseList) {
            if (vo.getStartTime() != null && vo.getEndTime() != null) {
                LocalTime startTime = vo.getStartTime().toLocalTime();
                LocalTime endTime = vo.getEndTime().toLocalTime();

                // 如果当前时间在课程开始和结束时间之间，则为当前正在进行的课程
                if (!now.isBefore(startTime) && now.isBefore(endTime)) {
                    // 计算剩余分钟数
                    long remainingMinutes = Duration.between(now, endTime).toMinutes();
                    vo.setRemainingMinutes((int) (remainingMinutes > 0 ? remainingMinutes : 0));
                    activeClassVOList.add(vo);
                }
                // 如果课程还未开始，且开始时间在当前时间后的3分钟内，则为即将开始的课程
                else if (now.isBefore(startTime)) {
                    Duration duration = Duration.between(now, startTime);
                    if (duration.getSeconds() <= 180) { // 3分钟内
                        vo.setCountdown((int) duration.getSeconds());
                        upcomingClassVOList.add(vo);
                    }
                }
            }
        }

        // 按剩余时间降序排序（剩余时间最多的排在最前面）
        if (!activeClassVOList.isEmpty()) {
            activeClassVOList.sort(Comparator.comparing(ActiveClassVO::getRemainingMinutes).reversed());
        }

        // 按倒计时升序排序（即将开始的排在最前面）
        if (!upcomingClassVOList.isEmpty()) {
            upcomingClassVOList.sort(Comparator.comparing(ActiveClassVO::getCountdown));
        }

        // 获取部门名称
        String deptName = EduDeptCodeEnum.getByKey(deptCode).getDesc();

        // 通过 WebSocket 向特定部门的用户推送课程状态
        WebSocketEntity.sendClassStatusByDept(deptCode,
                deptName,
                JSON.toJSONString(activeClassVOList),
                JSON.toJSONString(upcomingClassVOList));
    }
}
