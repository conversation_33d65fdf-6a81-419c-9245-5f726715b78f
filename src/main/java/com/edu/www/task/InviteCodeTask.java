package com.edu.www.task;

import com.edu.www.dto.InviteCodeDTO;
import com.edu.www.enums.EduYesOrNoEnum;
import com.edu.www.mapper.InviteCodeMapper;
import com.edu.www.utils.DateUtil;
import com.edu.www.vo.InviteCodeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邀请码定时任务
 *
 * <AUTHOR>
 * @date 2024/09/10
 */
@Component
public class InviteCodeTask {

    private static final Logger logger = LoggerFactory.getLogger(InviteCodeTask.class);

    @Autowired
    private InviteCodeMapper inviteCodeMapper;

    /**
     * 每周日晚上11:59执行
     * 删除过期未使用的邀请码
     * cron表达式：秒 分 时 日 月 周(0和7都表示周日)
     */
    @Scheduled(cron = "0 59 23 ? * 0")
    public void deleteExpiredInviteCode() {
        logger.info("开始执行过期邀请码删除任务");
        try {
            InviteCodeDTO inviteCodeDTO = new InviteCodeDTO();
            inviteCodeDTO.setIsUsed(EduYesOrNoEnum.NO.getKey());
            List<InviteCodeVO> inviteCodeVOList = inviteCodeMapper.query(inviteCodeDTO);
            if (CollectionUtils.isEmpty(inviteCodeVOList)) {
                return;
            }

            logger.info("找到{}个未使用的邀请码，开始检查过期状态", inviteCodeVOList.size());

            // 使用 Stream 过滤过期的数据
            List<InviteCodeVO> expiredCodes = inviteCodeVOList.stream()
                    .filter(e -> DateUtil.isDateExpired(e.getExpiryTime())).collect(Collectors.toList());
            List<String> idsList = expiredCodes.stream().map(InviteCodeVO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(expiredCodes)) {
                return;
            }

            inviteCodeMapper.deleteByIds(idsList);
            logger.info("找到{}个过期未使用的邀请码，准备删除", expiredCodes.size());
        } catch (Exception e) {
            logger.error("过期邀请码状态删除失败: {}", e.getMessage(), e);
        }
    }
} 