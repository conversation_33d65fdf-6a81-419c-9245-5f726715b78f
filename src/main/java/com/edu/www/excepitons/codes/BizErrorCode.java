package com.edu.www.excepitons.codes;

/**
 * 业务错误编码
 *
 * <AUTHOR>
 * @date 2024/09/07
 */
public enum BizErrorCode implements ErrorCode {
    /**
     * 这种情况一般是捕获了没有单独进行catch处理的异常然后设定的错误码
     */
    UNEXPECTED_ERROR("UNEXPECTED_ERROR", "非预期的系统错误"),

    /**
     * 参数为空（含null）是非法的
     */
    BLANK_IS_ILLEGAL_PARAM("BLANK_IS_ILLEGAL_PARAM", "参数为空（含null）是非法的"),

    /**
     * 重复请求不一致
     */
    REPEAT_REQ_INCONSISTENT("REPEAT_REQ_INCONSISTENT", "重复请求不一致"),
    ;


    /**
     * 处理结果码
     */
    private final String code;

    /**
     * 处理结果描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code
     * @param desc
     */
    BizErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
