package com.edu.www.excepitons;

import com.edu.www.excepitons.codes.ErrorCode;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * @date 2024/09/07
 */
public class EduException extends Exception {

    public EduException(ErrorCode errorCode, String extraMsg, Throwable cause) {
        super(errorCode, extraMsg, cause);
    }

    public EduException(ErrorCode errorCode) {
        this(errorCode, errorCode.getDesc(), null);
    }

    public EduException(ErrorCode errorCode, String extraMsg) {
        this(errorCode, extraMsg, null);
    }

    public EduException(ErrorCode errorCode, Throwable cause) {
        this(errorCode, errorCode.getDesc(), cause);
    }

    public EduException(String extraMsg, Throwable cause) {
        super(extraMsg, cause);
    }
}
