package com.edu.www.excepitons;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @date 2025/03/13
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理业务异常
     */
    @ExceptionHandler(EduException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> handleBizException(HttpServletRequest request, EduException exception) {
        logger.error("全局捕获业务异常: msg={}", exception.getMessage(), exception);

        Map<String, Object> response = new HashMap<>();
        response.put("errorCode", exception.getErrorCode());
        response.put("msg", exception.getMessage());
        response.put("success", Boolean.FALSE);
        response.put("data", null);
        return response;
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Map<String, Object> handleException(HttpServletRequest request, Exception exception) {
        logger.error("全局捕获系统异常: msg={}", exception.getMessage(), exception);

        Map<String, Object> response = new HashMap<>();
        response.put("errorCode", 500);
        response.put("msg", "系统异常，请联系管理员");
        response.put("success", Boolean.FALSE);
        response.put("data", null);
        return response;
    }
}