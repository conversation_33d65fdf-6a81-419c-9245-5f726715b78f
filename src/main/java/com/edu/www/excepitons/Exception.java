package com.edu.www.excepitons;

import com.edu.www.excepitons.codes.ErrorCode;


/**
 * 异常类
 *
 * <AUTHOR>
 * @date 2024/09/07
 */
public class Exception extends RuntimeException {

    /**
     * 错误编码
     */
    private ErrorCode errorCode;

    /**
     * 除了错误码本身描述的提示信息外，额外补充的信息
     */
    private String extraMsg;

    public Exception(ErrorCode errorCode, String extraMsg, Throwable cause) {
        this.errorCode = errorCode;
        this.extraMsg = extraMsg;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    public String getExtraMsg() {
        return extraMsg;
    }


    public Exception(String message) {
        super(message);
    }

    public Exception() {
        super();
    }

    public Exception(String message, Throwable cause) {
        super(message, cause);
    }

    public Exception(Throwable cause) {
        super(cause);
    }

    protected Exception(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
