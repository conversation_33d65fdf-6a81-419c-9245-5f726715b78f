package com.edu.www.client.managebac;

import com.edu.www.constants.Constant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * MB学生信息分页查询接口
 *
 * <AUTHOR>
 * @date 2024/09/06
 */
public class MBYearGroupsClient {
    private static final Logger logger = LoggerFactory.getLogger(MBYearGroupsClient.class);

    /**
     * 调用 ManageBac API 获取年份组别。
     *
     * @param authToken 您的 ManageBac API 认证令牌。
     * @return 包含 API 响应体的 Optional<String>。如果请求失败（例如，网络问题、非 2xx 状态码），则返回 Optional.empty()。
     * @throws IOException          如果发生 I/O 错误。
     * @throws InterruptedException 如果操作被中断。
     */
    public Optional<String> getYearGroups(String authToken) throws IOException, InterruptedException {
        // 构建 HttpClient 实例
        HttpClient client = HttpClient.newHttpClient();

        // 构建 HttpRequest
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(Constant.BASE_URL + "/year-groups"))
                .header(Constant.AUTH_TOKEN, authToken) // 添加认证令牌到请求头
                .GET() // 指定为 GET 请求
                .build();

        try {
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != HttpStatus.OK.value()) {
                // 请求失败，打印状态码和响应体（如果需要调试）
                logger.error("API call failed with status code: {}", response.statusCode());
                logger.error("Response body:{} ", response.body());
                return Optional.empty();
            }
            // 请求成功，返回响应体
            return Optional.of(response.body());
        } catch (IOException | InterruptedException e) {
            // 捕获网络或其他 I/O 异常
            logger.error("Error while making API call: {}", e.getMessage());
            throw e; // 重新抛出异常，让调用者处理
        }
    }

    /**
     * 获取年级组列表（带查询参数）
     *
     * @param authToken   您的 ManageBac API 认证令牌
     * @param queryParams 查询参数Map
     * @return 包含 API 响应体的 Optional<String>。如果请求失败，则返回 Optional.empty()
     * @throws IOException          如果发生 I/O 错误
     * @throws InterruptedException 如果操作被中断
     */
    public Optional<String> getYearGroupsWithParams(String authToken, Map<String, String> queryParams)
            throws IOException, InterruptedException {

        // 构建URL
        String url = Constant.BASE_URL + "/year-groups";
        if (queryParams != null && !queryParams.isEmpty()) {
            String queryString = queryParams.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));
            url += "?" + queryString;
        }

        logger.info("请求ManageBac年级组API: {}", url);

        // 构建 HttpClient 实例
        HttpClient client = HttpClient.newHttpClient();

        // 构建 HttpRequest
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header(Constant.AUTH_TOKEN, authToken) // 添加认证令牌到请求头
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .GET() // 指定为 GET 请求
                .build();

        try {
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            logger.info("ManageBac年级组API响应状态码: {}", response.statusCode());

            if (response.statusCode() != HttpStatus.OK.value()) {
                // 请求失败，记录错误信息
                logger.error("ManageBac年级组API调用失败，状态码: {}", response.statusCode());
                logger.error("响应体: {}", response.body());
                return Optional.empty();
            }

            // 请求成功，返回响应体
            logger.debug("年级组API响应成功，响应体长度: {}", response.body().length());
            return Optional.of(response.body());

        } catch (IOException | InterruptedException e) {
            // 捕获网络或其他 I/O 异常
            logger.error("调用ManageBac年级组API时发生错误: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让调用者处理
        }
    }
}
