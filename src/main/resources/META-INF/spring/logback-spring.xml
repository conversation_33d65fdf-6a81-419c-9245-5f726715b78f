<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 开发环境和生产环境使用不同的日志路径 -->
    <springProfile name="dev">
        <property name="LOG_PATH" value="./logs" />
    </springProfile>
    <springProfile name="prod">
        <property name="LOG_PATH" value="/opt/product/edu/logs" />
    </springProfile>
    
    <property name="LOG_FILE" value="${LOG_PATH}/application.log" />

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <prudent>true</prudent>
        <file>${LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/application.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 开发环境下使用DEBUG级别 -->
    <springProfile name="dev">
        <logger name="com.example.edu" level="DEBUG" />
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>

    <!-- 生产环境下使用INFO级别 -->
    <springProfile name="prod">
        <logger name="com.example.edu" level="INFO" />
        <root level="INFO">
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
</configuration>