<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.SubjectMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.SubjectVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName"/>
        <result column="GROUP_CODE" jdbcType="VARCHAR" property="groupCode"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="LEVEL" jdbcType="VARCHAR" property="level"/>
        <result column="SUB_SUBJECT_NAME" jdbcType="VARCHAR" property="subSubjectName"/>
        <result column="SUBJECT_TEACHER_ID" jdbcType="VARCHAR" property="subjectTeacherId"/>
        <result column="TEACHING_CLASS_ID" jdbcType="VARCHAR" property="teachingClassId"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, GROUP_NAME, GROUP_CODE, NAME, CODE, LEVEL, SUB_SUBJECT_NAME, SUBJECT_TEACHER_ID, TEACHING_CLASS_ID,
        DEPARTMENT, DEPARTMENT_CODE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--查询学科信息-->
    <select id="query" parameterType="com.edu.www.dto.SubjectDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_subject t
        <where>
            <if test="groupName != null and groupName != ''">
                and t.GROUP_NAME like CONCAT('%', #{groupName}, '%')
            </if>
            <if test="groupCode != null and groupCode != ''">
                and t.GROUP_CODE = #{groupCode}
            </if>
            <if test="name != null and name != ''">
                and t.NAME like CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                and t.CODE = #{code}
            </if>
            <if test="level != null and level != ''">
                and t.LEVEL = #{level}
            </if>
            <if test="subSubjectName != null and subSubjectName != ''">
                and t.SUB_SUBJECT_NAME like CONCAT('%', #{subSubjectName}, '%')
            </if>
            <if test="subjectTeacherId != null and subjectTeacherId != ''">
                and t.SUBJECT_TEACHER_ID = #{subjectTeacherId}
            </if>
            <if test="teachingClassId != null and teachingClassId != ''">
                and t.TEACHING_CLASS_ID like CONCAT('%', #{teachingClassId}, '%')
            </if>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.updated_at desc
    </select>

    <!--新增学科信息-->
    <insert id="insert" parameterType="com.edu.www.dto.SubjectDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_subject_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_subject (
            ID, GROUP_NAME, GROUP_CODE, NAME, CODE, LEVEL, SUB_SUBJECT_NAME, SUBJECT_TEACHER_ID, TEACHING_CLASS_ID,
            DEPARTMENT, DEPARTMENT_CODE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
            #{id}, #{groupName}, #{groupCode}, #{name}, #{code}, #{level}, #{subSubjectName}, #{subjectTeacherId}, #{teachingClassId},
            #{department}, #{departmentCode}, #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--根据ID查询学科信息-->
    <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_subject t
        WHERE t.ID = #{id}
    </select>

    <!--更新学科信息-->
    <update id="update" parameterType="com.edu.www.dto.SubjectDTO">
        UPDATE t_edu_subject t
        <set>
            <if test="groupName != null and groupName != ''">
                t.GROUP_NAME = #{groupName},
            </if>
            <if test="groupCode != null and groupCode != ''">
                t.GROUP_CODE = #{groupCode},
            </if>
            <if test="name != null and name != ''">
                t.NAME = #{name},
            </if>
            <if test="code != null and code != ''">
                t.CODE = #{code},
            </if>
            <if test="level != null">
                t.LEVEL = #{level},
            </if>
            <if test="subSubjectName != null">
                t.SUB_SUBJECT_NAME = #{subSubjectName},
            </if>
            <if test="subjectTeacherId != null and subjectTeacherId != ''">
                t.SUBJECT_TEACHER_ID = #{subjectTeacherId},
            </if>
            <if test="teachingClassId != null">
                t.TEACHING_CLASS_ID = #{teachingClassId},
            </if>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!--根据ID删除学科信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_subject t
        WHERE t.ID = #{id}
    </delete>
</mapper> 