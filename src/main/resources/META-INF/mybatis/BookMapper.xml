<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.BookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.BookVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BOOK_NAME" jdbcType="VARCHAR" property="bookName"/>
        <result column="AUTHOR" jdbcType="VARCHAR" property="author"/>
        <result column="ISBN" jdbcType="VARCHAR" property="isbn"/>
        <result column="PUBLISHER" jdbcType="VARCHAR" property="publisher"/>
        <result column="PUBLICATION_DATE" jdbcType="DATE" property="publicationDate"/>
        <result column="BOOK_CATEGORY" jdbcType="VARCHAR" property="bookCategory"/>
        <result column="SUBJECT_CODE" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="GRADE_LEVEL" jdbcType="VARCHAR" property="gradeLevel"/>
        <result column="PRICE" jdbcType="DECIMAL" property="price"/>
        <result column="PURCHASE_PRICE" jdbcType="DECIMAL" property="purchasePrice"/>
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId"/>
        <result column="CURRENT_STOCK" jdbcType="INTEGER" property="currentStock"/>
        <result column="TOTAL_IN_STOCK" jdbcType="INTEGER" property="totalInStock"/>
        <result column="TOTAL_OUT_STOCK" jdbcType="INTEGER" property="totalOutStock"/>
        <result column="MIN_STOCK_ALERT" jdbcType="INTEGER" property="minStockAlert"/>
        <result column="MAX_STOCK_LIMIT" jdbcType="INTEGER" property="maxStockLimit"/>
        <result column="storage_location" jdbcType="VARCHAR" property="storageLocation"/>
        <result column="BOOK_CONDITION" jdbcType="VARCHAR" property="bookCondition"/>
        <result column="FRONT_COVER_IMAGE" jdbcType="VARCHAR" property="frontCoverImage"/>
        <result column="FRONT_COVER_FILE_NAME" jdbcType="VARCHAR" property="frontCoverFileName"/>
        <result column="BACK_COVER_IMAGE" jdbcType="VARCHAR" property="backCoverImage"/>
        <result column="BACK_COVER_FILE_NAME" jdbcType="VARCHAR" property="backCoverFileName"/>
        <result column="SCAN_DATE" jdbcType="DATE" property="scanDate"/>
        <result column="SCANNED_BY" jdbcType="VARCHAR" property="scannedBy"/>
        <result column="BOOK_STATUS" jdbcType="VARCHAR" property="bookStatus"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , BOOK_NAME, AUTHOR, ISBN, PUBLISHER, PUBLICATION_DATE, BOOK_CATEGORY,
        SUBJECT_CODE, GRADE_LEVEL, PRICE, PURCHASE_PRICE, SUPPLIER_ID, CURRENT_STOCK,
        TOTAL_IN_STOCK, TOTAL_OUT_STOCK, MIN_STOCK_ALERT, MAX_STOCK_LIMIT, storage_location,
        BOOK_CONDITION, FRONT_COVER_IMAGE, FRONT_COVER_FILE_NAME, BACK_COVER_IMAGE,
        BACK_COVER_FILE_NAME, SCAN_DATE, SCANNED_BY,
        BOOK_STATUS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询书籍信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_book t
        WHERE t.ID = #{id}
    </select>

    <!--查询书籍信息-->
    <select id="query" parameterType="com.edu.www.dto.BookDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_book t
        <where>
            <if test="bookName != null and bookName != ''">
                and t.BOOK_NAME like CONCAT('%', #{bookName}, '%')
            </if>
            <if test="author != null and author != ''">
                and t.AUTHOR like CONCAT('%', #{author}, '%')
            </if>
            <if test="isbn != null and isbn != ''">
                and t.ISBN like CONCAT('%', #{isbn}, '%')
            </if>
            <if test="publisher != null and publisher != ''">
                and t.PUBLISHER like CONCAT('%', #{publisher}, '%')
            </if>
            <if test="bookCategory != null and bookCategory != ''">
                and t.BOOK_CATEGORY = #{bookCategory}
            </if>
            <if test="subjectCode != null and subjectCode != ''">
                and t.SUBJECT_CODE = #{subjectCode}
            </if>
            <if test="gradeLevel != null and gradeLevel != ''">
                <choose>
                    <!-- 如果是JSON数组格式，使用JSON_CONTAINS查询 -->
                    <when test="gradeLevel.startsWith('[') and gradeLevel.endsWith(']')">
                        and JSON_CONTAINS(t.GRADE_LEVEL, #{gradeLevel})
                    </when>
                    <!-- 如果是逗号分隔格式，使用FIND_IN_SET查询 -->
                    <when test="gradeLevel.contains(',')">
                        and (
                        <foreach collection="gradeLevel.split(',')" item="grade" separator=" or ">
                            FIND_IN_SET(#{grade}, t.GRADE_LEVEL) > 0
                        </foreach>
                        )
                    </when>
                    <!-- 单个年级精确匹配 -->
                    <otherwise>
                        and (t.GRADE_LEVEL = #{gradeLevel} or FIND_IN_SET(#{gradeLevel}, t.GRADE_LEVEL) > 0 or
                        JSON_CONTAINS(t.GRADE_LEVEL, CONCAT('"', #{gradeLevel}, '"')))
                    </otherwise>
                </choose>
            </if>
            <if test="supplierId != null and supplierId != ''">
                and t.SUPPLIER_ID = #{supplierId}
            </if>
            <if test="bookCondition != null and bookCondition != ''">
                and t.BOOK_CONDITION = #{bookCondition}
            </if>
            <if test="bookStatus != null and bookStatus != ''">
                and t.BOOK_STATUS = #{bookStatus}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增书籍信息-->
    <insert id="insert" parameterType="com.edu.www.dto.BookDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_book_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_book (
        ID, BOOK_NAME, AUTHOR, ISBN, PUBLISHER, PUBLICATION_DATE, BOOK_CATEGORY,
        SUBJECT_CODE, GRADE_LEVEL, PRICE, PURCHASE_PRICE, SUPPLIER_ID, CURRENT_STOCK,
        TOTAL_IN_STOCK, TOTAL_OUT_STOCK, MIN_STOCK_ALERT, MAX_STOCK_LIMIT, storage_location,
        BOOK_CONDITION, FRONT_COVER_IMAGE, FRONT_COVER_FILE_NAME, BACK_COVER_IMAGE, BACK_COVER_FILE_NAME, SCAN_DATE, SCANNED_BY,
        BOOK_STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{bookName}, #{author}, #{isbn}, #{publisher}, #{publicationDate}, #{bookCategory},
        #{subjectCode}, #{gradeLevel}, #{price}, #{purchasePrice}, #{supplierId}, #{currentStock},
        #{totalInStock}, #{totalOutStock}, #{minStockAlert}, #{maxStockLimit}, #{storageLocation},
        #{bookCondition}, #{frontCoverImage}, #{frontCoverFileName}, #{backCoverImage}, #{backCoverFileName},
        #{scanDate}, #{scannedBy}, #{bookStatus}, #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改书籍信息-->
    <update id="update" parameterType="com.edu.www.dto.BookDTO">
        UPDATE t_edu_book t
        <set>
            <if test="bookName != null and bookName != ''">
                t.BOOK_NAME = #{bookName},
            </if>
            <if test="author != null and author != ''">
                t.AUTHOR = #{author},
            </if>
            <if test="isbn != null and isbn != ''">
                t.ISBN = #{isbn},
            </if>
            <if test="publisher != null and publisher != ''">
                t.PUBLISHER = #{publisher},
            </if>
            <if test="publicationDate != null">
                t.PUBLICATION_DATE = #{publicationDate},
            </if>
            <if test="bookCategory != null and bookCategory != ''">
                t.BOOK_CATEGORY = #{bookCategory},
            </if>
            <if test="subjectCode != null and subjectCode != ''">
                t.SUBJECT_CODE = #{subjectCode},
            </if>
            <if test="gradeLevel != null and gradeLevel != ''">
                t.GRADE_LEVEL = #{gradeLevel},
            </if>
            <if test="price != null">
                t.PRICE = #{price},
            </if>
            <if test="purchasePrice != null">
                t.PURCHASE_PRICE = #{purchasePrice},
            </if>
            <if test="supplierId != null and supplierId != ''">
                t.SUPPLIER_ID = #{supplierId},
            </if>
            <if test="currentStock != null">
                t.CURRENT_STOCK = #{currentStock},
            </if>
            <if test="totalInStock != null">
                t.TOTAL_IN_STOCK = #{totalInStock},
            </if>
            <if test="totalOutStock != null">
                t.TOTAL_OUT_STOCK = #{totalOutStock},
            </if>
            <if test="minStockAlert != null">
                t.MIN_STOCK_ALERT = #{minStockAlert},
            </if>
            <if test="maxStockLimit != null">
                t.MAX_STOCK_LIMIT = #{maxStockLimit},
            </if>
            <if test="storageLocation != null and storageLocation != ''">
                t.storage_location = #{storageLocation},
            </if>
            <if test="bookCondition != null and bookCondition != ''">
                t.BOOK_CONDITION = #{bookCondition},
            </if>
            <if test="frontCoverImage != null and frontCoverImage != ''">
                t.FRONT_COVER_IMAGE = #{frontCoverImage},
            </if>
            <if test="frontCoverFileName != null and frontCoverFileName != ''">
                t.FRONT_COVER_FILE_NAME = #{frontCoverFileName},
            </if>
            <if test="backCoverImage != null and backCoverImage != ''">
                t.BACK_COVER_IMAGE = #{backCoverImage},
            </if>
            <if test="backCoverFileName != null and backCoverFileName != ''">
                t.BACK_COVER_FILE_NAME = #{backCoverFileName},
            </if>
            <if test="scanDate != null">
                t.SCAN_DATE = #{scanDate},
            </if>
            <if test="scannedBy != null and scannedBy != ''">
                t.SCANNED_BY = #{scannedBy},
            </if>
            <if test="bookStatus != null and bookStatus != ''">
                t.BOOK_STATUS = #{bookStatus},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除书籍信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_book
        WHERE ID = #{id}
    </delete>

</mapper>
