<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.ClassroomMapper">

    <!-- 教室信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.ClassroomVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BUILDING_NAME" jdbcType="VARCHAR" property="buildingName"/>
        <result column="BUILDING_CODE" jdbcType="VARCHAR" property="buildingCode"/>
        <result column="FLOOR" jdbcType="VARCHAR" property="floor"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="IS_CONTROLLED" jdbcType="VARCHAR" property="isControlled"/>
        <result column="TAG" jdbcType="VARCHAR" property="tag"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , BUILDING_NAME, BUILDING_CODE, FLOOR, DEPARTMENT, DEPARTMENT_CODE, NAME, CODE, IS_CONTROLLED, TAG,
        DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询教室信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_classroom t
        WHERE t.ID = #{id}
    </select>

    <!--查询教室信息-->
    <select id="query" parameterType="com.edu.www.dto.ClassroomDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_classroom t
        <where>
            <if test="buildingName != null and buildingName != ''">
                and t.BUILDING_NAME like CONCAT('%', #{buildingName}, '%')
            </if>
            <if test="buildingCode != null and buildingCode != ''">
                and t.BUILDING_CODE = #{buildingCode}
            </if>
            <if test="floor != null and floor != ''">
                and t.FLOOR = #{floor}
            </if>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="name != null and name != ''">
                and t.NAME like CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                and t.CODE = #{code}
            </if>
            <if test="isControlled != null and isControlled != ''">
                and t.IS_CONTROLLED = #{isControlled}
            </if>
            <if test="tag != null and tag != ''">
                and t.TAG like CONCAT('%', #{tag}, '%')
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.UPDATED_AT desc
    </select>

    <!--新增教室信息-->
    <insert id="insert" parameterType="com.edu.www.dto.ClassroomDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_classroom_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_classroom (
            ID, BUILDING_NAME, BUILDING_CODE, FLOOR, DEPARTMENT, DEPARTMENT_CODE, NAME, CODE, IS_CONTROLLED, TAG, DESCRIPTION,
            CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
            #{id}, #{buildingName}, #{buildingCode}, #{floor}, #{department}, #{departmentCode}, #{name}, #{code}, #{isControlled}, #{tag}, #{description},
            NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改教室信息-->
    <update id="update" parameterType="com.edu.www.dto.ClassroomDTO">
        UPDATE t_edu_classroom t
        <set>
            <if test="buildingName != null and buildingName != ''">
                t.BUILDING_NAME = #{buildingName},
            </if>
            <if test="buildingCode != null and buildingCode != ''">
                t.BUILDING_CODE = #{buildingCode},
            </if>
            <if test="floor != null and floor != ''">
                t.FLOOR = #{floor},
            </if>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="name != null and name != ''">
                t.NAME = #{name},
            </if>
            <if test="code != null and code != ''">
                t.CODE = #{code},
            </if>
            <if test="isControlled != null and isControlled != ''">
                t.IS_CONTROLLED = #{isControlled},
            </if>
            <if test="tag != null">
                t.TAG = #{tag},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除教室信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_classroom t
        WHERE t.ID = #{id}
    </delete>

</mapper> 