<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.TeacherMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.TeacherVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME_ZH" jdbcType="VARCHAR" property="nameZh"/>
        <result column="NAME_EN" jdbcType="VARCHAR" property="nameEn"/>
        <result column="GENDER" jdbcType="VARCHAR" property="gender"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="EMP_NO" jdbcType="VARCHAR" property="empNo"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="IS_SUBJECT_GROUP_LEADER" jdbcType="VARCHAR" property="isSubjectGroupLeader"/>
        <result column="SUBJECT_GROUP_CODE" jdbcType="VARCHAR" property="subjectGroupCode"/>
        <result column="OFFICE_CODE" jdbcType="VARCHAR" property="officeCode"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        , NAME_ZH, NAME_EN, GENDER, DEPARTMENT, DEPARTMENT_CODE, EMP_NO, STATUS, TYPE, IS_SUBJECT_GROUP_LEADER, SUBJECT_GROUP_CODE,
        OFFICE_CODE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--查询教师信息-->
    <select id="query" parameterType="com.edu.www.dto.TeacherDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_teacher t
        <where>
            <if test="nameZh != null and nameZh != ''">
                and t.NAME_ZH like CONCAT('%', #{nameZh}, '%')
            </if>
            <if test="nameEn != null and nameEn != ''">
                and t.NAME_EN like CONCAT('%', #{nameEn}, '%')
            </if>
            <if test="gender != null and gender != ''">
                and t.GENDER = #{gender}
            </if>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="empNo != null and empNo != ''">
                and t.EMP_NO = #{empNo}
            </if>
            <if test="status != null and status != ''">
                and t.STATUS = #{status}
            </if>
            <if test="type != null and type != ''">
                and t.TYPE = #{type}
            </if>
            <if test="isSubjectGroupLeader != null and isSubjectGroupLeader != ''">
                and t.IS_SUBJECT_GROUP_LEADER = #{isSubjectGroupLeader}
            </if>
            <if test="subjectGroupCode != null and subjectGroupCode != ''">
                and t.SUBJECT_GROUP_CODE = #{subjectGroupCode}
            </if>
            <if test="officeCode != null and officeCode != ''">
                and t.OFFICE_CODE = #{officeCode}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.updated_at desc
    </select>

    <!--新增教师信息-->
    <insert id="insert" parameterType="com.edu.www.dto.TeacherDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_teacher_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_teacher (
        ID, NAME_ZH, NAME_EN, GENDER, DEPARTMENT, DEPARTMENT_CODE, EMP_NO, STATUS, TYPE,
        IS_SUBJECT_GROUP_LEADER, SUBJECT_GROUP_CODE, OFFICE_CODE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT,
        UPDATED_BY
        )
        VALUES (
        #{id}, #{nameZh}, #{nameEn}, #{gender}, #{department}, #{departmentCode}, #{empNo}, #{status}, #{type},
        #{isSubjectGroupLeader}, #{subjectGroupCode}, #{officeCode}, #{description}, NOW(), #{createdBy}, NOW(),
        #{updatedBy}
        )
    </insert>

    <!--根据ID查询教师信息-->
    <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_teacher t
        WHERE t.ID = #{id}
    </select>

    <!--根据IDS批量查询教师信息-->
    <select id="getByIds" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_teacher t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--更新教师信息-->
    <update id="update" parameterType="com.edu.www.dto.TeacherDTO">
        UPDATE t_edu_teacher t
        <set>
            <if test="nameZh != null and nameZh != ''">
                t.NAME_ZH = #{nameZh},
            </if>
            <if test="nameEn != null">
                t.NAME_EN = #{nameEn},
            </if>
            <if test="gender != null and gender != ''">
                t.GENDER = #{gender},
            </if>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="empNo != null and empNo != ''">
                t.EMP_NO = #{empNo},
            </if>
            <if test="status != null and status != ''">
                t.STATUS = #{status},
            </if>
            <if test="type != null and type != ''">
                t.TYPE = #{type},
            </if>
            <if test="isSubjectGroupLeader != null and isSubjectGroupLeader != ''">
                t.IS_SUBJECT_GROUP_LEADER = #{isSubjectGroupLeader},
            </if>
            <if test="subjectGroupCode != null">
                t.SUBJECT_GROUP_CODE = #{subjectGroupCode},
            </if>
            <if test="officeCode != null">
                t.OFFICE_CODE = #{officeCode},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!--根据ID删除教师信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_teacher t
        WHERE t.ID = #{id}
    </delete>
</mapper> 