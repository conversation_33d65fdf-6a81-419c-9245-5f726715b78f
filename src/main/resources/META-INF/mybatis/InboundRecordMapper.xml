<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.InboundRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.InboundRecordVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PURCHASE_ID" jdbcType="VARCHAR" property="purchaseId"/>
        <result column="BOOK_ID" jdbcType="VARCHAR" property="bookId"/>
        <result column="INBOUND_QUANTITY" jdbcType="INTEGER" property="inboundQuantity"/>
        <result column="INBOUND_DATE" jdbcType="DATE" property="inboundDate"/>
        <result column="QUALITY_CHECK_RESULT" jdbcType="VARCHAR" property="qualityCheckResult"/>
        <result column="QUALITY_CHECK_NAME" jdbcType="VARCHAR" property="qualityCheckName"/>
        <result column="QUALITY_CHECK_DATE" jdbcType="DATE" property="qualityCheckDate"/>
        <result column="STORAGE_LOCATION" jdbcType="VARCHAR" property="storageLocation"/>
        <result column="INBOUND_NAME" jdbcType="VARCHAR" property="inboundName"/>
        <result column="BATCH_NO" jdbcType="VARCHAR" property="batchNo"/>
        <result column="INBOUND_TYPE" jdbcType="VARCHAR" property="inboundType"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, PURCHASE_ID, BOOK_ID, INBOUND_QUANTITY, INBOUND_DATE, QUALITY_CHECK_RESULT,
        QUALITY_CHECK_NAME, QUALITY_CHECK_DATE, STORAGE_LOCATION, INBOUND_NAME, BATCH_NO,
        INBOUND_TYPE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询入库记录信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_inbound_record t
        WHERE t.ID = #{id}
    </select>

    <!--查询入库记录信息-->
    <select id="query" parameterType="com.edu.www.dto.InboundRecordDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_inbound_record t
        <where>
            <if test="purchaseId != null and purchaseId != ''">
                and t.PURCHASE_ID = #{purchaseId}
            </if>
            <if test="bookId != null and bookId != ''">
                and t.BOOK_ID = #{bookId}
            </if>
            <if test="qualityCheckResult != null and qualityCheckResult != ''">
                and t.QUALITY_CHECK_RESULT = #{qualityCheckResult}
            </if>
            <if test="qualityCheckName != null and qualityCheckName != ''">
                and t.QUALITY_CHECK_NAME like CONCAT('%', #{qualityCheckName}, '%')
            </if>
            <if test="storageLocation != null and storageLocation != ''">
                and t.STORAGE_LOCATION like CONCAT('%', #{storageLocation}, '%')
            </if>
            <if test="inboundName != null and inboundName != ''">
                and t.INBOUND_NAME like CONCAT('%', #{inboundName}, '%')
            </if>
            <if test="batchNo != null and batchNo != ''">
                and t.BATCH_NO like CONCAT('%', #{batchNo}, '%')
            </if>
            <if test="inboundType != null and inboundType != ''">
                and t.INBOUND_TYPE = #{inboundType}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增入库记录信息-->
    <insert id="insert" parameterType="com.edu.www.dto.InboundRecordDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_inbound_record_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_inbound_record (
        ID, PURCHASE_ID, BOOK_ID, INBOUND_QUANTITY, INBOUND_DATE, QUALITY_CHECK_RESULT,
        QUALITY_CHECK_NAME, QUALITY_CHECK_DATE, STORAGE_LOCATION, INBOUND_NAME, BATCH_NO,
        INBOUND_TYPE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{purchaseId}, #{bookId}, #{inboundQuantity}, #{inboundDate}, #{qualityCheckResult},
        #{qualityCheckName}, #{qualityCheckDate}, #{storageLocation}, #{inboundName}, #{batchNo},
        #{inboundType}, #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改入库记录信息-->
    <update id="update" parameterType="com.edu.www.dto.InboundRecordDTO">
        UPDATE t_edu_inbound_record t
        <set>
            <if test="purchaseId != null and purchaseId != ''">
                t.PURCHASE_ID = #{purchaseId},
            </if>
            <if test="bookId != null and bookId != ''">
                t.BOOK_ID = #{bookId},
            </if>
            <if test="inboundQuantity != null">
                t.INBOUND_QUANTITY = #{inboundQuantity},
            </if>
            <if test="inboundDate != null">
                t.INBOUND_DATE = #{inboundDate},
            </if>
            <if test="qualityCheckResult != null and qualityCheckResult != ''">
                t.QUALITY_CHECK_RESULT = #{qualityCheckResult},
            </if>
            <if test="qualityCheckName != null and qualityCheckName != ''">
                t.QUALITY_CHECK_NAME = #{qualityCheckName},
            </if>
            <if test="qualityCheckDate != null">
                t.QUALITY_CHECK_DATE = #{qualityCheckDate},
            </if>
            <if test="storageLocation != null and storageLocation != ''">
                t.STORAGE_LOCATION = #{storageLocation},
            </if>
            <if test="inboundName != null and inboundName != ''">
                t.INBOUND_NAME = #{inboundName},
            </if>
            <if test="batchNo != null and batchNo != ''">
                t.BATCH_NO = #{batchNo},
            </if>
            <if test="inboundType != null and inboundType != ''">
                t.INBOUND_TYPE = #{inboundType},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除入库记录信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_inbound_record WHERE ID = #{id}
    </delete>

</mapper>
