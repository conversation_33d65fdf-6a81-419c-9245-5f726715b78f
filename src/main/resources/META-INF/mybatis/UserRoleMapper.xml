<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.UserRoleMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.UserRoleVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="USER_ID" jdbcType="VARCHAR" property="userId"/>
        <result column="ROLE_ID" jdbcType="VARCHAR" property="roleId"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        ID
        , USER_ID, ROLE_ID, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!-- 根据ID查询用户角色关联信息 -->
    <select id="get" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_user_role
        WHERE ID = #{id}
    </select>

    <!-- 查询用户角色关联列表 -->
    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_user_role
        <where>
            <if test="userId != null and userId != ''">
                AND USER_ID = #{userId}
            </if>
            <if test="roleId != null and roleId != ''">
                AND ROLE_ID = #{roleId}
            </if>
        </where>
        ORDER BY CREATED_AT DESC
    </select>

    <!-- 根据用户ID查询用户角色关联列表 -->
    <select id="getByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_user_role
        WHERE USER_ID = #{userId}
    </select>

    <!-- 根据角色ID查询用户角色关联列表 -->
    <select id="queryByRoleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_user_role
        WHERE ROLE_ID = #{roleId}
    </select>

    <!-- 新增用户角色关联 -->
    <insert id="insert" parameterType="com.edu.www.dto.UserRoleDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_user_role_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_user_role(ID, USER_ID, ROLE_ID, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
        VALUES (#{id}, #{userId}, #{roleId}, NOW(), #{createdBy}, NOW(), #{updatedBy})
    </insert>

    <!-- 批量新增用户角色关联 -->
    <insert id="batchInsert">
        INSERT INTO t_edu_user_role(
        ID, USER_ID, ROLE_ID, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (CONCAT('t_edu_user_role_', UUID()), #{item.userId}, #{item.roleId}, NOW(), #{item.createdBy},
            NOW(), #{item.updatedBy})
        </foreach>
    </insert>

    <!-- 更新用户角色关联 -->
    <update id="update" parameterType="com.edu.www.dto.UserRoleDTO">
        UPDATE t_edu_user_role
        <set>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="roleId != null">ROLE_ID = #{roleId},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 删除用户角色关联 -->
    <delete id="delete">
        DELETE
        FROM t_edu_user_role
        WHERE ID = #{id}
    </delete>

    <!-- 根据用户ID删除用户角色关联 -->
    <delete id="deleteByUserId">
        DELETE
        FROM t_edu_user_role
        WHERE USER_ID = #{userId}
    </delete>

    <!-- 根据角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId">
        DELETE
        FROM t_edu_user_role
        WHERE ROLE_ID = #{roleId}
    </delete>

    <!-- 根据用户ID和角色ID删除用户角色关联 -->
    <delete id="deleteByUserIdAndRoleId">
        DELETE
        FROM t_edu_user_role
        WHERE USER_ID = #{userId}
          AND ROLE_ID = #{roleId}
    </delete>
</mapper> 