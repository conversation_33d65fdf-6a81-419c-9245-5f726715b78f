<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.ReturnRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.ReturnRecordVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BORROW_ID" jdbcType="VARCHAR" property="borrowId"/>
        <result column="RETURN_QUANTITY" jdbcType="INTEGER" property="returnQuantity"/>
        <result column="RETURN_DATE" jdbcType="DATE" property="returnDate"/>
        <result column="RETURN_CONDITION" jdbcType="VARCHAR" property="returnCondition"/>
        <result column="QUALITY_CHECK_NAME" jdbcType="VARCHAR" property="qualityCheckName"/>
        <result column="DAMAGE_DESCRIPTION" jdbcType="VARCHAR" property="damageDescription"/>
        <result column="PENALTY_AMOUNT" jdbcType="DECIMAL" property="penaltyAmount"/>
        <result column="RETURN_NAME" jdbcType="VARCHAR" property="returnName"/>
        <result column="RECEIVED_NAME" jdbcType="VARCHAR" property="receivedName"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, BORROW_ID, RETURN_QUANTITY, RETURN_DATE, RETURN_CONDITION, QUALITY_CHECK_NAME,
        DAMAGE_DESCRIPTION, PENALTY_AMOUNT, RETURN_NAME, RECEIVED_NAME, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询归还记录信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_return_record t
        WHERE t.ID = #{id}
    </select>

    <!--查询归还记录信息-->
    <select id="query" parameterType="com.edu.www.dto.ReturnRecordDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_return_record t
        <where>
            <if test="borrowId != null and borrowId != ''">
                and t.BORROW_ID = #{borrowId}
            </if>
            <if test="returnCondition != null and returnCondition != ''">
                and t.RETURN_CONDITION = #{returnCondition}
            </if>
            <if test="qualityCheckName != null and qualityCheckName != ''">
                and t.QUALITY_CHECK_NAME like CONCAT('%', #{qualityCheckName}, '%')
            </if>
            <if test="returnName != null and returnName != ''">
                and t.RETURN_NAME like CONCAT('%', #{returnName}, '%')
            </if>
            <if test="receivedName != null and receivedName != ''">
                and t.RECEIVED_NAME like CONCAT('%', #{receivedName}, '%')
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增归还记录信息-->
    <insert id="insert" parameterType="com.edu.www.dto.ReturnRecordDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_return_record_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_return_record (
        ID, BORROW_ID, RETURN_QUANTITY, RETURN_DATE, RETURN_CONDITION, QUALITY_CHECK_NAME,
        DAMAGE_DESCRIPTION, PENALTY_AMOUNT, RETURN_NAME, RECEIVED_NAME, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{borrowId}, #{returnQuantity}, #{returnDate}, #{returnCondition}, #{qualityCheckName},
        #{damageDescription}, #{penaltyAmount}, #{returnName}, #{receivedName}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改归还记录信息-->
    <update id="update" parameterType="com.edu.www.dto.ReturnRecordDTO">
        UPDATE t_edu_return_record t
        <set>
            <if test="borrowId != null and borrowId != ''">
                t.BORROW_ID = #{borrowId},
            </if>
            <if test="returnQuantity != null">
                t.RETURN_QUANTITY = #{returnQuantity},
            </if>
            <if test="returnDate != null">
                t.RETURN_DATE = #{returnDate},
            </if>
            <if test="returnCondition != null and returnCondition != ''">
                t.RETURN_CONDITION = #{returnCondition},
            </if>
            <if test="qualityCheckName != null and qualityCheckName != ''">
                t.QUALITY_CHECK_NAME = #{qualityCheckName},
            </if>
            <if test="damageDescription != null and damageDescription != ''">
                t.DAMAGE_DESCRIPTION = #{damageDescription},
            </if>
            <if test="penaltyAmount != null">
                t.PENALTY_AMOUNT = #{penaltyAmount},
            </if>
            <if test="returnName != null and returnName != ''">
                t.RETURN_NAME = #{returnName},
            </if>
            <if test="receivedName != null and receivedName != ''">
                t.RECEIVED_NAME = #{receivedName},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除归还记录信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_return_record WHERE ID = #{id}
    </delete>

</mapper>
