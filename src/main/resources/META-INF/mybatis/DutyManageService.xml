<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.MenuMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.MenuVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="NAME_EN" jdbcType="VARCHAR" property="nameEn"/>
        <result column="PATH" jdbcType="VARCHAR" property="path"/>
        <result column="COMPONENT" jdbcType="VARCHAR" property="component"/>
        <result column="META" property="meta" jdbcType="VARCHAR"/>
        <result column="ICON" jdbcType="VARCHAR" property="icon"/>
        <result column="DIRECTORY_SORT" jdbcType="INTEGER" property="directorySort"/>
        <result column="PAGE_SORT" jdbcType="INTEGER" property="pageSort"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="IS_HIDDEN" jdbcType="VARCHAR" property="isHidden"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        ID
        , PARENT_ID, NAME, NAME_EN, PATH, COMPONENT, META, ICON, DIRECTORY_SORT, PAGE_SORT, TYPE, IS_HIDDEN, STATUS,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!-- 根据ID查询菜单信息 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_menu
        WHERE ID = #{id}
    </select>

    <!--查询父级菜单数量-->
    <select id="queryParentAll" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_edu_menu
        <where>
            AND PARENT_ID = ''
            <if test="name != null and name != ''">
                AND NAME LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="type != null and type != ''">
                AND TYPE = #{type}
            </if>
            <if test="status != null">
                AND STATUS = #{status}
            </if>
            <if test="isHidden != null">
                AND IS_HIDDEN = #{isHidden}
            </if>
        </where>
        ORDER BY DIRECTORY_SORT, PAGE_SORT ASC
    </select>

    <!-- 查询菜单列表 -->
    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_menu
        <where>
            <if test="name != null and name != ''">
                AND NAME LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="parentId != null and parentId != ''">
                AND PARENT_ID = #{parentId}
            </if>
            <if test="type != null and type != ''">
                AND TYPE = #{type}
            </if>
            <if test="status != null">
                AND STATUS = #{status}
            </if>
            <if test="isHidden != null">
                AND IS_HIDDEN = #{isHidden}
            </if>
        </where>
        ORDER BY DIRECTORY_SORT, PAGE_SORT ASC
    </select>

    <!-- 查询所有菜单（包含权限信息） -->
    <resultMap id="MenuWithPermissionsMap" type="com.edu.www.vo.MenuVO" extends="BaseResultMap">
        <collection property="permissions" ofType="com.edu.www.vo.PermissionVO" column="ID"
                    select="com.edu.www.mapper.PermissionMapper.queryByMenuId"/>
    </resultMap>

    <select id="queryAllWithPermissions" resultMap="MenuWithPermissionsMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_menu
        WHERE STATUS = 1
        ORDER BY DIRECTORY_SORT, PAGE_SORT ASC
    </select>

    <!-- 根据用户ID查询菜单列表（包含所有父级节点，MySQL 8.0+递归CTE实现） -->
    <select id="queryByUserId" resultMap="BaseResultMap">
        WITH RECURSIVE menu_tree AS (
            SELECT m.*
            FROM t_edu_menu m
                     INNER JOIN t_edu_permission p ON m.ID = p.MENU_ID
                     INNER JOIN t_edu_role_permission rp ON p.ID = rp.PERMISSION_ID
                     INNER JOIN t_edu_user_role ur ON rp.ROLE_ID = ur.ROLE_ID
            WHERE ur.USER_ID = #{userId}
              AND m.STATUS = 1
            UNION
            SELECT parent.*
            FROM t_edu_menu parent
                     JOIN menu_tree child ON parent.ID = child.PARENT_ID
        )
        SELECT DISTINCT * FROM menu_tree
        ORDER BY DIRECTORY_SORT, PAGE_SORT ASC
    </select>

    <!-- 根据角色ID查询菜单列表 -->
    <select id="queryByRoleId" resultMap="BaseResultMap">
        SELECT DISTINCT m.*
        FROM t_edu_menu m
                 INNER JOIN t_edu_permission p ON m.ID = p.MENU_ID
                 INNER JOIN t_edu_role_permission rp ON p.ID = rp.PERMISSION_ID
        WHERE rp.ROLE_ID = #{roleId}
          AND m.STATUS = 1
        ORDER BY m.DIRECTORY_SORT, m.PAGE_SORT ASC
    </select>

    <!-- 新增菜单 -->
    <insert id="insert" parameterType="com.edu.www.dto.MenuDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_menu_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_menu(ID, PARENT_ID, NAME, NAME_EN, PATH, COMPONENT, META, ICON, DIRECTORY_SORT, PAGE_SORT, TYPE,
        IS_HIDDEN, STATUS, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
        VALUES (#{id}, #{parentId}, #{name}, #{nameEn}, #{path}, #{component}, #{meta}, #{icon}, #{directorySort}, #{pageSort},
        #{type}, #{isHidden}, #{status}, NOW(), #{createdBy}, NOW(), #{updatedBy})
    </insert>

    <!-- 更新菜单 -->
    <update id="update" parameterType="com.edu.www.dto.MenuDTO">
        UPDATE t_edu_menu
        <set>
            <if test="parentId != null">PARENT_ID = #{parentId},</if>
            <if test="name != null">NAME = #{name},</if>
            <if test="nameEn != null">NAME_EN = #{nameEn},</if>
            <if test="path != null">PATH = #{path},</if>
            <if test="component != null">COMPONENT = #{component},</if>
            <if test="meta != null">META = #{meta},</if>
            <if test="icon != null">ICON = #{icon},</if>
            DIRECTORY_SORT = #{directorySort},
            PAGE_SORT = #{pageSort},
            <if test="type != null">TYPE = #{type},</if>
            <if test="isHidden != null">IS_HIDDEN = #{isHidden},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 删除菜单 -->
    <delete id="delete">
        DELETE
        FROM t_edu_menu
        WHERE ID = #{id}
    </delete>
</mapper> 