<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.FileMapper">

    <!-- 文件信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.FileVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="FILE_ID" jdbcType="VARCHAR" property="fileId"/>
        <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName"/>
        <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType"/>
        <result column="FILE_SIZE" jdbcType="BIGINT" property="fileSize"/>
        <result column="STORAGE_DIR" jdbcType="VARCHAR" property="storageDir"/>
        <result column="PREVIEW_URL" jdbcType="VARCHAR" property="previewUrl"/>
        <result column="IS_PREVIEW" jdbcType="VARCHAR" property="isPreview"/>
        <result column="EXTENSION" jdbcType="VARCHAR" property="extension"/>
        <result column="FULL_PATH" jdbcType="VARCHAR" property="fullPath"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , FILE_ID, FILE_NAME, FILE_TYPE, FILE_SIZE, STORAGE_DIR, PREVIEW_URL,
        IS_PREVIEW, EXTENSION, FULL_PATH, STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询文件信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_file t
        WHERE t.ID = #{id}
    </select>

    <!--根据文件ID查询文件信息-->
    <select id="getByFileId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_file t
        WHERE t.FILE_ID = #{fileId}
        LIMIT 1
    </select>

    <!--根据多个文件ID查询文件信息-->
    <select id="getByFileIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_file t
        WHERE t.FILE_ID IN
        <foreach collection="fileIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--查询文件信息-->
    <select id="query" parameterType="com.edu.www.dto.FileDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_file t
        <where>
            <if test="fileId != null and fileId != ''">
                and t.FILE_ID = #{fileId}
            </if>
            <if test="fileName != null and fileName != ''">
                and t.FILE_NAME like CONCAT('%', #{fileName}, '%')
            </if>
            <if test="fileType != null and fileType != ''">
                and t.FILE_TYPE = #{fileType}
            </if>
            <if test="storageDir != null and storageDir != ''">
                and t.STORAGE_DIR like CONCAT('%', #{storageDir}, '%')
            </if>
            <if test="isPreview != null and isPreview != ''">
                and t.IS_PREVIEW = #{isPreview}
            </if>
            <if test="extension != null and extension != ''">
                and t.EXTENSION = #{extension}
            </if>
            <if test="status != null and status != ''">
                and t.STATUS = #{status}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增文件信息-->
    <insert id="insert" parameterType="com.edu.www.dto.FileDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_file_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_file (
        ID, FILE_ID, FILE_NAME, FILE_TYPE, FILE_SIZE, STORAGE_DIR, PREVIEW_URL,
        IS_PREVIEW, EXTENSION, FULL_PATH, STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{fileId}, #{fileName}, #{fileType}, #{fileSize}, #{storageDir}, #{previewUrl},
        #{isPreview}, #{extension}, #{fullPath}, #{status}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改文件信息-->
    <update id="update" parameterType="com.edu.www.dto.FileDTO">
        UPDATE t_edu_file
        <set>
            <if test="fileName != null and fileName != ''">
                FILE_NAME = #{fileName},
            </if>
            <if test="fileType != null and fileType != ''">
                FILE_TYPE = #{fileType},
            </if>
            <if test="fileSize != null">
                FILE_SIZE = #{fileSize},
            </if>
            <if test="storageDir != null and storageDir != ''">
                STORAGE_DIR = #{storageDir},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                PREVIEW_URL = #{previewUrl},
            </if>
            <if test="isPreview != null and isPreview != ''">
                IS_PREVIEW = #{isPreview},
            </if>
            <if test="extension != null and extension != ''">
                EXTENSION = #{extension},
            </if>
            <if test="fullPath != null and fullPath != ''">
                FULL_PATH = #{fullPath},
            </if>
            <if test="status != null and status != ''">
                STATUS = #{status},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                UPDATED_BY = #{updatedBy},
            </if>
            UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!--根据ID删除文件信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_file
        WHERE ID = #{id}
    </delete>

    <!--根据文件ID删除文件信息-->
    <delete id="deleteByFileId" parameterType="java.lang.String">
        DELETE
        FROM t_edu_file
        WHERE FILE_ID = #{fileId}
    </delete>

    <!--批量删除文件信息-->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM t_edu_file WHERE ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--根据文件类型查询文件信息-->
    <select id="queryByFileType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_file t
        WHERE t.FILE_TYPE = #{fileType}
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--根据状态查询文件信息-->
    <select id="queryByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_file t
        WHERE t.STATUS = #{status}
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--统计文件总数-->
    <select id="countFiles" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_edu_file
    </select>

    <!--统计文件总大小-->
    <select id="sumFileSize" resultType="java.lang.Long">
        SELECT COALESCE(SUM(FILE_SIZE), 0)
        FROM t_edu_file
    </select>

</mapper>
