<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.ScheduleMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.ScheduleVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="SUBJECT_NAME" jdbcType="VARCHAR" property="subjectName"/>
        <result column="GRADE_CODE" jdbcType="VARCHAR" property="gradeCode"/>
        <result column="CLASS_CODE" jdbcType="VARCHAR" property="classCode"/>
        <result column="SUBJECT_LEVEL" jdbcType="VARCHAR" property="subjectLevel"/>
        <result column="TEACHER_ID" jdbcType="VARCHAR" property="teacherId"/>
        <result column="CLASS_ROOM_CODE" jdbcType="VARCHAR" property="classRoomCode"/>
        <result column="WEEKDAY" jdbcType="VARCHAR" property="weekday"/>
        <result column="DAY_PART" jdbcType="VARCHAR" property="dayPart"/>
        <result column="PERIOD" jdbcType="VARCHAR" property="period"/>
        <result column="START_TIME" jdbcType="TIME" property="startTime"/>
        <result column="END_TIME" jdbcType="TIME" property="endTime"/>
        <result column="YEAR" jdbcType="VARCHAR" property="year"/>
        <result column="SEMESTER" jdbcType="VARCHAR" property="semester"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        , DEPARTMENT, DEPARTMENT_CODE, SUBJECT_NAME, GRADE_CODE, CLASS_CODE, SUBJECT_LEVEL,
        TEACHER_ID, CLASS_ROOM_CODE, WEEKDAY, DAY_PART, PERIOD, START_TIME, END_TIME,
        YEAR, SEMESTER, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--查询课程表信息-->
    <select id="query" parameterType="com.edu.www.dto.ScheduleDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_schedule t
        <where>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="subjectName != null and subjectName != ''">
                and t.SUBJECT_NAME like CONCAT('%', #{subjectName}, '%')
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                and t.GRADE_CODE = #{gradeCode}
            </if>
            <if test="classCode != null and classCode != ''">
                and t.CLASS_CODE like CONCAT('%', #{classCode}, '%')
            </if>
            <if test="subjectLevel != null and subjectLevel != ''">
                and t.SUBJECT_LEVEL = #{subjectLevel}
            </if>
            <if test="teacherId != null and teacherId != ''">
                and t.TEACHER_ID = #{teacherId}
            </if>
            <if test="classRoomCode != null and classRoomCode != ''">
                and t.CLASS_ROOM_CODE = #{classRoomCode}
            </if>
            <if test="weekday != null and weekday != ''">
                and t.WEEKDAY = #{weekday}
            </if>
            <if test="dayPart != null and dayPart != ''">
                and t.DAY_PART = #{dayPart}
            </if>
            <if test="period != null and period != ''">
                and t.PERIOD = #{period}
            </if>
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.WEEKDAY, t.DAY_PART, t.PERIOD
    </select>

    <!--查询课程表信息-->
    <select id="queryList" parameterType="com.edu.www.vo.ScheduleToolVO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_schedule t
        <where>
            <!-- 必填条件 -->
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            
            <!-- 年级条件 - 使用列表 -->
            <if test="gradeCodes != null and gradeCodes.size() > 0">
                <choose>
                    <when test="gradeCmpSym != null and gradeCmpSym == 'notEqual'">
                        and t.GRADE_CODE not in
                    </when>
                    <otherwise>
                        and t.GRADE_CODE in
                    </otherwise>
                </choose>
                <foreach collection="gradeCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            
            <!-- 教师条件 - 使用列表 -->
            <if test="teacherIds != null and teacherIds.size() > 0">
                <choose>
                    <when test="teacherCmpSym != null and teacherCmpSym == 'notEqual'">
                        and t.TEACHER_ID not in
                    </when>
                    <otherwise>
                        and t.TEACHER_ID in
                    </otherwise>
                </choose>
                <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                    #{teacherId}
                </foreach>
            </if>

            <!-- 星期条件 - 使用列表 -->
            <if test="weekdays != null and weekdays.size() > 0">
                <choose>
                    <when test="weekdayCmpSym != null and weekdayCmpSym == 'notEqual'">
                        and t.WEEKDAY not in
                    </when>
                    <otherwise>
                        and t.WEEKDAY in
                    </otherwise>
                </choose>
                <foreach collection="weekdays" item="day" open="(" separator="," close=")">
                    #{day}
                </foreach>
            </if>

            <!-- 课段条件 - 使用列表 -->
            <if test="periods != null and periods.size() > 0">
                <choose>
                    <when test="periodCmpSym != null and periodCmpSym == 'notEqual'">
                        and t.PERIOD not in
                    </when>
                    <otherwise>
                        and t.PERIOD in
                    </otherwise>
                </choose>
                <foreach collection="periods" item="p" open="(" separator="," close=")">
                    #{p}
                </foreach>
            </if>

            <!-- 时间段条件 -->
            <if test="startTime != null and endTime != null">
                AND t.START_TIME &lt; #{endTime} AND t.END_TIME &gt; #{startTime}
            </if>
        </where>
    </select>

    <!--新增课程表信息-->
    <insert id="insert" parameterType="com.edu.www.dto.ScheduleDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_schedule_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_schedule (
        ID, DEPARTMENT, DEPARTMENT_CODE, SUBJECT_NAME, GRADE_CODE, CLASS_CODE, SUBJECT_LEVEL,
        TEACHER_ID, CLASS_ROOM_CODE, WEEKDAY, DAY_PART, PERIOD, START_TIME, END_TIME,
        YEAR, SEMESTER, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{department}, #{departmentCode}, #{subjectName}, #{gradeCode}, #{classCode}, #{subjectLevel},
        #{teacherId}, #{classRoomCode}, #{weekday}, #{dayPart}, #{period}, #{startTime}, #{endTime},
        #{year}, #{semester}, #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--根据ID查询课程表信息-->
    <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_schedule t
        WHERE t.ID = #{id}
    </select>

    <!--更新课程表信息-->
    <update id="update" parameterType="com.edu.www.dto.ScheduleDTO">
        UPDATE t_edu_schedule t
        <set>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="subjectName != null and subjectName != ''">
                t.SUBJECT_NAME = #{subjectName},
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                t.GRADE_CODE = #{gradeCode},
            </if>
            <if test="classCode != null">
                t.CLASS_CODE = #{classCode},
            </if>
            <if test="subjectLevel != null">
                t.SUBJECT_LEVEL = #{subjectLevel},
            </if>
            <if test="teacherId != null and teacherId != ''">
                t.TEACHER_ID = #{teacherId},
            </if>
            <if test="classRoomCode != null">
                t.CLASS_ROOM_CODE = #{classRoomCode},
            </if>
            <if test="weekday != null and weekday != ''">
                t.WEEKDAY = #{weekday},
            </if>
            <if test="dayPart != null and dayPart != ''">
                t.DAY_PART = #{dayPart},
            </if>
            <if test="period != null and period != ''">
                t.PERIOD = #{period},
            </if>
            <if test="startTime != null">
                t.START_TIME = #{startTime},
            </if>
            <if test="endTime != null">
                t.END_TIME = #{endTime},
            </if>
            <if test="year != null and year != ''">
                t.YEAR = #{year},
            </if>
            <if test="semester != null and semester != ''">
                t.SEMESTER = #{semester},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!--根据ID删除课程表信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_schedule t
        WHERE t.ID = #{id}
    </delete>
</mapper> 