<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.UserVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="LAST_NAME" jdbcType="VARCHAR" property="lastName"/>
        <result column="FIRST_NAME" jdbcType="VARCHAR" property="firstName"/>
        <result column="NAME_ZH" jdbcType="VARCHAR" property="nameZh"/>
        <result column="NAME_EN" jdbcType="VARCHAR" property="nameEn"/>
        <result column="GENDER" jdbcType="TINYINT" property="gender"/>
        <result column="BIRTH_DATE" jdbcType="DATE" property="birthDate"/>
        <result column="BIRTHDAY_TYPE" jdbcType="VARCHAR" property="birthdayType"/>
        <result column="BIRTHDAY" jdbcType="DATE" property="birthday"/>
        <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="EMP_NO" jdbcType="VARCHAR" property="empNo"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="PASSWORD" jdbcType="VARCHAR" property="password"/>
        <result column="LANGUAGE" jdbcType="VARCHAR" property="language"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, LAST_NAME, FIRST_NAME, NAME_ZH, NAME_EN, GENDER, BIRTH_DATE,
          BIRTHDAY_TYPE, BIRTHDAY, DEPARTMENT_NAME, DEPARTMENT_CODE, EMP_NO, EMAIL, PASSWORD, LANGUAGE,
          CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询用户信息-->
    <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_user t where t.id = #{id}
    </select>

    <!--根据多个ID批量查询用户信息-->
    <select id="getByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_user t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--根据用户名和密码查询用户信息-->
    <select id="getByUsernameAndPassword" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_user t
        where t.EMAIL = #{username} and t.PASSWORD = #{password}
    </select>

    <!--查询用户信息-->
    <select id="query" parameterType="com.edu.www.dto.UserDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_user t
        <where>
            <if test="lastName != null and lastName != ''">
                and t.LAST_NAME like CONCAT(#{lastName}, '%')
            </if>
            <if test="firstName != null and firstName != ''">
                and t.FIRST_NAME like CONCAT('%', #{firstName}, '%')
            </if>
            <if test="nameZh != null and nameZh != ''">
                and t.NAME_ZH like CONCAT('%', #{nameZh}, '%')
            </if>
            <if test="nameEn != null and nameEn != ''">
                and t.NAME_EN like CONCAT('%', #{nameEn}, '%')
            </if>
            <if test="gender != null">
                and t.GENDER = #{gender}
            </if>
            <if test="birthDate != null">
                and t.BIRTH_DATE = #{birthDate}
            </if>
            <if test="birthdayType != null and birthdayType != ''">
                and t.BIRTHDAY_TYPE = #{birthdayType}
            </if>
            <if test="birthday != null">
                and t.BIRTHDAY = #{birthday}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and t.DEPARTMENT_NAME like CONCAT('%', #{departmentName}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="empNo != null and empNo != ''">
                and t.EMP_NO = #{empNo}
            </if>
            <if test="email != null and email != ''">
                and t.EMAIL = #{email}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.updated_at desc
    </select>

    <!--查询邮箱号是否存在-->
    <select id="existsByEmail" parameterType="java.lang.String" resultType="java.lang.Boolean">
        select count(*) > 0
        from t_edu_user t
        where t.EMAIL = #{email}
    </select>

    <!--新增用户信息-->
    <insert id="insert" parameterType="com.edu.www.dto.UserDTO" useGeneratedKeys="false">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_user_', UUID())]]>
        </selectKey>
        insert into t_edu_user
        (<include refid="Base_Column_List"/>)
        values (#{id}, #{lastName}, #{firstName}, #{nameZh}, #{nameEn}, #{gender}, #{birthDate},
        #{birthdayType}, #{birthday}, #{departmentName}, #{departmentCode}, #{empNo}, #{email}, #{password}, #{language},
        NOW(), #{createdBy}, NOW(), #{updatedBy})
    </insert>

    <!--更新用户信息-->
    <update id="update" parameterType="com.edu.www.vo.UserVO">
        update t_edu_user t
        <set>
            <if test="lastName != null and lastName != ''">
                t.LAST_NAME = #{lastName},
            </if>
            <if test="firstName != null and firstName != ''">
                t.FIRST_NAME = #{firstName},
            </if>
            <if test="nameZh != null and nameZh != ''">
                t.NAME_ZH = #{nameZh},
            </if>
            <if test="nameEn != null and nameEn != ''">
                t.NAME_EN = #{nameEn},
            </if>
            <if test="gender != null">
                t.GENDER = #{gender},
            </if>
            <if test="birthDate != null">
                t.BIRTH_DATE = #{birthDate},
            </if>
            <if test="birthdayType != null and birthdayType != ''">
                t.BIRTHDAY_TYPE = #{birthdayType},
            </if>
            <if test="birthday != null">
                t.BIRTHDAY = #{birthday},
            </if>
            <if test="departmentName != null and departmentName != ''">
                t.DEPARTMENT_NAME = #{departmentName},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="empNo != null and empNo != ''">
                t.EMP_NO = #{empNo},
            </if>
            <if test="email != null and email != ''">
                t.EMAIL = #{email},
            </if>
            <if test="password != null and password != ''">
                t.PASSWORD = #{password},
            </if>
            <if test="language != null and language != ''">
                t.LANGUAGE = #{language},
            </if>
            <if test="createdBy != null and createdBy != ''">
                t.CREATED_BY = #{createdBy},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        where t.ID = #{id}
    </update>

    <!--根据ID修改密码-->
    <update id="updatePwdById" parameterType="com.edu.www.vo.UserVO">
        update t_edu_user t
        <set>
            <if test="password != null and password != ''">
                t.PASSWORD = #{password},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        where t.ID = #{id}
    </update>

    <!--根据ID删除用户信息-->
    <delete id="delete" parameterType="java.lang.String">
        delete
        from t_edu_user t
        where t.id = #{id}
    </delete>
</mapper>