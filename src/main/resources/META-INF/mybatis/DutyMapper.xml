<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.DutyMapper">

    <!-- 值日信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.DutyVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="YEAR" jdbcType="VARCHAR" property="year"/>
        <result column="SEMESTER" jdbcType="VARCHAR" property="semester"/>
        <result column="SEQ_WEEK" jdbcType="VARCHAR" property="seqWeek"/>
        <result column="WEEKDAY" jdbcType="VARCHAR" property="weekday"/>
        <result column="DUTY_DATE" jdbcType="DATE" property="dutyDate"/>
        <result column="DUTY_TYPE" jdbcType="VARCHAR" property="dutyType"/>
        <result column="TEACHER_NAME" jdbcType="VARCHAR" property="teacherName"/>
        <result column="TEACHER_ID" jdbcType="VARCHAR" property="teacherId"/>
        <result column="COLOR" jdbcType="VARCHAR" property="color"/>
        <result column="IS_BASE_GROUP" jdbcType="VARCHAR" property="isBaseGroup"/>
        <result column="BASE_GROUP_CODE" jdbcType="VARCHAR" property="baseGroupCode"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, SEQ_WEEK, WEEKDAY, DUTY_DATE,
        DUTY_TYPE, TEACHER_NAME, TEACHER_ID, COLOR, IS_BASE_GROUP, BASE_GROUP_CODE,
        DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询值日信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_duty t
        WHERE t.ID = #{id}
    </select>

    <!--查询值日信息-->
    <select id="query" parameterType="com.edu.www.dto.DutyDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_duty t
        <where>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="seqWeek != null and seqWeek != ''">
                and t.SEQ_WEEK = #{seqWeek}
            </if>
            <if test="weekday != null and weekday != ''">
                and t.WEEKDAY = #{weekday}
            </if>
            <if test="dutyDate != null">
                and t.DUTY_DATE = #{dutyDate}
            </if>
            <if test="dutyType != null and dutyType != ''">
                and t.DUTY_TYPE = #{dutyType}
            </if>
            <if test="teacherName != null and teacherName != ''">
                and t.TEACHER_NAME like CONCAT('%', #{teacherName}, '%')
            </if>
            <if test="teacherId != null and teacherId != ''">
                and t.TEACHER_ID = #{teacherId}
            </if>
            <if test="color != null and color != ''">
                and t.COLOR = #{color}
            </if>
            <if test="isBaseGroup != null and isBaseGroup != ''">
                and t.IS_BASE_GROUP = #{isBaseGroup}
            </if>
            <if test="baseGroupCode != null and baseGroupCode != ''">
                and t.BASE_GROUP_CODE = #{baseGroupCode}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.DUTY_DATE ASC, t.UPDATED_AT desc
    </select>

    <!--新增值日信息-->
    <insert id="insert" parameterType="com.edu.www.dto.DutyDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_duty_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_duty (
        ID, DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, SEQ_WEEK, WEEKDAY, DUTY_DATE,
        DUTY_TYPE, TEACHER_NAME, TEACHER_ID, COLOR, IS_BASE_GROUP, BASE_GROUP_CODE, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{department}, #{departmentCode}, #{year}, #{semester}, #{seqWeek}, #{weekday}, #{dutyDate},
        #{dutyType}, #{teacherName}, #{teacherId}, #{color}, #{isBaseGroup}, #{baseGroupCode}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>


    <!--修改值日信息-->
    <update id="update" parameterType="com.edu.www.dto.DutyDTO">
        UPDATE t_edu_duty t
        <set>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="year != null and year != ''">
                t.YEAR = #{year},
            </if>
            <if test="semester != null and semester != ''">
                t.SEMESTER = #{semester},
            </if>
            <if test="seqWeek != null and seqWeek != ''">
                t.SEQ_WEEK = #{seqWeek},
            </if>
            <if test="weekday != null and weekday != ''">
                t.WEEKDAY = #{weekday},
            </if>
            <if test="dutyDate != null">
                t.DUTY_DATE = #{dutyDate},
            </if>
            <if test="dutyType != null and dutyType != ''">
                t.DUTY_TYPE = #{dutyType},
            </if>
            <if test="teacherName != null and teacherName != ''">
                t.TEACHER_NAME = #{teacherName},
            </if>
            <if test="teacherId != null and teacherId != ''">
                t.TEACHER_ID = #{teacherId},
            </if>
            <if test="color != null">
                t.COLOR = #{color},
            </if>
            <if test="isBaseGroup != null and isBaseGroup != ''">
                t.IS_BASE_GROUP = #{isBaseGroup},
            </if>
            <if test="baseGroupCode != null and baseGroupCode != ''">
                t.BASE_GROUP_CODE = #{baseGroupCode},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除值日信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_duty t
        WHERE t.ID = #{id}
    </delete>

    <!--批量删除值日信息-->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE
        FROM t_edu_duty t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--查询值日信息并按DUTY_DATE升序排序-->
    <select id="queryOrderByDutyDateAsc" parameterType="com.edu.www.dto.DutyDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_duty t
        <where>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="seqWeek != null and seqWeek != ''">
                and t.SEQ_WEEK = #{seqWeek}
            </if>
            <if test="weekday != null and weekday != ''">
                and t.WEEKDAY = #{weekday}
            </if>
            <if test="dutyDate != null">
                and t.DUTY_DATE = #{dutyDate}
            </if>
            <if test="dutyType != null and dutyType != ''">
                and t.DUTY_TYPE = #{dutyType}
            </if>
            <if test="teacherName != null and teacherName != ''">
                and t.TEACHER_NAME like CONCAT('%', #{teacherName}, '%')
            </if>
            <if test="teacherId != null and teacherId != ''">
                and t.TEACHER_ID = #{teacherId}
            </if>
            <if test="color != null and color != ''">
                and t.COLOR = #{color}
            </if>
            <if test="isBaseGroup != null and isBaseGroup != ''">
                and t.IS_BASE_GROUP = #{isBaseGroup}
            </if>
            <if test="baseGroupCode != null and baseGroupCode != ''">
                and t.BASE_GROUP_CODE = #{baseGroupCode}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
            and t.DUTY_DATE IS NOT NULL
        </where>
        order by t.DUTY_DATE ASC
    </select>

    <!--批量新增值日信息-->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_edu_duty (
        ID, DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, SEQ_WEEK, WEEKDAY, DUTY_DATE,
        DUTY_TYPE, TEACHER_NAME, TEACHER_ID, COLOR, IS_BASE_GROUP, BASE_GROUP_CODE, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (CONCAT('t_edu_duty_', UUID()), #{item.department}, #{item.departmentCode},
            #{item.year}, #{item.semester}, #{item.seqWeek}, #{item.weekday},
            #{item.dutyDate}, #{item.dutyType}, #{item.teacherName}, #{item.teacherId},
            #{item.color}, #{item.isBaseGroup}, #{item.baseGroupCode}, #{item.description},
            NOW(), #{item.createdBy}, NOW(), #{item.updatedBy})
        </foreach>
    </insert>

    <!--根据日期范围查询值日信息-->
    <select id="queryByDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_duty t
        <where>
            <if test="dutyDTO.department != null and dutyDTO.department != ''">
                and t.DEPARTMENT like CONCAT('%', #{dutyDTO.department}, '%')
            </if>
            <if test="dutyDTO.departmentCode != null and dutyDTO.departmentCode != ''">
                and t.DEPARTMENT_CODE = #{dutyDTO.departmentCode}
            </if>
            <if test="dutyDTO.year != null and dutyDTO.year != ''">
                and t.YEAR = #{dutyDTO.year}
            </if>
            <if test="dutyDTO.semester != null and dutyDTO.semester != ''">
                and t.SEMESTER = #{dutyDTO.semester}
            </if>
            <if test="dutyDTO.seqWeek != null and dutyDTO.seqWeek != ''">
                and t.SEQ_WEEK = #{dutyDTO.seqWeek}
            </if>
            <if test="dutyDTO.weekday != null and dutyDTO.weekday != ''">
                and t.WEEKDAY = #{dutyDTO.weekday}
            </if>
            <if test="dutyDTO.dutyType != null and dutyDTO.dutyType != ''">
                and t.DUTY_TYPE = #{dutyDTO.dutyType}
            </if>
            <if test="dutyDTO.teacherName != null and dutyDTO.teacherName != ''">
                and t.TEACHER_NAME like CONCAT('%', #{dutyDTO.teacherName}, '%')
            </if>
            <if test="dutyDTO.teacherId != null and dutyDTO.teacherId != ''">
                and t.TEACHER_ID = #{dutyDTO.teacherId}
            </if>
            <if test="dutyDTO.color != null and dutyDTO.color != ''">
                and t.COLOR = #{dutyDTO.color}
            </if>
            <if test="dutyDTO.isBaseGroup != null and dutyDTO.isBaseGroup != ''">
                and t.IS_BASE_GROUP = #{dutyDTO.isBaseGroup}
            </if>
            <if test="dutyDTO.baseGroupCode != null and dutyDTO.baseGroupCode != ''">
                and t.BASE_GROUP_CODE = #{dutyDTO.baseGroupCode}
            </if>
            <if test="dutyDTO.description != null and dutyDTO.description != ''">
                and t.DESCRIPTION like CONCAT('%', #{dutyDTO.description}, '%')
            </if>
            <if test="dutyDTO.createdBy != null and dutyDTO.createdBy != ''">
                and t.CREATED_BY = #{dutyDTO.createdBy}
            </if>
            <if test="dutyDTO.updatedBy != null and dutyDTO.updatedBy != ''">
                and t.UPDATED_BY = #{dutyDTO.updatedBy}
            </if>
            <!-- 日期范围查询 -->
            <if test="startDate != null and endDate != null">
                and t.DUTY_DATE BETWEEN #{startDate} AND #{endDate}
            </if>
            and t.DUTY_DATE IS NOT NULL
        </where>
    </select>

</mapper>
