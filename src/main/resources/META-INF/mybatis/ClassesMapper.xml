<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.ClassesMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.ClassesVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="GRADE_NAME" jdbcType="VARCHAR" property="gradeName"/>
        <result column="GRADE_CODE" jdbcType="VARCHAR" property="gradeCode"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="HEAD_TEACHER_ID" jdbcType="VARCHAR" property="headTeacherId"/>
        <result column="ASST_HEAD_TEACHER_ID" jdbcType="VARCHAR" property="asstHeadTeacherId"/>
        <result column="CLASSROOM_CODE" jdbcType="VARCHAR" property="classroomCode"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, DEPARTMENT, DEPARTMENT_CODE, GRADE_NAME, GRADE_CODE, NAME, CODE, HEAD_TEACHER_ID, ASST_HEAD_TEACHER_ID, 
        CLASSROOM_CODE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询班级信息-->
    <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_classes t
        WHERE t.ID = #{id}
    </select>

    <!--根据多个ID批量查询班级信息-->
    <select id="getByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_classes t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--查询班级信息-->
    <select id="query" parameterType="com.edu.www.dto.ClassesDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_classes t
        <where>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="gradeName != null and gradeName != ''">
                and t.GRADE_NAME like CONCAT('%', #{gradeName}, '%')
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                and t.GRADE_CODE = #{gradeCode}
            </if>
            <if test="name != null and name != ''">
                and t.NAME like CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                and t.CODE = #{code}
            </if>
            <if test="headTeacherId != null and headTeacherId != ''">
                and t.HEAD_TEACHER_ID = #{headTeacherId}
            </if>
            <if test="asstHeadTeacherId != null and asstHeadTeacherId != ''">
                and t.ASST_HEAD_TEACHER_ID = #{asstHeadTeacherId}
            </if>
            <if test="classroomCode != null and classroomCode != ''">
                and t.CLASSROOM_CODE = #{classroomCode}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.updated_at desc
    </select>

    <!--新增班级信息-->
    <insert id="insert" parameterType="com.edu.www.dto.ClassesDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_classes_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_classes (
            ID, DEPARTMENT, DEPARTMENT_CODE, GRADE_NAME, GRADE_CODE, NAME, CODE, 
            HEAD_TEACHER_ID, ASST_HEAD_TEACHER_ID, CLASSROOM_CODE, DESCRIPTION, 
            CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
            #{id}, #{department}, #{departmentCode}, #{gradeName}, #{gradeCode}, #{name}, #{code}, 
            #{headTeacherId}, #{asstHeadTeacherId}, #{classroomCode}, #{description}, 
            NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--更新班级信息-->
    <update id="update" parameterType="com.edu.www.dto.ClassesDTO">
        UPDATE t_edu_classes t
        <set>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="gradeName != null and gradeName != ''">
                t.GRADE_NAME = #{gradeName},
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                t.GRADE_CODE = #{gradeCode},
            </if>
            <if test="name != null">
                t.NAME = #{name},
            </if>
            <if test="code != null">
                t.CODE = #{code},
            </if>
            <if test="headTeacherId != null and headTeacherId != ''">
                t.HEAD_TEACHER_ID = #{headTeacherId},
            </if>
            <if test="asstHeadTeacherId != null">
                t.ASST_HEAD_TEACHER_ID = #{asstHeadTeacherId},
            </if>
            <if test="classroomCode != null">
                t.CLASSROOM_CODE = #{classroomCode},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!--根据ID删除班级信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_classes t
        WHERE t.ID = #{id}
    </delete>
</mapper> 