<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.StudentMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.StudentVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME_ZH" jdbcType="VARCHAR" property="nameZh"/>
        <result column="NAME_EN" jdbcType="VARCHAR" property="nameEn"/>
        <result column="STUDENT_CODE" jdbcType="VARCHAR" property="studentCode"/>
        <result column="GENDER" jdbcType="TINYINT" property="gender"/>
        <result column="BIRTH_DATE" jdbcType="DATE" property="birthDate"/>
        <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="CLASS_ID" jdbcType="VARCHAR" property="classId"/>
        <result column="ADMISSION_DATE" jdbcType="DATE" property="admissionDate"/>
        <result column="CARD_TYPE" jdbcType="VARCHAR" property="cardType"/>
        <result column="CARD_NUM" jdbcType="VARCHAR" property="cardNum"/>
        <result column="CARD_NAME_ZH" jdbcType="VARCHAR" property="cardNameZh"/>
        <result column="CARD_NAME_EN" jdbcType="VARCHAR" property="cardNameEn"/>
        <result column="PHONE_NUM" jdbcType="VARCHAR" property="phoneNum"/>
        <result column="IS_PAID" jdbcType="VARCHAR" property="isPaid"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="IS_EXAM" jdbcType="VARCHAR" property="isExam"/>
        <result column="IS_TRANSFERRED" jdbcType="VARCHAR" property="isTransferred"/>
        <result column="TRANSFER_IN_DATE" jdbcType="TIMESTAMP" property="transferInDate"/>
        <result column="MIDDLE_SCHOOL_NAME" jdbcType="VARCHAR" property="middleSchoolName"/>
        <result column="IS_BOARDING" jdbcType="VARCHAR" property="isBoarding"/>
        <result column="CONTACT_INFO" jdbcType="VARCHAR" property="contactInfo"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="PARENT_EMAIL" jdbcType="VARCHAR" property="parentEmail"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="ETHNICITY" jdbcType="VARCHAR" property="ethnicity"/>
        <result column="HEIGHT" jdbcType="DECIMAL" property="height"/>
        <result column="WEIGHT" jdbcType="DECIMAL" property="weight"/>
        <result column="BLOOD_TYPE" jdbcType="VARCHAR" property="bloodType"/>
        <result column="BIRTH_PLACE" jdbcType="VARCHAR" property="birthPlace"/>
        <result column="CLOTHING_SIZE" jdbcType="VARCHAR" property="clothingSize"/>
        <result column="SIZE_SITUATION" jdbcType="VARCHAR" property="sizeSituation"/>
        <result column="BIRTH_ADDRESS" jdbcType="VARCHAR" property="birthAddress"/>
        <result column="HOUSEHOLD_ADDRESS" jdbcType="VARCHAR" property="householdAddress"/>
        <result column="HOME_ADDRESS" jdbcType="VARCHAR" property="homeAddress"/>
        <result column="IS_ONLY_CHILD" jdbcType="VARCHAR" property="isOnlyChild"/>
        <result column="SITUATION" jdbcType="VARCHAR" property="situation"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        , NAME_ZH, NAME_EN, STUDENT_CODE, GENDER, BIRTH_DATE, DEPARTMENT_NAME, DEPARTMENT_CODE, CLASS_ID, ADMISSION_DATE,
        CARD_TYPE, CARD_NUM, CARD_NAME_ZH, CARD_NAME_EN, PHONE_NUM, 
        IS_PAID, STATUS, IS_EXAM, IS_TRANSFERRED, TRANSFER_IN_DATE, MIDDLE_SCHOOL_NAME, IS_BOARDING, CONTACT_INFO,
        EMAIL, PARENT_EMAIL, DESCRIPTION, ETHNICITY, HEIGHT, WEIGHT, BLOOD_TYPE, BIRTH_PLACE, CLOTHING_SIZE, SIZE_SITUATION,
        BIRTH_ADDRESS, HOUSEHOLD_ADDRESS, HOME_ADDRESS, IS_ONLY_CHILD, SITUATION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--查询学生信息-->
    <select id="query" parameterType="com.edu.www.dto.StudentDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_edu_student t
        <where>
            <if test="nameZh != null and nameZh != ''">
                and t.NAME_ZH like CONCAT('%', #{nameZh}, '%')
            </if>
            <if test="nameEn != null and nameEn != ''">
                and t.NAME_EN like CONCAT('%', #{nameEn}, '%')
            </if>
            <if test="studentCode != null and studentCode != ''">
                and t.STUDENT_CODE = #{studentCode}
            </if>
            <if test="gender != null">
                and t.GENDER = #{gender}
            </if>
            <if test="birthDate != null">
                and t.BIRTH_DATE = #{birthDate}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and t.DEPARTMENT_NAME like CONCAT('%', #{departmentName}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="classId != null and classId != ''">
                and t.CLASS_ID = #{classId}
            </if>
            <if test="admissionDate != null">
                and t.ADMISSION_DATE = #{admissionDate}
            </if>
            <if test="cardType != null and cardType != ''">
                and t.CARD_TYPE = #{cardType}
            </if>
            <if test="cardNum != null and cardNum != ''">
                and t.CARD_NUM = #{cardNum}
            </if>
            <if test="cardNameZh != null and cardNameZh != ''">
                and t.CARD_NAME_ZH like CONCAT('%', #{cardNameZh}, '%')
            </if>
            <if test="cardNameEn != null and cardNameEn != ''">
                and t.CARD_NAME_EN like CONCAT('%', #{cardNameEn}, '%')
            </if>
            <if test="phoneNum != null and phoneNum != ''">
                and t.PHONE_NUM = #{phoneNum}
            </if>
            <if test="isPaid != null and isPaid != ''">
                and t.IS_PAID = #{isPaid}
            </if>
            <if test="status != null and status != ''">
                and t.STATUS = #{status}
            </if>
            <if test="isExam != null and isExam != ''">
                and t.IS_EXAM = #{isExam}
            </if>
            <if test="isTransferred != null and isTransferred != ''">
                and t.IS_TRANSFERRED = #{isTransferred}
            </if>
            <if test="transferInDate != null">
                and t.TRANSFER_IN_DATE = #{transferInDate}
            </if>
            <if test="middleSchoolName != null and middleSchoolName != ''">
                and t.MIDDLE_SCHOOL_NAME like CONCAT('%', #{middleSchoolName}, '%')
            </if>
            <if test="isBoarding != null and isBoarding != ''">
                and t.IS_BOARDING = #{isBoarding}
            </if>
            <if test="contactInfo != null and contactInfo != ''">
                and t.CONTACT_INFO like CONCAT('%', #{contactInfo}, '%')
            </if>
            <if test="email != null and email != ''">
                and t.EMAIL = #{email}
            </if>
            <if test="parentEmail != null and parentEmail != ''">
                and t.PARENT_EMAIL = #{parentEmail}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="ethnicity != null and ethnicity != ''">
                and t.ETHNICITY like CONCAT('%', #{ethnicity}, '%')
            </if>
            <if test="height != null">
                and t.HEIGHT = #{height}
            </if>
            <if test="weight != null">
                and t.WEIGHT = #{weight}
            </if>
            <if test="bloodType != null and bloodType != ''">
                and t.BLOOD_TYPE = #{bloodType}
            </if>
            <if test="birthPlace != null and birthPlace != ''">
                and t.BIRTH_PLACE like CONCAT('%', #{birthPlace}, '%')
            </if>
            <if test="clothingSize != null and clothingSize != ''">
                and t.CLOTHING_SIZE = #{clothingSize}
            </if>

            <if test="sizeSituation != null and sizeSituation != ''">
                and t.SIZE_SITUATION = #{sizeSituation}
            </if>

            <if test="birthAddress != null and birthAddress != ''">
                and t.BIRTH_ADDRESS like CONCAT('%', #{birthAddress}, '%')
            </if>
            <if test="householdAddress != null and householdAddress != ''">
                and t.HOUSEHOLD_ADDRESS like CONCAT('%', #{householdAddress}, '%')
            </if>
            <if test="homeAddress != null and homeAddress != ''">
                and t.HOME_ADDRESS like CONCAT('%', #{homeAddress}, '%')
            </if>
            <if test="isOnlyChild != null and isOnlyChild != ''">
                and t.IS_ONLY_CHILD = #{isOnlyChild}
            </if>
            <if test="situation != null and situation != ''">
                and t.SITUATION like CONCAT('%', #{situation}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.updated_at desc
    </select>

    <!--新增学生信息-->
    <insert id="insert" parameterType="com.edu.www.dto.StudentDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_student_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_student (
        ID, NAME_ZH, NAME_EN, STUDENT_CODE, GENDER, BIRTH_DATE, DEPARTMENT_NAME, DEPARTMENT_CODE, CLASS_ID,
        ADMISSION_DATE, CARD_TYPE, CARD_NUM, CARD_NAME_ZH, CARD_NAME_EN,
        PHONE_NUM, IS_PAID, STATUS, IS_EXAM, IS_TRANSFERRED, TRANSFER_IN_DATE, MIDDLE_SCHOOL_NAME, IS_BOARDING,
        CONTACT_INFO, EMAIL, PARENT_EMAIL, DESCRIPTION, ETHNICITY, HEIGHT, WEIGHT, BLOOD_TYPE, BIRTH_PLACE,
        CLOTHING_SIZE, SIZE_SITUATION, BIRTH_ADDRESS, HOUSEHOLD_ADDRESS, HOME_ADDRESS, IS_ONLY_CHILD, SITUATION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{nameZh}, #{nameEn}, #{studentCode}, #{gender}, #{birthDate}, #{departmentName}, #{departmentCode},
        #{classId}, #{admissionDate}, #{cardType}, #{cardNum}, #{cardNameZh}, #{cardNameEn},
        #{phoneNum}, #{isPaid}, #{status}, #{isExam}, #{isTransferred}, #{transferInDate},
        #{middleSchoolName}, #{isBoarding}, #{contactInfo}, #{email}, #{parentEmail}, #{description},
        #{ethnicity}, #{height}, #{weight}, #{bloodType}, #{birthPlace}, #{clothingSize}, #{sizeSituation},
        #{birthAddress},
        #{householdAddress}, #{homeAddress}, #{isOnlyChild}, #{situation},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--根据ID查询学生信息-->
    <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_student t
        WHERE t.ID = #{id}
    </select>

    <!--根据多个ID批量查询学生信息-->
    <select id="getByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_student t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据条件查询学生信息数量 -->
    <select id="queryOne" parameterType="com.edu.www.dto.StudentDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_student t
        <where>
            <if test="nameZh != null and nameZh != ''">
                and t.NAME_ZH like CONCAT('%', #{nameZh}, '%')
            </if>
            <if test="nameEn != null and nameEn != ''">
                and t.NAME_EN like CONCAT('%', #{nameEn}, '%')
            </if>
            <if test="studentCode != null and studentCode != ''">
                and t.STUDENT_CODE = #{studentCode}
            </if>
            <if test="gender != null">
                and t.GENDER = #{gender}
            </if>
            <if test="birthDate != null">
                and t.BIRTH_DATE = #{birthDate}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and t.DEPARTMENT_NAME like CONCAT('%', #{departmentName}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="classId != null and classId != ''">
                and t.CLASS_ID = #{classId}
            </if>
            <if test="admissionDate != null">
                and t.ADMISSION_DATE = #{admissionDate}
            </if>
            <if test="cardType != null and cardType != ''">
                and t.CARD_TYPE = #{cardType}
            </if>
            <if test="cardNum != null and cardNum != ''">
                and t.CARD_NUM = #{cardNum}
            </if>
            <if test="cardNameZh != null and cardNameZh != ''">
                and t.CARD_NAME_ZH like CONCAT('%', #{cardNameZh}, '%')
            </if>
            <if test="cardNameEn != null and cardNameEn != ''">
                and t.CARD_NAME_EN like CONCAT('%', #{cardNameEn}, '%')
            </if>
            <if test="phoneNum != null and phoneNum != ''">
                and t.PHONE_NUM = #{phoneNum}
            </if>
            <if test="isPaid != null and isPaid != ''">
                and t.IS_PAID = #{isPaid}
            </if>
            <if test="status != null and status != ''">
                and t.STATUS = #{status}
            </if>
            <if test="isExam != null and isExam != ''">
                and t.IS_EXAM = #{isExam}
            </if>
            <if test="isTransferred != null and isTransferred != ''">
                and t.IS_TRANSFERRED = #{isTransferred}
            </if>
            <if test="transferInDate != null">
                and t.TRANSFER_IN_DATE = #{transferInDate}
            </if>
            <if test="middleSchoolName != null and middleSchoolName != ''">
                and t.MIDDLE_SCHOOL_NAME like CONCAT('%', #{middleSchoolName}, '%')
            </if>
            <if test="isBoarding != null and isBoarding != ''">
                and t.IS_BOARDING = #{isBoarding}
            </if>
            <if test="contactInfo != null and contactInfo != ''">
                and t.CONTACT_INFO like CONCAT('%', #{contactInfo}, '%')
            </if>
            <if test="email != null and email != ''">
                and t.EMAIL = #{email}
            </if>
            <if test="parentEmail != null and parentEmail != ''">
                and t.PARENT_EMAIL = #{parentEmail}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="ethnicity != null and ethnicity != ''">
                and t.ETHNICITY like CONCAT('%', #{ethnicity}, '%')
            </if>
            <if test="height != null">
                and t.HEIGHT = #{height}
            </if>
            <if test="weight != null">
                and t.WEIGHT = #{weight}
            </if>
            <if test="bloodType != null and bloodType != ''">
                and t.BLOOD_TYPE = #{bloodType}
            </if>
            <if test="birthPlace != null and birthPlace != ''">
                and t.BIRTH_PLACE like CONCAT('%', #{birthPlace}, '%')
            </if>
            <if test="clothingSize != null and clothingSize != ''">
                and t.CLOTHING_SIZE = #{clothingSize}
            </if>

            <if test="sizeSituation != null and sizeSituation != ''">
                and t.SIZE_SITUATION = #{sizeSituation}
            </if>

            <if test="birthAddress != null and birthAddress != ''">
                and t.BIRTH_ADDRESS like CONCAT('%', #{birthAddress}, '%')
            </if>
            <if test="householdAddress != null and householdAddress != ''">
                and t.HOUSEHOLD_ADDRESS like CONCAT('%', #{householdAddress}, '%')
            </if>
            <if test="homeAddress != null and homeAddress != ''">
                and t.HOME_ADDRESS like CONCAT('%', #{homeAddress}, '%')
            </if>
            <if test="isOnlyChild != null and isOnlyChild != ''">
                and t.IS_ONLY_CHILD = #{isOnlyChild}
            </if>
            <if test="situation != null and situation != ''">
                and t.SITUATION like CONCAT('%', #{situation}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        LIMIT 1
    </select>

    <!--更新学生信息-->
    <update id="update" parameterType="com.edu.www.dto.StudentDTO">
        UPDATE t_edu_student t
        <set>
            <if test="nameZh != null and nameZh != ''">
                t.NAME_ZH = #{nameZh},
            </if>
            <if test="nameEn != null">
                t.NAME_EN = #{nameEn},
            </if>
            <if test="studentCode != null and studentCode != ''">
                t.STUDENT_CODE = #{studentCode},
            </if>
            <if test="gender != null">
                t.GENDER = #{gender},
            </if>
            <if test="birthDate != null">
                t.BIRTH_DATE = #{birthDate},
            </if>
            <if test="departmentName != null and departmentName != ''">
                t.DEPARTMENT_NAME = #{departmentName},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="classId != null and classId != ''">
                t.CLASS_ID = #{classId},
            </if>
            <if test="admissionDate != null">
                t.ADMISSION_DATE = #{admissionDate},
            </if>
            <if test="cardType != null">
                t.CARD_TYPE = #{cardType},
            </if>
            <if test="cardNum != null">
                t.CARD_NUM = #{cardNum},
            </if>
            <if test="cardNameZh != null">
                t.CARD_NAME_ZH = #{cardNameZh},
            </if>
            <if test="cardNameEn != null">
                t.CARD_NAME_EN = #{cardNameEn},
            </if>
            <if test="phoneNum != null">
                t.PHONE_NUM = #{phoneNum},
            </if>
            <if test="isPaid != null and isPaid != ''">
                t.IS_PAID = #{isPaid},
            </if>
            <if test="status != null and status != ''">
                t.STATUS = #{status},
            </if>
            <if test="isExam != null and isExam != ''">
                t.IS_EXAM = #{isExam},
            </if>
            <if test="isTransferred != null and isTransferred != ''">
                t.IS_TRANSFERRED = #{isTransferred},
            </if>
            <choose>
                <when test="transferInDate != null">
                    t.TRANSFER_IN_DATE = #{transferInDate},
                </when>
                <otherwise>
                    t.TRANSFER_IN_DATE = NULL,
                </otherwise>
            </choose>
            <if test="middleSchoolName != null">
                t.MIDDLE_SCHOOL_NAME = #{middleSchoolName},
            </if>
            <if test="isBoarding != null">
                t.IS_BOARDING = #{isBoarding},
            </if>
            <if test="contactInfo != null">
                t.CONTACT_INFO = #{contactInfo},
            </if>
            <if test="email != null">
                t.EMAIL = #{email},
            </if>
            <if test="parentEmail != null">
                t.PARENT_EMAIL = #{parentEmail},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="ethnicity != null">
                t.ETHNICITY = #{ethnicity},
            </if>
            <if test="height != null">
                t.HEIGHT = #{height},
            </if>
            <if test="weight != null">
                t.WEIGHT = #{weight},
            </if>
            <if test="bloodType != null">
                t.BLOOD_TYPE = #{bloodType},
            </if>
            <if test="birthPlace != null">
                t.BIRTH_PLACE = #{birthPlace},
            </if>
            <if test="clothingSize != null">
                t.CLOTHING_SIZE = #{clothingSize},
            </if>

            <choose>
                <when test="sizeSituation != null">
                    t.SIZE_SITUATION = #{sizeSituation},
                </when>
                <otherwise>
                    t.SIZE_SITUATION = NULL,
                </otherwise>
            </choose>

            <if test="birthAddress != null">
                t.BIRTH_ADDRESS = #{birthAddress},
            </if>
            <if test="householdAddress != null">
                t.HOUSEHOLD_ADDRESS = #{householdAddress},
            </if>
            <if test="homeAddress != null">
                t.HOME_ADDRESS = #{homeAddress},
            </if>
            <if test="isOnlyChild != null and isOnlyChild != ''">
                t.IS_ONLY_CHILD = #{isOnlyChild},
            </if>
            <choose>
                <when test="situation != null">
                    t.SITUATION = #{situation},
                </when>
                <otherwise>
                    t.SITUATION = NULL,
                </otherwise>
            </choose>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!--根据ID删除学生信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_student t
        WHERE t.ID = #{id}
    </delete>
</mapper>