<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.SupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.SupplierVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="SUPPLIER_CODE" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="SUPPLIER_TYPE" jdbcType="VARCHAR" property="supplierType"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_PHONE" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="CONTACT_EMAIL" jdbcType="VARCHAR" property="contactEmail"/>
        <result column="CONTACT_ADDRESS" jdbcType="VARCHAR" property="contactAddress"/>
        <result column="BUSINESS_LICENSE" jdbcType="VARCHAR" property="businessLicense"/>
        <result column="BANK_NAME" jdbcType="VARCHAR" property="bankName"/>
        <result column="TAX_NUMBER" jdbcType="VARCHAR" property="taxNumber"/>
        <result column="CREDIT_RATING" jdbcType="VARCHAR" property="creditRating"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, SUPPLIER_NAME, SUPPLIER_CODE, SUPPLIER_TYPE, CONTACT_NAME, CONTACT_PHONE,
        CONTACT_EMAIL, CONTACT_ADDRESS, BUSINESS_LICENSE, BANK_NAME, TAX_NUMBER,
        CREDIT_RATING, STATUS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询供应商信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_supplier t
        WHERE t.ID = #{id}
    </select>

    <!--查询供应商信息-->
    <select id="query" parameterType="com.edu.www.dto.SupplierDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_supplier t
        <where>
            <if test="supplierName != null and supplierName != ''">
                and t.SUPPLIER_NAME like CONCAT('%', #{supplierName}, '%')
            </if>
            <if test="supplierCode != null and supplierCode != ''">
                and t.SUPPLIER_CODE like CONCAT('%', #{supplierCode}, '%')
            </if>
            <if test="supplierType != null and supplierType != ''">
                and t.SUPPLIER_TYPE = #{supplierType}
            </if>
            <if test="contactName != null and contactName != ''">
                and t.CONTACT_NAME like CONCAT('%', #{contactName}, '%')
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                and t.CONTACT_PHONE like CONCAT('%', #{contactPhone}, '%')
            </if>
            <if test="creditRating != null and creditRating != ''">
                and t.CREDIT_RATING = #{creditRating}
            </if>
            <if test="status != null and status != ''">
                and t.STATUS = #{status}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增供应商信息-->
    <insert id="insert" parameterType="com.edu.www.dto.SupplierDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_supplier_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_supplier (
        ID, SUPPLIER_NAME, SUPPLIER_CODE, SUPPLIER_TYPE, CONTACT_NAME, CONTACT_PHONE,
        CONTACT_EMAIL, CONTACT_ADDRESS, BUSINESS_LICENSE, BANK_NAME, TAX_NUMBER,
        CREDIT_RATING, STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{supplierName}, #{supplierCode}, #{supplierType}, #{contactName}, #{contactPhone},
        #{contactEmail}, #{contactAddress}, #{businessLicense}, #{bankName}, #{taxNumber},
        #{creditRating}, #{status}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改供应商信息-->
    <update id="update" parameterType="com.edu.www.dto.SupplierDTO">
        UPDATE t_edu_supplier t
        <set>
            <if test="supplierName != null and supplierName != ''">
                t.SUPPLIER_NAME = #{supplierName},
            </if>
            <if test="supplierCode != null and supplierCode != ''">
                t.SUPPLIER_CODE = #{supplierCode},
            </if>
            <if test="supplierType != null and supplierType != ''">
                t.SUPPLIER_TYPE = #{supplierType},
            </if>
            <if test="contactName != null and contactName != ''">
                t.CONTACT_NAME = #{contactName},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                t.CONTACT_PHONE = #{contactPhone},
            </if>
            <if test="contactEmail != null and contactEmail != ''">
                t.CONTACT_EMAIL = #{contactEmail},
            </if>
            <if test="contactAddress != null and contactAddress != ''">
                t.CONTACT_ADDRESS = #{contactAddress},
            </if>
            <if test="businessLicense != null and businessLicense != ''">
                t.BUSINESS_LICENSE = #{businessLicense},
            </if>
            <if test="bankName != null and bankName != ''">
                t.BANK_NAME = #{bankName},
            </if>
            <if test="taxNumber != null and taxNumber != ''">
                t.TAX_NUMBER = #{taxNumber},
            </if>
            <if test="creditRating != null and creditRating != ''">
                t.CREDIT_RATING = #{creditRating},
            </if>
            <if test="status != null and status != ''">
                t.STATUS = #{status},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除供应商信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_supplier WHERE ID = #{id}
    </delete>

    <!--根据供应商编码查询数量-->
    <select id="getByCodeCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_edu_supplier WHERE SUPPLIER_CODE = #{supplierCode}
    </select>

    <!--查询所有供应商列表-->
    <select id="getAll" resultMap="BaseResultMap">
        SELECT t.ID, t.SUPPLIER_NAME
        FROM t_edu_supplier t
        WHERE t.STATUS = '1'
        ORDER BY t.SUPPLIER_NAME ASC
    </select>

</mapper>
