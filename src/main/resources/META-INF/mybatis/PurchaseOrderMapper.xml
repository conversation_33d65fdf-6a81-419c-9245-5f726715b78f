<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.PurchaseOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.PurchaseOrderVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="YEAR" jdbcType="VARCHAR" property="year"/>
        <result column="SEMESTER" jdbcType="VARCHAR" property="semester"/>
        <result column="PURCHASE_TITLE" jdbcType="VARCHAR" property="purchaseTitle"/>
        <result column="PURCHASE_NO" jdbcType="VARCHAR" property="purchaseNo"/>
        <result column="PURCHASE_DATE" jdbcType="DATE" property="purchaseDate"/>
        <result column="REQUESTER_NAME" jdbcType="VARCHAR" property="requesterName"/>
        <result column="BOOK_CATEGORY" jdbcType="VARCHAR" property="bookCategory"/>
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
        <result column="PAYMENT_METHOD" jdbcType="VARCHAR" property="paymentMethod"/>
        <result column="PURCHASE_STATUS" jdbcType="VARCHAR" property="purchaseStatus"/>
        <result column="IS_NEED_CONTRACT" jdbcType="VARCHAR" property="isNeedContract"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="EXPECTED_DELIVERY_DATE" jdbcType="DATE" property="expectedDeliveryDate"/>
        <result column="ACTUAL_DELIVERY_DATE" jdbcType="DATE" property="actualDeliveryDate"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, PURCHASE_TITLE, PURCHASE_NO, PURCHASE_DATE,
        REQUESTER_NAME, BOOK_CATEGORY, SUPPLIER_ID, TOTAL_AMOUNT, CURRENCY, PAYMENT_METHOD,
        PURCHASE_STATUS, IS_NEED_CONTRACT, CONTRACT_NO, EXPECTED_DELIVERY_DATE,
        ACTUAL_DELIVERY_DATE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询采购订单信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_purchase_order t
        WHERE t.ID = #{id}
    </select>

    <!--根据采购订单号查询数量-->
    <select id="getByCodeCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_edu_purchase_order
        WHERE PURCHASE_NO = #{purchaseOrderNo}
    </select>

    <!--查询采购订单信息-->
    <select id="query" parameterType="com.edu.www.dto.PurchaseOrderDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_purchase_order t
        <where>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="purchaseTitle != null and purchaseTitle != ''">
                and t.PURCHASE_TITLE like CONCAT('%', #{purchaseTitle}, '%')
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                and t.PURCHASE_NO like CONCAT('%', #{purchaseNo}, '%')
            </if>
            <if test="requesterName != null and requesterName != ''">
                and t.REQUESTER_NAME like CONCAT('%', #{requesterName}, '%')
            </if>
            <if test="bookCategory != null and bookCategory != ''">
                and t.BOOK_CATEGORY = #{bookCategory}
            </if>
            <if test="supplierId != null and supplierId != ''">
                and t.SUPPLIER_ID = #{supplierId}
            </if>
            <if test="currency != null and currency != ''">
                and t.CURRENCY = #{currency}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and t.PAYMENT_METHOD = #{paymentMethod}
            </if>
            <if test="purchaseStatus != null and purchaseStatus != ''">
                and t.PURCHASE_STATUS = #{purchaseStatus}
            </if>
            <if test="isNeedContract != null and isNeedContract != ''">
                and t.IS_NEED_CONTRACT = #{isNeedContract}
            </if>
            <if test="contractNo != null and contractNo != ''">
                and t.CONTRACT_NO = #{contractNo}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增采购订单信息-->
    <insert id="insert" parameterType="com.edu.www.dto.PurchaseOrderDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_purchase_order_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_purchase_order (
        ID, DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, PURCHASE_TITLE, PURCHASE_NO, PURCHASE_DATE,
        REQUESTER_NAME, BOOK_CATEGORY, SUPPLIER_ID, TOTAL_AMOUNT, CURRENCY, PAYMENT_METHOD,
        PURCHASE_STATUS, IS_NEED_CONTRACT, CONTRACT_NO, EXPECTED_DELIVERY_DATE,
        ACTUAL_DELIVERY_DATE, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{department}, #{departmentCode}, #{year}, #{semester}, #{purchaseTitle}, #{purchaseNo}, #{purchaseDate},
        #{requesterName}, #{bookCategory}, #{supplierId}, #{totalAmount}, #{currency}, #{paymentMethod},
        #{purchaseStatus}, #{isNeedContract}, #{contractNo}, #{expectedDeliveryDate},
        #{actualDeliveryDate}, #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改采购订单信息-->
    <update id="update" parameterType="com.edu.www.dto.PurchaseOrderDTO">
        UPDATE t_edu_purchase_order t
        <set>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="year != null and year != ''">
                t.YEAR = #{year},
            </if>
            <if test="semester != null and semester != ''">
                t.SEMESTER = #{semester},
            </if>
            <if test="purchaseTitle != null and purchaseTitle != ''">
                t.PURCHASE_TITLE = #{purchaseTitle},
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                t.PURCHASE_NO = #{purchaseNo},
            </if>
            <if test="purchaseDate != null">
                t.PURCHASE_DATE = #{purchaseDate},
            </if>
            <if test="requesterName != null and requesterName != ''">
                t.REQUESTER_NAME = #{requesterName},
            </if>
            <if test="bookCategory != null and bookCategory != ''">
                t.BOOK_CATEGORY = #{bookCategory},
            </if>
            <if test="supplierId != null and supplierId != ''">
                t.SUPPLIER_ID = #{supplierId},
            </if>
            <if test="totalAmount != null">
                t.TOTAL_AMOUNT = #{totalAmount},
            </if>
            <if test="currency != null and currency != ''">
                t.CURRENCY = #{currency},
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                t.PAYMENT_METHOD = #{paymentMethod},
            </if>
            <if test="purchaseStatus != null and purchaseStatus != ''">
                t.PURCHASE_STATUS = #{purchaseStatus},
            </if>
            <if test="isNeedContract != null and isNeedContract != ''">
                t.IS_NEED_CONTRACT = #{isNeedContract},
            </if>
            <if test="contractNo != null and contractNo != ''">
                t.CONTRACT_NO = #{contractNo},
            </if>
            <if test="expectedDeliveryDate != null">
                t.EXPECTED_DELIVERY_DATE = #{expectedDeliveryDate},
            </if>
            <if test="actualDeliveryDate != null">
                t.ACTUAL_DELIVERY_DATE = #{actualDeliveryDate},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除采购订单信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_purchase_order
        WHERE ID = #{id}
    </delete>

</mapper>
