<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.PurchaseContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.PurchaseContractVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="PURCHASE_ID" jdbcType="VARCHAR" property="purchaseId"/>
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId"/>
        <result column="CONTRACT_TITLE" jdbcType="VARCHAR" property="contractTitle"/>
        <result column="CONTRACT_AMOUNT" jdbcType="DECIMAL" property="contractAmount"/>
        <result column="PREPAYMENT_AMOUNT" jdbcType="DECIMAL" property="prepaymentAmount"/>
        <result column="PREPAYMENT_RATIO" jdbcType="DECIMAL" property="prepaymentRatio"/>
        <result column="CONTRACT_STATUS" jdbcType="VARCHAR" property="contractStatus"/>
        <result column="OA_PROCESS_ID" jdbcType="VARCHAR" property="oaProcessId"/>
        <result column="CONTRACT_FILE_PATH" jdbcType="VARCHAR" property="contractFilePath"/>
        <result column="SIGN_DATE" jdbcType="DATE" property="signDate"/>
        <result column="EFFECTIVE_DATE" jdbcType="DATE" property="effectiveDate"/>
        <result column="EXPIRY_DATE" jdbcType="DATE" property="expiryDate"/>
        <result column="PAYMENT_TERMS" jdbcType="VARCHAR" property="paymentTerms"/>
        <result column="DELIVERY_TERMS" jdbcType="VARCHAR" property="deliveryTerms"/>
        <result column="QUALITY_STANDARDS" jdbcType="VARCHAR" property="qualityStandards"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, CONTRACT_NO, PURCHASE_ID, SUPPLIER_ID, CONTRACT_TITLE, CONTRACT_AMOUNT,
        PREPAYMENT_AMOUNT, PREPAYMENT_RATIO, CONTRACT_STATUS, OA_PROCESS_ID, CONTRACT_FILE_PATH,
        SIGN_DATE, EFFECTIVE_DATE, EXPIRY_DATE, PAYMENT_TERMS, DELIVERY_TERMS,
        QUALITY_STANDARDS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询采购合同信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_purchase_contract t
        WHERE t.ID = #{id}
    </select>

    <!--查询采购合同信息-->
    <select id="query" parameterType="com.edu.www.dto.PurchaseContractDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_purchase_contract t
        <where>
            <if test="contractNo != null and contractNo != ''">
                and t.CONTRACT_NO like CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="purchaseId != null and purchaseId != ''">
                and t.PURCHASE_ID = #{purchaseId}
            </if>
            <if test="supplierId != null and supplierId != ''">
                and t.SUPPLIER_ID = #{supplierId}
            </if>
            <if test="contractTitle != null and contractTitle != ''">
                and t.CONTRACT_TITLE like CONCAT('%', #{contractTitle}, '%')
            </if>
            <if test="contractStatus != null and contractStatus != ''">
                and t.CONTRACT_STATUS = #{contractStatus}
            </if>
            <if test="oaProcessId != null and oaProcessId != ''">
                and t.OA_PROCESS_ID = #{oaProcessId}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增采购合同信息-->
    <insert id="insert" parameterType="com.edu.www.dto.PurchaseContractDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_purchase_contract_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_purchase_contract (
        ID, CONTRACT_NO, PURCHASE_ID, SUPPLIER_ID, CONTRACT_TITLE, CONTRACT_AMOUNT,
        PREPAYMENT_AMOUNT, PREPAYMENT_RATIO, CONTRACT_STATUS, OA_PROCESS_ID, CONTRACT_FILE_PATH,
        SIGN_DATE, EFFECTIVE_DATE, EXPIRY_DATE, PAYMENT_TERMS, DELIVERY_TERMS,
        QUALITY_STANDARDS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{contractNo}, #{purchaseId}, #{supplierId}, #{contractTitle}, #{contractAmount},
        #{prepaymentAmount}, #{prepaymentRatio}, #{contractStatus}, #{oaProcessId}, #{contractFilePath},
        #{signDate}, #{effectiveDate}, #{expiryDate}, #{paymentTerms}, #{deliveryTerms},
        #{qualityStandards}, #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改采购合同信息-->
    <update id="update" parameterType="com.edu.www.dto.PurchaseContractDTO">
        UPDATE t_edu_purchase_contract t
        <set>
            <if test="contractNo != null and contractNo != ''">
                t.CONTRACT_NO = #{contractNo},
            </if>
            <if test="purchaseId != null and purchaseId != ''">
                t.PURCHASE_ID = #{purchaseId},
            </if>
            <if test="supplierId != null and supplierId != ''">
                t.SUPPLIER_ID = #{supplierId},
            </if>
            <if test="contractTitle != null and contractTitle != ''">
                t.CONTRACT_TITLE = #{contractTitle},
            </if>
            <if test="contractAmount != null">
                t.CONTRACT_AMOUNT = #{contractAmount},
            </if>
            <if test="prepaymentAmount != null">
                t.PREPAYMENT_AMOUNT = #{prepaymentAmount},
            </if>
            <if test="prepaymentRatio != null">
                t.PREPAYMENT_RATIO = #{prepaymentRatio},
            </if>
            <if test="contractStatus != null and contractStatus != ''">
                t.CONTRACT_STATUS = #{contractStatus},
            </if>
            <if test="oaProcessId != null and oaProcessId != ''">
                t.OA_PROCESS_ID = #{oaProcessId},
            </if>
            <if test="contractFilePath != null and contractFilePath != ''">
                t.CONTRACT_FILE_PATH = #{contractFilePath},
            </if>
            <if test="signDate != null">
                t.SIGN_DATE = #{signDate},
            </if>
            <if test="effectiveDate != null">
                t.EFFECTIVE_DATE = #{effectiveDate},
            </if>
            <if test="expiryDate != null">
                t.EXPIRY_DATE = #{expiryDate},
            </if>
            <if test="paymentTerms != null and paymentTerms != ''">
                t.PAYMENT_TERMS = #{paymentTerms},
            </if>
            <if test="deliveryTerms != null and deliveryTerms != ''">
                t.DELIVERY_TERMS = #{deliveryTerms},
            </if>
            <if test="qualityStandards != null and qualityStandards != ''">
                t.QUALITY_STANDARDS = #{qualityStandards},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除采购合同信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_purchase_contract WHERE ID = #{id}
    </delete>

</mapper>
