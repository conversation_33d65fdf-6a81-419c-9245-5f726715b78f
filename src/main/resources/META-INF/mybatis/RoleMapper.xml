<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.RoleMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.RoleVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        ID
        , NAME, CODE, STATUS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!-- 根据ID查询角色信息 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_role
        WHERE ID = #{id}
    </select>

    <!--查询角色编码是否存在-->
    <select id="existsByCode" parameterType="java.lang.String" resultType="java.lang.Boolean">
        select count(*) > 0
        from t_edu_role t
        where t.CODE = #{code}
    </select>

    <!-- 查询角色列表 -->
    <select id="query" parameterType="com.edu.www.dto.RoleDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_role t
        <where>
            <if test="name != null and name != ''">
                AND t.NAME LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND t.CODE LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="status != null">
                AND t.STATUS = #{status}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="queryByUserId" resultMap="BaseResultMap">
        SELECT r.*
        FROM t_edu_role r
                 INNER JOIN t_edu_user_role ur ON r.ID = ur.ROLE_ID
        WHERE ur.USER_ID = #{userId}
          AND r.STATUS = 1
    </select>

    <!-- 根据权限ID查询拥有该权限的角色列表 -->
    <select id="queryByPermissionId" resultMap="BaseResultMap">
        SELECT r.*
        FROM t_edu_role r
                 INNER JOIN t_edu_role_permission rp ON r.ID = rp.ROLE_ID
        WHERE rp.PERMISSION_ID = #{permissionId}
          AND r.STATUS = 1
    </select>

    <!-- 新增角色 -->
    <insert id="insert" parameterType="com.edu.www.dto.RoleDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_role_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_role(ID, NAME, CODE, STATUS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
        VALUES (#{id}, #{name}, #{code}, #{status}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy})
    </insert>

    <!-- 更新角色 -->
    <update id="update" parameterType="com.edu.www.dto.RoleDTO">
        UPDATE t_edu_role
        <set>
            <if test="name != null">NAME = #{name},</if>
            <if test="code != null">CODE = #{code},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 删除角色 -->
    <delete id="delete">
        DELETE
        FROM t_edu_role
        WHERE ID = #{id}
    </delete>
</mapper> 