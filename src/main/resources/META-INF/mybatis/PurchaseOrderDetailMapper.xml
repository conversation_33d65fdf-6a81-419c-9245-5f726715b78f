<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.PurchaseOrderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.PurchaseOrderDetailVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PURCHASE_NO" jdbcType="VARCHAR" property="purchaseNo"/>
        <result column="BOOK_NAME" jdbcType="VARCHAR" property="bookName"/>
        <result column="ISBN" jdbcType="VARCHAR" property="isbn"/>
        <result column="AUTHOR" jdbcType="VARCHAR" property="author"/>
        <result column="PUBLISHER" jdbcType="VARCHAR" property="publisher"/>
        <result column="PUBLICATION_DATE" jdbcType="DATE" property="publicationDate"/>
        <result column="UNIT_PRICE" jdbcType="DECIMAL" property="unitPrice"/>
        <result column="QUANTITY" jdbcType="INTEGER" property="quantity"/>
        <result column="TOTAL_PRICE" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="BOOK_TYPE" jdbcType="VARCHAR" property="bookType"/>
        <result column="SUBJECT_CODE" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="GRADE_LEVEL" jdbcType="VARCHAR" property="gradeLevel"/>
        <result column="URGENT_LEVEL" jdbcType="VARCHAR" property="urgentLevel"/>
        <result column="RECEIVED_QUANTITY" jdbcType="INTEGER" property="receivedQuantity"/>
        <result column="QUALITY_STATUS" jdbcType="VARCHAR" property="qualityStatus"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, PURCHASE_NO, BOOK_NAME, ISBN, AUTHOR, PUBLISHER, PUBLICATION_DATE,
        UNIT_PRICE, QUANTITY, TOTAL_PRICE, BOOK_TYPE, SUBJECT_CODE, GRADE_LEVEL,
        URGENT_LEVEL, RECEIVED_QUANTITY, QUALITY_STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询采购明细信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_purchase_order_detail t
        WHERE t.ID = #{id}
    </select>

    <!--查询采购明细信息-->
    <select id="query" parameterType="com.edu.www.dto.PurchaseOrderDetailDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_purchase_order_detail t
        <where>
            <if test="purchaseNo != null and purchaseNo != ''">
                and t.PURCHASE_NO = #{purchaseNo}
            </if>
            <if test="bookName != null and bookName != ''">
                and t.BOOK_NAME like CONCAT('%', #{bookName}, '%')
            </if>
            <if test="isbn != null and isbn != ''">
                and t.ISBN like CONCAT('%', #{isbn}, '%')
            </if>
            <if test="author != null and author != ''">
                and t.AUTHOR like CONCAT('%', #{author}, '%')
            </if>
            <if test="publisher != null and publisher != ''">
                and t.PUBLISHER like CONCAT('%', #{publisher}, '%')
            </if>
            <if test="bookType != null and bookType != ''">
                and t.BOOK_TYPE = #{bookType}
            </if>
            <if test="subjectCode != null and subjectCode != ''">
                and t.SUBJECT_CODE = #{subjectCode}
            </if>
            <if test="gradeLevel != null and gradeLevel != ''">
                and t.GRADE_LEVEL = #{gradeLevel}
            </if>
            <if test="urgentLevel != null and urgentLevel != ''">
                and t.URGENT_LEVEL = #{urgentLevel}
            </if>
            <if test="qualityStatus != null and qualityStatus != ''">
                and t.QUALITY_STATUS = #{qualityStatus}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增采购明细信息-->
    <insert id="insert" parameterType="com.edu.www.dto.PurchaseOrderDetailDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_purchase_order_detail_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_purchase_order_detail (
        ID, PURCHASE_NO, BOOK_NAME, ISBN, AUTHOR, PUBLISHER, PUBLICATION_DATE,
        UNIT_PRICE, QUANTITY, TOTAL_PRICE, BOOK_TYPE, SUBJECT_CODE, GRADE_LEVEL,
        URGENT_LEVEL, RECEIVED_QUANTITY, QUALITY_STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{purchaseNo}, #{bookName}, #{isbn}, #{author}, #{publisher}, #{publicationDate},
        #{unitPrice}, #{quantity}, #{totalPrice}, #{bookType}, #{subjectCode}, #{gradeLevel},
        #{urgentLevel}, #{receivedQuantity}, #{qualityStatus}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改采购明细信息-->
    <update id="update" parameterType="com.edu.www.dto.PurchaseOrderDetailDTO">
        UPDATE t_edu_purchase_order_detail t
        <set>
            <if test="purchaseNo != null and purchaseNo != ''">
                t.PURCHASE_NOPURCHASE_NO = #{purchaseNo},
            </if>
            <if test="bookName != null and bookName != ''">
                t.BOOK_NAME = #{bookName},
            </if>
            <if test="isbn != null and isbn != ''">
                t.ISBN = #{isbn},
            </if>
            <if test="author != null and author != ''">
                t.AUTHOR = #{author},
            </if>
            <if test="publisher != null and publisher != ''">
                t.PUBLISHER = #{publisher},
            </if>
            <if test="publicationDate != null">
                t.PUBLICATION_DATE = #{publicationDate},
            </if>
            <if test="unitPrice != null">
                t.UNIT_PRICE = #{unitPrice},
            </if>
            <if test="quantity != null">
                t.QUANTITY = #{quantity},
            </if>
            <if test="totalPrice != null">
                t.TOTAL_PRICE = #{totalPrice},
            </if>
            <if test="bookType != null and bookType != ''">
                t.BOOK_TYPE = #{bookType},
            </if>
            <if test="subjectCode != null and subjectCode != ''">
                t.SUBJECT_CODE = #{subjectCode},
            </if>
            <if test="gradeLevel != null and gradeLevel != ''">
                t.GRADE_LEVEL = #{gradeLevel},
            </if>
            <if test="urgentLevel != null and urgentLevel != ''">
                t.URGENT_LEVEL = #{urgentLevel},
            </if>
            <if test="receivedQuantity != null">
                t.RECEIVED_QUANTITY = #{receivedQuantity},
            </if>
            <if test="qualityStatus != null and qualityStatus != ''">
                t.QUALITY_STATUS = #{qualityStatus},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除采购明细信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_purchase_order_detail WHERE ID = #{id}
    </delete>

</mapper>
