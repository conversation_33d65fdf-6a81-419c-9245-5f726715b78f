<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.StudentSubjectMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.StudentSubjectVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="STUDENT_ID" jdbcType="VARCHAR" property="studentId"/>
        <result column="SUBJECT" jdbcType="VARCHAR" property="subject"/>
        <result column="SUBJECT_CODE" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="SUBJECT_TYPE" jdbcType="VARCHAR" property="subjectType"/>
        <result column="SUBJECT_LEVEL" jdbcType="VARCHAR" property="subjectLevel"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, STUDENT_ID, SUBJECT, SUBJECT_CODE, SUBJECT_TYPE, SUBJECT_LEVEL, DESCRIPTION, 
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--查询学生学科信息列表-->
    <select id="query" parameterType="com.edu.www.dto.StudentSubjectDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_student_subject t
        <where>
            <if test="studentId != null and studentId != ''">
                and t.STUDENT_ID = #{studentId}
            </if>
            <if test="subject != null and subject != ''">
                and t.SUBJECT like CONCAT('%', #{subject}, '%')
            </if>
            <if test="subjectCode != null and subjectCode != ''">
                and t.SUBJECT_CODE = #{subjectCode}
            </if>
            <if test="subjectType != null and subjectType != ''">
                and t.SUBJECT_TYPE = #{subjectType}
            </if>
            <if test="subjectLevel != null and subjectLevel != ''">
                and t.SUBJECT_LEVEL = #{subjectLevel}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.UPDATED_AT desc
    </select>

    <!--新增学生学科信息-->
    <insert id="insert" parameterType="com.edu.www.dto.StudentSubjectDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_student_subject_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_student_subject (
            ID, STUDENT_ID, SUBJECT, SUBJECT_CODE, SUBJECT_TYPE, SUBJECT_LEVEL, DESCRIPTION,
            CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
            #{id}, #{studentId}, #{subject}, #{subjectCode}, #{subjectType}, #{subjectLevel}, #{description},
            NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--根据ID查询学生学科信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_student_subject t
        WHERE t.ID = #{id}
    </select>

    <!--更新学生学科信息-->
    <update id="update" parameterType="com.edu.www.dto.StudentSubjectDTO">
        UPDATE t_edu_student_subject t
        <set>
            <if test="studentId != null and studentId != ''">
                t.STUDENT_ID = #{studentId},
            </if>
            <if test="subject != null and subject != ''">
                t.SUBJECT = #{subject},
            </if>
            <if test="subjectCode != null and subjectCode != ''">
                t.SUBJECT_CODE = #{subjectCode},
            </if>
            <if test="subjectType != null">
                t.SUBJECT_TYPE = #{subjectType},
            </if>
            <if test="subjectLevel != null and subjectLevel != ''">
                t.SUBJECT_LEVEL = #{subjectLevel},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除学生学科信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_student_subject t
        WHERE t.ID = #{id}
    </delete>

    <!-- 学生信息结果映射 -->
    <resultMap id="StudentResultMap" type="com.edu.www.vo.StudentVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME_ZH" jdbcType="VARCHAR" property="nameZh"/>
        <result column="NAME_EN" jdbcType="VARCHAR" property="nameEn"/>
        <result column="STUDENT_CODE" jdbcType="VARCHAR" property="studentCode"/>
        <result column="GENDER" jdbcType="TINYINT" property="gender"/>
        <result column="BIRTH_DATE" jdbcType="DATE" property="birthDate"/>
        <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="CLASS_ID" jdbcType="VARCHAR" property="classId"/>
        <result column="ADMISSION_DATE" jdbcType="DATE" property="admissionDate"/>
        <result column="CARD_TYPE" jdbcType="VARCHAR" property="cardType"/>
        <result column="CARD_NUM" jdbcType="VARCHAR" property="cardNum"/>
        <result column="CARD_NAME_ZH" jdbcType="VARCHAR" property="cardNameZh"/>
        <result column="CARD_NAME_EN" jdbcType="VARCHAR" property="cardNameEn"/>
        <result column="PHONE_NUM" jdbcType="VARCHAR" property="phoneNum"/>
        <result column="IS_PAID" jdbcType="VARCHAR" property="isPaid"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="IS_EXAM" jdbcType="VARCHAR" property="isExam"/>
        <result column="IS_TRANSFERRED" jdbcType="VARCHAR" property="isTransferred"/>
        <result column="TRANSFER_IN_DATE" jdbcType="TIMESTAMP" property="transferInDate"/>
        <result column="MIDDLE_SCHOOL_NAME" jdbcType="VARCHAR" property="middleSchoolName"/>
        <result column="IS_BOARDING" jdbcType="VARCHAR" property="isBoarding"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="PARENT_EMAIL" jdbcType="VARCHAR" property="parentEmail"/>
        <result column="CONTACT_INFO" jdbcType="LONGVARCHAR" property="contactInfo"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="ETHNICITY" jdbcType="VARCHAR" property="ethnicity"/>
        <result column="HEIGHT" jdbcType="DECIMAL" property="height"/>
        <result column="WEIGHT" jdbcType="DECIMAL" property="weight"/>
        <result column="BLOOD_TYPE" jdbcType="VARCHAR" property="bloodType"/>
        <result column="BIRTH_PLACE" jdbcType="VARCHAR" property="birthPlace"/>
        <result column="CLOTHING_SIZE" jdbcType="VARCHAR" property="clothingSize"/>
        <result column="SIZE_SITUATION" jdbcType="VARCHAR" property="sizeSituation"/>
        <result column="BIRTH_ADDRESS" jdbcType="VARCHAR" property="birthAddress"/>
        <result column="HOUSEHOLD_ADDRESS" jdbcType="VARCHAR" property="householdAddress"/>
        <result column="HOME_ADDRESS" jdbcType="VARCHAR" property="homeAddress"/>
        <result column="IS_ONLY_CHILD" jdbcType="VARCHAR" property="isOnlyChild"/>
        <result column="SITUATION" jdbcType="VARCHAR" property="situation"/>
    </resultMap>

    <!-- 学生基础列 -->
    <sql id="Student_Column_List">
        s.ID, s.NAME_ZH, s.NAME_EN, s.STUDENT_CODE, s.GENDER, s.BIRTH_DATE, s.DEPARTMENT_NAME, s.DEPARTMENT_CODE,
        s.CLASS_ID, s.ADMISSION_DATE, s.CARD_TYPE, s.CARD_NUM, s.CARD_NAME_ZH, s.CARD_NAME_EN, s.PHONE_NUM,
        s.IS_PAID, s.STATUS, s.IS_EXAM, s.IS_TRANSFERRED, s.TRANSFER_IN_DATE, s.MIDDLE_SCHOOL_NAME, s.IS_BOARDING,
        s.EMAIL, s.PARENT_EMAIL, s.CONTACT_INFO, s.DESCRIPTION, s.CREATED_AT, s.CREATED_BY, s.UPDATED_AT, s.UPDATED_BY,
        s.ETHNICITY, s.HEIGHT, s.WEIGHT, s.BLOOD_TYPE, s.BIRTH_PLACE, s.CLOTHING_SIZE, s.SIZE_SITUATION,
        s.BIRTH_ADDRESS, s.HOUSEHOLD_ADDRESS, s.HOME_ADDRESS, s.IS_ONLY_CHILD, s.SITUATION
    </sql>

    <!--根据科目选择条件查询学生信息-->
    <select id="getStudentBySubjectSelection" resultMap="StudentResultMap">
        SELECT DISTINCT
        <include refid="Student_Column_List"/>
        FROM t_edu_student s
        INNER JOIN t_edu_student_subject ss ON s.ID = ss.STUDENT_ID
        <where>
            <!-- 部门编码条件 -->
            <if test="seatVO.departmentCode != null and seatVO.departmentCode != ''">
                AND s.DEPARTMENT_CODE = #{seatVO.departmentCode}
            </if>
            <!-- 科目选择条件 -->
            <if test="seatVO.subjectSelection != null and seatVO.subjectSelection.size() > 0">
                AND (
                <foreach collection="seatVO.subjectSelection" item="selection" separator=" OR ">
                    (
                        ss.SUBJECT_CODE = #{selection.subjectCode}
                        <if test="selection.subjectDetail != null and selection.subjectDetail.size() > 0">
                            AND (
                            <foreach collection="selection.subjectDetail" item="detail" separator=" OR ">
                                (
                                    <!-- 如果有subjectType，则必须匹配 -->
                                    <if test="detail.subjectType != null and detail.subjectType != ''">
                                        ss.SUBJECT_TYPE = #{detail.subjectType}
                                        <if test="detail.subjectLevel != null and detail.subjectLevel != ''">
                                            AND ss.SUBJECT_LEVEL = #{detail.subjectLevel}
                                        </if>
                                    </if>
                                    <!-- 如果没有subjectType但有subjectLevel，则只匹配subjectLevel -->
                                    <if test="(detail.subjectType == null or detail.subjectType == '') and detail.subjectLevel != null and detail.subjectLevel != ''">
                                        ss.SUBJECT_LEVEL = #{detail.subjectLevel}
                                    </if>
                                )
                            </foreach>
                            )
                        </if>
                    )
                </foreach>
                )
            </if>
        </where>
        ORDER BY s.STUDENT_CODE ASC
    </select>

</mapper>