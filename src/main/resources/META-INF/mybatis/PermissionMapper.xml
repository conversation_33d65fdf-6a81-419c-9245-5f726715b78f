<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.PermissionMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.vo.PermissionVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="MENU_ID" jdbcType="VARCHAR" property="menuId"/>
        <result column="MENU_PARENT_ID" jdbcType="VARCHAR" property="menuParentId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="URL" jdbcType="VARCHAR" property="url"/>
        <result column="METHOD" jdbcType="VARCHAR" property="method"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        ID
        , MENU_ID, MENU_PARENT_ID, NAME, CODE, TYPE, URL, METHOD, STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!-- 根据ID查询权限信息 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_permission
        WHERE ID = #{id}
    </select>

    <!--查询权限编码是否存在-->
    <select id="existsByCode" parameterType="java.lang.String" resultType="java.lang.Boolean">
        select count(*) > 0
        from t_edu_permission t
        where t.CODE = #{code}
    </select>

    <!-- 查询权限列表 -->
    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_permission
        <where>
            <if test="name != null and name != ''">
                AND NAME LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND CODE = #{code}
            </if>
            <if test="menuId != null and menuId != ''">
                AND MENU_ID = #{menuId}
            </if>
            <if test="type != null">
                AND TYPE = #{type}
            </if>
            <if test="status != null">
                AND STATUS = #{status}
            </if>
        </where>
    </select>

    <!-- 根据菜单ID查询权限列表 -->
    <select id="queryByMenuId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_permission
        WHERE MENU_ID = #{menuId} AND STATUS = 1
    </select>

    <!-- 根据用户ID查询权限列表 -->
    <select id="queryByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT p.*
        FROM t_edu_permission p
                 INNER JOIN t_edu_role_permission rp ON p.ID = rp.PERMISSION_ID
                 INNER JOIN t_edu_user_role ur ON rp.ROLE_ID = ur.ROLE_ID
        WHERE ur.USER_ID = #{userId}
          AND p.STATUS = 1
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="queryByRoleId" resultMap="BaseResultMap">
        SELECT p.*
        FROM t_edu_permission p
                 INNER JOIN t_edu_role_permission rp ON p.ID = rp.PERMISSION_ID
        WHERE rp.ROLE_ID = #{roleId}
          AND p.STATUS = 1
    </select>

    <!-- 根据用户ID查询按钮权限列表 -->
    <select id="queryButtonPermissionsByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT p.*
        FROM t_edu_permission p
                 INNER JOIN t_edu_role_permission rp ON p.ID = rp.PERMISSION_ID
                 INNER JOIN t_edu_user_role ur ON rp.ROLE_ID = ur.ROLE_ID
        WHERE ur.USER_ID = #{userId}
          AND p.TYPE = 2
          AND p.STATUS = 1
    </select>

    <!-- 新增权限 -->
    <insert id="insert" parameterType="com.edu.www.dto.PermissionDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_permission_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_permission(ID, MENU_ID, MENU_PARENT_ID, NAME, CODE, TYPE, URL, METHOD, STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
        VALUES (#{id}, #{menuId}, #{menuParentId}, #{name}, #{code}, #{type}, #{url}, #{method}, #{status}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy})
    </insert>

    <!-- 更新权限 -->
    <update id="update" parameterType="com.edu.www.dto.PermissionDTO">
        UPDATE t_edu_permission
        <set>
            <if test="menuId != null">MENU_ID = #{menuId},</if>
            <if test="menuParentId != null">MENU_PARENT_ID = #{menuParentId},</if>
            <if test="name != null">NAME = #{name},</if>
            <if test="code != null">CODE = #{code},</if>
            <if test="type != null">TYPE = #{type},</if>
            <if test="url != null">URL = #{url},</if>
            <if test="method != null">METHOD = #{method},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 删除权限 -->
    <delete id="delete">
        DELETE
        FROM t_edu_permission
        WHERE ID = #{id}
    </delete>
</mapper> 