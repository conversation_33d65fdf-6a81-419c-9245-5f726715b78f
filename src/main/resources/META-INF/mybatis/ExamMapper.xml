<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.ExamMapper">

    <!-- 考试信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.ExamVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="YEAR" jdbcType="VARCHAR" property="year"/>
        <result column="SEMESTER" jdbcType="VARCHAR" property="semester"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="GRADE_CODE" jdbcType="VARCHAR" property="gradeCode"/>
        <result column="CLASS_CODE" jdbcType="VARCHAR" property="classCode"/>
        <result column="CLASSROOM_CODE" jdbcType="VARCHAR" property="classroomCode"/>
        <result column="COMPOSITION_INFO" jdbcType="LONGVARCHAR" property="compositionInfo"/>
        <result column="TOTAL_DURATION" jdbcType="INTEGER" property="totalDuration"/>
        <result column="SEQ_DAY" jdbcType="INTEGER" property="seqDay"/>
        <result column="EXAM_DATE" jdbcType="DATE" property="examDate"/>
        <result column="WEEKDAY" jdbcType="VARCHAR" property="weekday"/>
        <result column="INVIGILATOR" jdbcType="VARCHAR" property="invigilator"/>
        <result column="INSPECTOR" jdbcType="LONGVARCHAR" property="inspector"/>
        <result column="EXAM_CANDIDATE" jdbcType="INTEGER" property="examCandidate"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, YEAR, SEMESTER, DEPARTMENT_CODE, TYPE, TITLE, GRADE_CODE, CLASS_CODE, CLASSROOM_CODE,
        COMPOSITION_INFO, TOTAL_DURATION, SEQ_DAY, EXAM_DATE, WEEKDAY, INVIGILATOR,
        INSPECTOR, EXAM_CANDIDATE, STATUS, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询考试信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_exam t
        WHERE t.ID = #{id}
    </select>

    <!--根据多个ID批量查询考试信息-->
    <select id="getByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_exam t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--查询考试信息-->
    <select id="query" parameterType="com.edu.www.dto.ExamDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_exam t
        <where>
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="type != null and type != ''">
                and t.TYPE = #{type}
            </if>
            <if test="title != null and title != ''">
                and t.TITLE like CONCAT('%', #{title}, '%')
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                and t.GRADE_CODE = #{gradeCode}
            </if>
            <if test="classCode != null and classCode != ''">
                and t.CLASS_CODE = #{classCode}
            </if>
            <if test="classroomCode != null and classroomCode != ''">
                and t.CLASSROOM_CODE = #{classroomCode}
            </if>
            <if test="totalDuration != null">
                and t.TOTAL_DURATION = #{totalDuration}
            </if>
            <if test="seqDay != null">
                and t.SEQ_DAY = #{seqDay}
            </if>
            <if test="examDate != null">
                and t.EXAM_DATE = #{examDate}
            </if>
            <if test="weekday != null and weekday != ''">
                and t.WEEKDAY = #{weekday}
            </if>
            <if test="invigilator != null and invigilator != ''">
                and t.INVIGILATOR like CONCAT('%', #{invigilator}, '%')
            </if>
            <if test="inspector != null and inspector != ''">
                and t.INSPECTOR like CONCAT('%"', #{inspector}, '"%')
            </if>
            <if test="status != null and status != ''">
                and t.STATUS = #{status}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        ORDER BY t.TYPE ASC, t.UPDATED_AT DESC
    </select>

    <!--新增考试信息-->
    <insert id="insert" parameterType="com.edu.www.dto.ExamDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_exam_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_exam (
        ID, YEAR, SEMESTER, DEPARTMENT_CODE, TYPE, TITLE, GRADE_CODE, CLASS_CODE, CLASSROOM_CODE,
        COMPOSITION_INFO, TOTAL_DURATION, SEQ_DAY, EXAM_DATE, WEEKDAY, INVIGILATOR,
        INSPECTOR, EXAM_CANDIDATE, STATUS, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{year}, #{semester}, #{departmentCode}, #{type}, #{title}, #{gradeCode}, #{classCode}, #{classroomCode},
        #{compositionInfo}, #{totalDuration}, #{seqDay}, #{examDate}, #{weekday}, #{invigilator},
        #{inspector}, #{examCandidate}, #{status}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改考试信息-->
    <update id="update" parameterType="com.edu.www.dto.ExamDTO">
        UPDATE t_edu_exam t
        <set>
            <if test="year != null and year != ''">
                t.YEAR = #{year},
            </if>
            <if test="semester != null and semester != ''">
                t.SEMESTER = #{semester},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="type != null and type != ''">
                t.TYPE = #{type},
            </if>
            <if test="title != null and title != ''">
                t.TITLE = #{title},
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                t.GRADE_CODE = #{gradeCode},
            </if>
            <if test="classCode != null and classCode != ''">
                t.CLASS_CODE = #{classCode},
            </if>
            <if test="classroomCode != null and classroomCode != ''">
                t.CLASSROOM_CODE = #{classroomCode},
            </if>
            <if test="compositionInfo != null">
                t.COMPOSITION_INFO = #{compositionInfo},
            </if>
            <if test="totalDuration != null">
                t.TOTAL_DURATION = #{totalDuration},
            </if>
            <if test="seqDay != null">
                t.SEQ_DAY = #{seqDay},
            </if>
            <if test="examDate != null">
                t.EXAM_DATE = #{examDate},
            </if>
            <if test="weekday != null and weekday != ''">
                t.WEEKDAY = #{weekday},
            </if>
            <if test="invigilator != null and invigilator != ''">
                t.INVIGILATOR = #{invigilator},
            </if>
            <if test="inspector != null and inspector != ''">
                t.INSPECTOR = #{inspector},
            </if>
            <if test="examCandidate != null">
                t.EXAM_CANDIDATE = #{examCandidate},
            </if>
            <if test="status != null and status != ''">
                t.STATUS = #{status},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除考试信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_exam t
        WHERE t.ID = #{id}
    </delete>

</mapper>