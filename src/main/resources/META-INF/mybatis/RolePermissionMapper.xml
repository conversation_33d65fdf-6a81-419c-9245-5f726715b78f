<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.RolePermissionMapper">
    <resultMap id="BaseResultMap" type="com.edu.www.dto.RolePermissionDTO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="ROLE_ID" jdbcType="VARCHAR" property="roleId"/>
        <result column="PERMISSION_ID" jdbcType="VARCHAR" property="permissionId"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        ID
        , ROLE_ID, PERMISSION_ID, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!-- 根据ID查询角色权限关联信息 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_role_permission
        WHERE ID = #{id}
    </select>

    <!-- 查询角色权限关联列表 -->
    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_role_permission
        <where>
            <if test="roleId != null and roleId != ''">
                AND ROLE_ID = #{roleId}
            </if>
            <if test="permissionId != null and permissionId != ''">
                AND PERMISSION_ID = #{permissionId}
            </if>
        </where>
        ORDER BY CREATED_AT DESC
    </select>

    <!-- 根据角色ID查询角色权限关联列表 -->
    <select id="queryByRoleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_role_permission
        WHERE ROLE_ID = #{roleId}
    </select>

    <!-- 根据权限ID查询角色权限关联列表 -->
    <select id="queryByPermissionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_role_permission
        WHERE PERMISSION_ID = #{permissionId}
    </select>

    <!-- 新增角色权限关联 -->
    <insert id="insert" parameterType="com.edu.www.dto.RolePermissionDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_role_permission_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_role_permission(ID, ROLE_ID, PERMISSION_ID, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
        VALUES (#{id}, #{roleId}, #{permissionId}, NOW(), #{createdBy}, NOW(), #{updatedBy})
    </insert>

    <!-- 批量新增角色权限关联 -->
    <insert id="batchInsert">
        INSERT INTO t_edu_role_permission(
        ID, ROLE_ID, PERMISSION_ID, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (CONCAT('t_edu_role_permission_', UUID()), #{item.roleId}, #{item.permissionId}, NOW(), #{item.createdBy},
            NOW(), #{item.updatedBy})
        </foreach>
    </insert>

    <!-- 更新角色权限关联 -->
    <update id="update" parameterType="com.edu.www.dto.RolePermissionDTO">
        UPDATE t_edu_role_permission
        <set>
            <if test="roleId != null">ROLE_ID = #{roleId},</if>
            <if test="permissionId != null">PERMISSION_ID = #{permissionId},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            UPDATED_AT = NOW()
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 删除角色权限关联 -->
    <delete id="delete">
        DELETE
        FROM t_edu_role_permission
        WHERE ID = #{id}
    </delete>

    <!-- 根据角色ID删除角色权限关联 -->
    <delete id="deleteByRoleId">
        DELETE
        FROM t_edu_role_permission
        WHERE ROLE_ID = #{roleId}
    </delete>

    <!-- 根据权限ID删除角色权限关联 -->
    <delete id="deleteByPermissionId">
        DELETE
        FROM t_edu_role_permission
        WHERE PERMISSION_ID = #{permissionId}
    </delete>
</mapper> 