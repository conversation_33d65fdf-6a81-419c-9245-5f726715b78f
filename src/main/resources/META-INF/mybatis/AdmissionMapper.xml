<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.AdmissionMapper">

    <!-- 招生信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.AdmissionVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="PHONE_NUM" jdbcType="VARCHAR" property="phoneNum"/>
        <result column="ADMISSION_GRADE_CODE" jdbcType="VARCHAR" property="admissionGradeCode"/>
        <result column="YEAR_SYSTEM_CODE" jdbcType="VARCHAR" property="yearSystemCode"/>
        <result column="IS_ENROLLED" jdbcType="VARCHAR" property="isEnrolled"/>
        <result column="INVITE_DESC" jdbcType="VARCHAR" property="inviteDescription"/>
        <result column="EXAM_INFO" jdbcType="VARCHAR" property="examInfo"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , NAME, PHONE_NUM, ADMISSION_GRADE_CODE, YEAR_SYSTEM_CODE, IS_ENROLLED, INVITE_DESC,
        EXAM_INFO, DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询招生信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_admission t
        WHERE t.ID = #{id}
    </select>

    <!--查询招生信息-->
    <select id="query" parameterType="com.edu.www.dto.AdmissionDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_admission t
        <where>
            <if test="name != null and name != ''">
                and t.NAME like CONCAT('%', #{name}, '%')
            </if>
            <if test="phoneNum != null and phoneNum != ''">
                and t.PHONE_NUM like CONCAT('%', #{phoneNum}, '%')
            </if>
            <if test="admissionGradeCode != null and admissionGradeCode != ''">
                and t.ADMISSION_GRADE_CODE = #{admissionGradeCode}
            </if>
            <if test="yearSystemCode != null and yearSystemCode != ''">
                and t.YEAR_SYSTEM_CODE = #{yearSystemCode}
            </if>
            <if test="isEnrolled != null and isEnrolled != ''">
                and t.IS_ENROLLED = #{isEnrolled}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.UPDATED_AT desc
    </select>

    <!--新增招生信息-->
    <insert id="insert" parameterType="com.edu.www.dto.AdmissionDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_admission_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_admission (
        ID, NAME, PHONE_NUM, ADMISSION_GRADE_CODE, YEAR_SYSTEM_CODE, IS_ENROLLED,
        INVITE_DESC, EXAM_INFO, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{name}, #{phoneNum}, #{admissionGradeCode}, #{yearSystemCode}, #{isEnrolled},
        #{inviteDescription}, #{examInfo}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>


    <!--修改招生信息-->
    <update id="update" parameterType="com.edu.www.dto.AdmissionDTO">
        UPDATE t_edu_admission t
        <set>
            <if test="name != null and name != ''">
                t.NAME = #{name},
            </if>
            <if test="phoneNum != null and phoneNum != ''">
                t.PHONE_NUM = #{phoneNum},
            </if>
            <if test="admissionGradeCode != null and admissionGradeCode != ''">
                t.ADMISSION_GRADE_CODE = #{admissionGradeCode},
            </if>
            <if test="yearSystemCode != null and yearSystemCode != ''">
                t.YEAR_SYSTEM_CODE = #{yearSystemCode},
            </if>
            <if test="isEnrolled != null and isEnrolled != ''">
                t.IS_ENROLLED = #{isEnrolled},
            </if>
            <if test="inviteDescription != null">
                t.INVITE_DESC = #{inviteDescription},
            </if>
            <if test="examInfo != null">
                t.EXAM_INFO = #{examInfo},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除招生信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_admission t
        WHERE t.ID = #{id}
    </delete>

</mapper> 