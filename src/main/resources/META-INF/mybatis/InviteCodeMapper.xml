<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.edu.www.mapper.InviteCodeMapper">

    <!-- 邀请码结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.InviteCodeVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="USER_ID" jdbcType="VARCHAR" property="userId"/>
        <result column="USED_TIME" jdbcType="TIMESTAMP" property="usedTime"/>
        <result column="IS_USED" jdbcType="VARCHAR" property="isUsed"/>
        <result column="EXPIRY_TIME" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID
        , CODE, USER_ID, USED_TIME, IS_USED, EXPIRY_TIME, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询邀请码-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_invite_code t
        WHERE t.ID = #{id}
    </select>

    <select id="getByCodeCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(1)
        FROM t_edu_invite_code t
        WHERE t.CODE = #{code}
          and t.IS_USED = '0'
    </select>

    <!--根据邀请码查询未被使用的邀请码-->
    <select id="getByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_invite_code t
        WHERE t.CODE = #{code}
        and t.IS_USED = '0'
    </select>

    <!--查询邀请码-->
    <select id="query" parameterType="com.edu.www.dto.InviteCodeDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_invite_code t
        <where>
            <if test="code != null and code != ''">
                and t.CODE like CONCAT('%', #{code}, '%')
            </if>
            <if test="userId != null and userId != ''">
                and t.USER_ID = #{userId}
            </if>
            <if test="isUsed != null and isUsed != ''">
                and t.IS_USED = #{isUsed}
            </if>
            <if test="description != null and description != ''">
                and t.DESCRIPTION like CONCAT('%', #{description}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                and t.CREATED_BY = #{createdBy}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and t.UPDATED_BY = #{updatedBy}
            </if>
        </where>
        order by t.UPDATED_AT desc
    </select>

    <!--新增邀请码-->
    <insert id="insert" parameterType="com.edu.www.dto.InviteCodeDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_invite_code_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_invite_code (
        ID, CODE, USER_ID, USED_TIME, IS_USED, EXPIRY_TIME, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
        VALUES (
        #{id}, #{code}, #{userId}, #{usedTime}, #{isUsed}, #{expiryTime}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改邀请码-->
    <update id="update" parameterType="com.edu.www.dto.InviteCodeDTO">
        UPDATE t_edu_invite_code t
        <set>
            <if test="code != null and code != ''">
                t.CODE = #{code},
            </if>
            <if test="userId != null and userId != ''">
                t.USER_ID = #{userId},
            </if>
            <if test="usedTime != null">
                t.USED_TIME = #{usedTime},
            </if>
            <if test="isUsed != null and isUsed != ''">
                t.IS_USED = #{isUsed},
            </if>
            <if test="expiryTime != null">
                t.EXPIRY_TIMEEXPIRY_TIME = #{expiryTime},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                t.UPDATED_BY = #{updatedBy},
            </if>
            t.UPDATED_AT = NOW()
        </set>
        WHERE t.ID = #{id}
    </update>


    <!--根据ID删除邀请码-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM t_edu_invite_code t
        WHERE t.ID = #{id}
    </delete>


    <!--批量删除邀请码-->
    <delete id="deleteByIds">
        DELETE
        FROM t_edu_invite_code t
        WHERE t.ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 