<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.PaymentRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.PaymentRecordVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId"/>
        <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType"/>
        <result column="PAYMENT_AMOUNT" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="PAYMENT_DATE" jdbcType="DATE" property="paymentDate"/>
        <result column="PAYMENT_METHOD" jdbcType="VARCHAR" property="paymentMethod"/>
        <result column="OA_PROCESS_ID" jdbcType="VARCHAR" property="oaProcessId"/>
        <result column="PAYMENT_STATUS" jdbcType="VARCHAR" property="paymentStatus"/>
        <result column="PAYMENT_VOUCHER" jdbcType="VARCHAR" property="paymentVoucher"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, CONTRACT_ID, PAYMENT_TYPE, PAYMENT_AMOUNT, PAYMENT_DATE, PAYMENT_METHOD,
        OA_PROCESS_ID, PAYMENT_STATUS, PAYMENT_VOUCHER, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询支付记录信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_payment_record t
        WHERE t.ID = #{id}
    </select>

    <!--查询支付记录信息-->
    <select id="query" parameterType="com.edu.www.dto.PaymentRecordDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_payment_record t
        <where>
            <if test="contractId != null and contractId != ''">
                and t.CONTRACT_ID = #{contractId}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and t.PAYMENT_TYPE = #{paymentType}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and t.PAYMENT_METHOD = #{paymentMethod}
            </if>
            <if test="paymentStatus != null and paymentStatus != ''">
                and t.PAYMENT_STATUS = #{paymentStatus}
            </if>
            <if test="oaProcessId != null and oaProcessId != ''">
                and t.OA_PROCESS_ID = #{oaProcessId}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增支付记录信息-->
    <insert id="insert" parameterType="com.edu.www.dto.PaymentRecordDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_payment_record_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_payment_record (
        ID, CONTRACT_ID, PAYMENT_TYPE, PAYMENT_AMOUNT, PAYMENT_DATE, PAYMENT_METHOD,
        OA_PROCESS_ID, PAYMENT_STATUS, PAYMENT_VOUCHER, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{contractId}, #{paymentType}, #{paymentAmount}, #{paymentDate}, #{paymentMethod},
        #{oaProcessId}, #{paymentStatus}, #{paymentVoucher}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改支付记录信息-->
    <update id="update" parameterType="com.edu.www.dto.PaymentRecordDTO">
        UPDATE t_edu_payment_record t
        <set>
            <if test="contractId != null and contractId != ''">
                t.CONTRACT_ID = #{contractId},
            </if>
            <if test="paymentType != null and paymentType != ''">
                t.PAYMENT_TYPE = #{paymentType},
            </if>
            <if test="paymentAmount != null">
                t.PAYMENT_AMOUNT = #{paymentAmount},
            </if>
            <if test="paymentDate != null">
                t.PAYMENT_DATE = #{paymentDate},
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                t.PAYMENT_METHOD = #{paymentMethod},
            </if>
            <if test="oaProcessId != null and oaProcessId != ''">
                t.OA_PROCESS_ID = #{oaProcessId},
            </if>
            <if test="paymentStatus != null and paymentStatus != ''">
                t.PAYMENT_STATUS = #{paymentStatus},
            </if>
            <if test="paymentVoucher != null and paymentVoucher != ''">
                t.PAYMENT_VOUCHER = #{paymentVoucher},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除支付记录信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_payment_record WHERE ID = #{id}
    </delete>

</mapper>
