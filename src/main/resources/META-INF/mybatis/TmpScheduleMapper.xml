<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.TmpScheduleMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.TmpScheduleVO">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="YEAR" property="year" jdbcType="VARCHAR"/>
        <result column="SEMESTER" property="semester" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT" property="department" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="GRADE_CODE" property="gradeCode" jdbcType="VARCHAR"/>
        <result column="CLASS_CODE" property="classCode" jdbcType="VARCHAR"/>
        <result column="POSITION" property="position" jdbcType="VARCHAR"/>
        <result column="CONTENT" property="content" jdbcType="VARCHAR"/>
        <result column="START_DATE" property="startDate" jdbcType="DATE"/>
        <result column="END_DATE" property="endDate" jdbcType="DATE"/>
        <result column="COLOR" property="color" jdbcType="VARCHAR"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="CREATED_AT" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_AT" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, YEAR, SEMESTER, DEPARTMENT, DEPARTMENT_CODE, GRADE_CODE, CLASS_CODE, 
        POSITION, CONTENT, START_DATE, END_DATE, COLOR,
        DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!-- 根据ID查询临时课程表信息 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_tmp_schedule
        WHERE ID = #{id}
    </select>

    <!-- 查询临时课程表信息 -->
    <select id="query" resultMap="BaseResultMap" parameterType="com.edu.www.dto.TmpScheduleDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_tmp_schedule
        <where>
            <if test="id != null and id != ''">
                AND ID = #{id}
            </if>
            <if test="year != null and year != ''">
                AND YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                AND SEMESTER = #{semester}
            </if>
            <if test="department != null and department != ''">
                AND DEPARTMENT = #{department}
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                AND DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                AND GRADE_CODE = #{gradeCode}
            </if>
            <if test="classCode != null and classCode != ''">
                AND CLASS_CODE = #{classCode}
            </if>
            <if test="position != null and position != ''">
                AND POSITION = #{position}
            </if>
            <if test="startDate != null">
                AND DATE(START_DATE) = DATE(#{startDate})
            </if>
            <if test="endDate != null">
                AND DATE(END_DATE) = DATE(#{endDate})
            </if>
        </where>
        ORDER BY CREATED_AT DESC
    </select>

    <!-- 查询临时课程表信息数量 -->
    <select id="count" parameterType="com.edu.www.dto.TmpScheduleDTO" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_edu_tmp_schedule
        <where>
            <if test="id != null and id != ''">
                AND ID = #{id}
            </if>
            <if test="year != null and year != ''">
                AND YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                AND SEMESTER = #{semester}
            </if>
            <if test="department != null and department != ''">
                AND DEPARTMENT = #{department}
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                AND DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="gradeCode != null and gradeCode != ''">
                AND GRADE_CODE = #{gradeCode}
            </if>
            <if test="classCode != null and classCode != ''">
                AND CLASS_CODE = #{classCode}
            </if>
            <if test="position != null and position != ''">
                AND POSITION = #{position}
            </if>
            <if test="startDate != null">
                AND DATE(START_DATE) = DATE(#{startDate})
            </if>
            <if test="endDate != null">
                AND DATE(END_DATE) = DATE(#{endDate})
            </if>
        </where>
    </select>

    <!-- 新增临时课程表信息 -->
    <insert id="insert" parameterType="com.edu.www.dto.TmpScheduleDTO" useGeneratedKeys="false">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_tmp_schedule_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_tmp_schedule (
            ID, YEAR, SEMESTER, DEPARTMENT, DEPARTMENT_CODE, GRADE_CODE, CLASS_CODE, 
            POSITION, CONTENT, START_DATE, END_DATE, COLOR,
            DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        ) VALUES (
            #{id}, #{year}, #{semester}, #{department}, #{departmentCode}, #{gradeCode}, #{classCode}, 
            #{position}, #{content}, #{startDate}, #{endDate}, #{color},
            #{description}, NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>
    
    <!-- 批量新增临时课程表信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_edu_tmp_schedule (
            ID, YEAR, SEMESTER, DEPARTMENT, DEPARTMENT_CODE, GRADE_CODE, CLASS_CODE, 
            POSITION, CONTENT, START_DATE, END_DATE, COLOR,
            DESCRIPTION, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
        (
            CONCAT('t_edu_tmp_schedule_', UUID()), #{item.year}, #{item.semester}, #{item.department}, 
            #{item.departmentCode}, #{item.gradeCode}, #{item.classCode}, #{item.position}, 
            #{item.content}, #{item.startDate}, #{item.endDate}, #{item.color}, #{item.description}, NOW(),
            #{item.createdBy}, NOW(), #{item.updatedBy}
        )
        </foreach>
    </insert>

    <!-- 修改临时课程表信息 -->
    <update id="update" parameterType="com.edu.www.dto.TmpScheduleDTO">
        UPDATE t_edu_tmp_schedule
        <set>
            <if test="year != null">YEAR = #{year},</if>
            <if test="semester != null">SEMESTER = #{semester},</if>
            <if test="department != null">DEPARTMENT = #{department},</if>
            <if test="departmentCode != null">DEPARTMENT_CODE = #{departmentCode},</if>
            <if test="gradeCode != null">GRADE_CODE = #{gradeCode},</if>
            <if test="classCode != null">CLASS_CODE = #{classCode},</if>
            <if test="position != null">POSITION = #{position},</if>
            <if test="content != null">CONTENT = #{content},</if>
            <if test="startDate != null">START_DATE = #{startDate},</if>
            <if test="endDate != null">END_DATE = #{endDate},</if>
            <if test="color != null">COLOR = #{color},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            UPDATED_AT = NOW(),
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 根据ID删除临时课程表信息 -->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_tmp_schedule WHERE ID = #{id}
    </delete>
</mapper> 