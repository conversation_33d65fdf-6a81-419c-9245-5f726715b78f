<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.www.mapper.BookBorrowingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.edu.www.vo.BookBorrowingVO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DEPARTMENT" jdbcType="VARCHAR" property="department"/>
        <result column="DEPARTMENT_CODE" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="YEAR" jdbcType="VARCHAR" property="year"/>
        <result column="SEMESTER" jdbcType="VARCHAR" property="semester"/>
        <result column="borrow_no" jdbcType="VARCHAR" property="borrowNo"/>
        <result column="BOOK_ID" jdbcType="VARCHAR" property="bookId"/>
        <result column="BORROWER_NAME" jdbcType="VARCHAR" property="borrowerName"/>
        <result column="BORROWER_TYPE" jdbcType="VARCHAR" property="borrowerType"/>
        <result column="BORROWER_ID" jdbcType="VARCHAR" property="borrowerId"/>
        <result column="CONTACT_PHONE" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="BORROW_QUANTITY" jdbcType="INTEGER" property="borrowQuantity"/>
        <result column="BORROW_DATE" jdbcType="DATE" property="borrowDate"/>
        <result column="IS_NEED_RETURN" jdbcType="VARCHAR" property="isNeedReturn"/>
        <result column="EXPECTED_RETURN_DATE" jdbcType="DATE" property="expectedReturnDate"/>
        <result column="ACTUAL_RETURN_DATE" jdbcType="DATE" property="actualReturnDate"/>
        <result column="BORROW_PURPOSE" jdbcType="VARCHAR" property="borrowPurpose"/>
        <result column="BORROW_REASON" jdbcType="VARCHAR" property="borrowReason"/>
        <result column="BORROW_STATUS" jdbcType="VARCHAR" property="borrowStatus"/>
        <result column="RETURN_CONDITION" jdbcType="VARCHAR" property="returnCondition"/>
        <result column="APPROVED_NAME" jdbcType="VARCHAR" property="approvedName"/>
        <result column="APPROVAL_DATE" jdbcType="DATE" property="approvalDate"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, borrow_no, BOOK_ID,
        BORROWER_NAME, BORROWER_TYPE, BORROWER_ID, CONTACT_PHONE, BORROW_QUANTITY,
        BORROW_DATE, IS_NEED_RETURN, EXPECTED_RETURN_DATE, ACTUAL_RETURN_DATE,
        BORROW_PURPOSE, BORROW_REASON, BORROW_STATUS, RETURN_CONDITION,
        APPROVED_NAME, APPROVAL_DATE, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    </sql>

    <!--根据ID查询书籍领用信息-->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_book_borrowing t
        WHERE t.ID = #{id}
    </select>

    <!--查询书籍领用信息-->
    <select id="query" parameterType="com.edu.www.dto.BookBorrowingDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_edu_book_borrowing t
        <where>
            <if test="department != null and department != ''">
                and t.DEPARTMENT like CONCAT('%', #{department}, '%')
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                and t.DEPARTMENT_CODE = #{departmentCode}
            </if>
            <if test="year != null and year != ''">
                and t.YEAR = #{year}
            </if>
            <if test="semester != null and semester != ''">
                and t.SEMESTER = #{semester}
            </if>
            <if test="borrowNo != null and borrowNo != ''">
                and t.borrow_no like CONCAT('%', #{borrowNo}, '%')
            </if>
            <if test="bookId != null and bookId != ''">
                and t.BOOK_ID = #{bookId}
            </if>
            <if test="borrowerName != null and borrowerName != ''">
                and t.BORROWER_NAME like CONCAT('%', #{borrowerName}, '%')
            </if>
            <if test="borrowerType != null and borrowerType != ''">
                and t.BORROWER_TYPE = #{borrowerType}
            </if>
            <if test="borrowerId != null and borrowerId != ''">
                and t.BORROWER_ID = #{borrowerId}
            </if>
            <if test="borrowPurpose != null and borrowPurpose != ''">
                and t.BORROW_PURPOSE = #{borrowPurpose}
            </if>
            <if test="borrowStatus != null and borrowStatus != ''">
                and t.BORROW_STATUS = #{borrowStatus}
            </if>
            <if test="returnCondition != null and returnCondition != ''">
                and t.RETURN_CONDITION = #{returnCondition}
            </if>
        </where>
        ORDER BY t.CREATED_AT DESC
    </select>

    <!--新增书籍领用信息-->
    <insert id="insert" parameterType="com.edu.www.dto.BookBorrowingDTO">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            <![CDATA[SELECT CONCAT('t_edu_book_borrowing_', UUID())]]>
        </selectKey>
        INSERT INTO t_edu_book_borrowing (
        ID, DEPARTMENT, DEPARTMENT_CODE, YEAR, SEMESTER, borrow_no, BOOK_ID,
        BORROWER_NAME, BORROWER_TYPE, BORROWER_ID, CONTACT_PHONE, BORROW_QUANTITY,
        BORROW_DATE, IS_NEED_RETURN, EXPECTED_RETURN_DATE, ACTUAL_RETURN_DATE,
        BORROW_PURPOSE, BORROW_REASON, BORROW_STATUS, RETURN_CONDITION,
        APPROVED_NAME, APPROVAL_DATE, DESCRIPTION,
        CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
        )
        VALUES (
        #{id}, #{department}, #{departmentCode}, #{year}, #{semester}, #{borrowNo}, #{bookId},
        #{borrowerName}, #{borrowerType}, #{borrowerId}, #{contactPhone}, #{borrowQuantity},
        #{borrowDate}, #{isNeedReturn}, #{expectedReturnDate}, #{actualReturnDate},
        #{borrowPurpose}, #{borrowReason}, #{borrowStatus}, #{returnCondition},
        #{approvedName}, #{approvalDate}, #{description},
        NOW(), #{createdBy}, NOW(), #{updatedBy}
        )
    </insert>

    <!--修改书籍领用信息-->
    <update id="update" parameterType="com.edu.www.dto.BookBorrowingDTO">
        UPDATE t_edu_book_borrowing t
        <set>
            <if test="department != null and department != ''">
                t.DEPARTMENT = #{department},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                t.DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="year != null and year != ''">
                t.YEAR = #{year},
            </if>
            <if test="semester != null and semester != ''">
                t.SEMESTER = #{semester},
            </if>
            <if test="borrowNo != null and borrowNo != ''">
                t.borrow_no = #{borrowNo},
            </if>
            <if test="bookId != null and bookId != ''">
                t.BOOK_ID = #{bookId},
            </if>
            <if test="borrowerName != null and borrowerName != ''">
                t.BORROWER_NAME = #{borrowerName},
            </if>
            <if test="borrowerType != null and borrowerType != ''">
                t.BORROWER_TYPE = #{borrowerType},
            </if>
            <if test="borrowerId != null and borrowerId != ''">
                t.BORROWER_ID = #{borrowerId},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                t.CONTACT_PHONE = #{contactPhone},
            </if>
            <if test="borrowQuantity != null">
                t.BORROW_QUANTITY = #{borrowQuantity},
            </if>
            <if test="borrowDate != null">
                t.BORROW_DATE = #{borrowDate},
            </if>
            <if test="isNeedReturn != null and isNeedReturn != ''">
                t.IS_NEED_RETURN = #{isNeedReturn},
            </if>
            <if test="expectedReturnDate != null">
                t.EXPECTED_RETURN_DATE = #{expectedReturnDate},
            </if>
            <if test="actualReturnDate != null">
                t.ACTUAL_RETURN_DATE = #{actualReturnDate},
            </if>
            <if test="borrowPurpose != null and borrowPurpose != ''">
                t.BORROW_PURPOSE = #{borrowPurpose},
            </if>
            <if test="borrowReason != null and borrowReason != ''">
                t.BORROW_REASON = #{borrowReason},
            </if>
            <if test="borrowStatus != null and borrowStatus != ''">
                t.BORROW_STATUS = #{borrowStatus},
            </if>
            <if test="returnCondition != null and returnCondition != ''">
                t.RETURN_CONDITION = #{returnCondition},
            </if>
            <if test="approvedName != null and approvedName != ''">
                t.APPROVED_NAME = #{approvedName},
            </if>
            <if test="approvalDate != null">
                t.APPROVAL_DATE = #{approvalDate},
            </if>
            <if test="description != null">
                t.DESCRIPTION = #{description},
            </if>
            t.UPDATED_AT = NOW(),
            t.UPDATED_BY = #{updatedBy}
        </set>
        WHERE t.ID = #{id}
    </update>

    <!--根据ID删除书籍领用信息-->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM t_edu_book_borrowing WHERE ID = #{id}
    </delete>

</mapper>
