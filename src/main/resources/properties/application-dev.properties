spring.application.name=edu
server.port=8081

# Mysql Config
# spring.datasource.url=***************************************************************************************************************************************
# spring.datasource.url=**********************************************************************************************************************************
spring.datasource.url=******************************************************************************************************************************
spring.datasource.username=ENC(D44IPa2rPcOV3YtQjsYBJA==)
spring.datasource.password=ENC(91uy2bZGTDeOJK2UgCOqx7QJQfQFH1Kj)
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hikari Config
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.connection-test-query=SELECT 1

# JPA Config
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# Redis Config
# spring.redis.host=*************
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=ENC(91uy2bZGTDeOJK2UgCOqx7QJQfQFH1Kj)
spring.redis.database=0
spring.redis.timeout=60000

# Spring Session Config
spring.session.store-type=redis
spring.session.redis.namespace=spring:session
spring.session.timeout=3600

# Log Config
logging.config=classpath:META-INF/spring/logback-spring.xml

# Other Config
spring.jackson.time-zone=Asia/Shanghai

# MyBatis Config
mybatis.mapper-locations=classpath:META-INF/mybatis/*.xml
mybatis.type-aliases-package=com.edu.www.dto

# Swagger Config
springfox.documentation.swagger-ui.enabled=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
springfox.documentation.swagger.v2.use-model-v3=true

# JWT
jwt.secret=ENC(FiGYydF2lzJf3hf/VoC3FooQEeFJb9PEALB077C4Lfz1iaZGTC18i+OijLiBEpLJ/g4/0ZkXT1M=)
jwt.expiration=86400

# MB API token
auth.token.mb=ENC(ceKIMipZcIs+iLRsgu27XqFGHHHeCWtz2xR5Dv9l8RUF+6U2ZIiiMaXGz5goxsThr9y625acReLxyiNkK5i5YI4S1KDFXPp78wtMUpVsur0=)

# Jasypt????
jasypt.encryptor.algorithm=PBEWithMD5AndDES
### MasterPassword My Name
jasypt.encryptor.password=QozcXesgJada8+
# ????????
jasypt.encryptor.string-output-type=base64
jasypt.encryptor.pool-size=1
jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator

# GitHub edu-file token
auth.token.edu.file=ENC(Mcn0xxyFTcMeFqdQVB/r58cf4N+rkja/9uYBHzkKScNHw4VAVwhwJAq0QeNw00bNSLEW8rD/2w8OVK5nKNxrHckjyUif2XmWWA1JrxEyn+lzbp1KQdySmkbeQt6z3w93heY2147HnMQ=)

# File upload cache directory
file.upload.cache.dir=./upload-cache

# File upload configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.file-size-threshold=1MB