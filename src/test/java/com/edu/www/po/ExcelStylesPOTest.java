package com.edu.www.po;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExcelStylesPO 测试类
 */
class ExcelStylesPOTest {

    private Workbook workbook;
    private CellStyle headerStyle;
    private CellStyle weekTitleStyle;
    private CellStyle dataStyle;
    private CellStyle dutyTypeStyle;

    @BeforeEach
    void setUp() {
        workbook = new XSSFWorkbook();
        headerStyle = workbook.createCellStyle();
        weekTitleStyle = workbook.createCellStyle();
        dataStyle = workbook.createCellStyle();
        dutyTypeStyle = workbook.createCellStyle();
    }

    @Test
    void testExcelStylesCreation() {
        // 测试正常创建
        ExcelStylesPO styles = new ExcelStylesPO(headerStyle, weekTitleStyle, dataStyle, dutyTypeStyle);
        
        assertNotNull(styles);
        assertEquals(headerStyle, styles.headerStyle());
        assertEquals(weekTitleStyle, styles.weekTitleStyle());
        assertEquals(dataStyle, styles.dataStyle());
        assertEquals(dutyTypeStyle, styles.dutyTypeStyle());
    }

    @Test
    void testExcelStylesBuilder() {
        // 测试构建器模式
        ExcelStylesPO styles = ExcelStylesPO.builder()
                .headerStyle(headerStyle)
                .weekTitleStyle(weekTitleStyle)
                .dataStyle(dataStyle)
                .dutyTypeStyle(dutyTypeStyle)
                .build();
        
        assertNotNull(styles);
        assertEquals(headerStyle, styles.headerStyle());
        assertEquals(weekTitleStyle, styles.weekTitleStyle());
        assertEquals(dataStyle, styles.dataStyle());
        assertEquals(dutyTypeStyle, styles.dutyTypeStyle());
    }

    @Test
    void testNullValidation() {
        // 测试null验证
        assertThrows(IllegalArgumentException.class, () -> 
            new ExcelStylesPO(null, weekTitleStyle, dataStyle, dutyTypeStyle));
        
        assertThrows(IllegalArgumentException.class, () -> 
            new ExcelStylesPO(headerStyle, null, dataStyle, dutyTypeStyle));
        
        assertThrows(IllegalArgumentException.class, () -> 
            new ExcelStylesPO(headerStyle, weekTitleStyle, null, dutyTypeStyle));
        
        assertThrows(IllegalArgumentException.class, () -> 
            new ExcelStylesPO(headerStyle, weekTitleStyle, dataStyle, null));
    }

    @Test
    void testBuilderNullValidation() {
        // 测试构建器的null验证
        assertThrows(IllegalArgumentException.class, () -> 
            ExcelStylesPO.builder()
                .headerStyle(null)
                .weekTitleStyle(weekTitleStyle)
                .dataStyle(dataStyle)
                .dutyTypeStyle(dutyTypeStyle)
                .build());
    }
}
